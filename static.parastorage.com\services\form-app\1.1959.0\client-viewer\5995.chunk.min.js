"use strict";(("undefined"!=typeof self?self:this).webpackJsonp__wix_form_app=("undefined"!=typeof self?self:this).webpackJsonp__wix_form_app||[]).push([[5995],{25995:(e,t,n)=>{n.d(t,{K:()=>h});var o=n(64660);
/*!
* focus-trap 7.6.2
* @license MIT, https://github.com/focus-trap/focus-trap/blob/master/LICENSE
*/function r(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}function a(e,t,n){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=typeof o)return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function i(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function u(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?i(Object(n),!0).forEach((function(t){a(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):i(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function c(e){return function(e){if(Array.isArray(e))return r(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return r(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?r(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var s=function(e,t){if(e.length>0){var n=e[e.length-1];n!==t&&n.pause()}var o=e.indexOf(t);-1===o||e.splice(o,1),e.push(t)},l=function(e,t){var n=e.indexOf(t);-1!==n&&e.splice(n,1),e.length>0&&e[e.length-1].unpause()},d=function(e){return"Tab"===(null==e?void 0:e.key)||9===(null==e?void 0:e.keyCode)},f=function(e){return d(e)&&!e.shiftKey},b=function(e){return d(e)&&e.shiftKey},v=function(e){return setTimeout(e,0)},p=function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),o=1;o<t;o++)n[o-1]=arguments[o];return"function"==typeof e?e.apply(void 0,n):e},m=function(e){return e.target.shadowRoot&&"function"==typeof e.composedPath?e.composedPath()[0]:e.target},y=[],h=function(e,t){var n,r=(null==t?void 0:t.document)||document,a=(null==t?void 0:t.trapStack)||y,i=u({returnFocusOnDeactivate:!0,escapeDeactivates:!0,delayInitialFocus:!0,isKeyForward:f,isKeyBackward:b},t),h={containers:[],containerGroups:[],tabbableGroups:[],nodeFocusedBeforeActivation:null,mostRecentlyFocusedNode:null,active:!1,paused:!1,delayInitialFocusTimer:void 0,recentNavEvent:void 0},w=function(e,t,n){return e&&void 0!==e[t]?e[t]:i[n||t]},g=function(e,t){var n="function"==typeof(null==t?void 0:t.composedPath)?t.composedPath():void 0;return h.containerGroups.findIndex((function(t){var o=t.container,r=t.tabbableNodes;return o.contains(e)||(null==n?void 0:n.includes(o))||r.find((function(t){return t===e}))}))},O=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.hasFallback,o=void 0!==n&&n,a=t.params,u=void 0===a?[]:a,s=i[e];if("function"==typeof s&&(s=s.apply(void 0,c(u))),!0===s&&(s=void 0),!s){if(void 0===s||!1===s)return s;throw new Error("`".concat(e,"` was specified but was not a node, or did not return a node"))}var l=s;if("string"==typeof s){try{l=r.querySelector(s)}catch(t){throw new Error("`".concat(e,'` appears to be an invalid selector; error="').concat(t.message,'"'))}if(!l&&!o)throw new Error("`".concat(e,"` as selector refers to no known node"))}return l},N=function(){var e=O("initialFocus",{hasFallback:!0});if(!1===e)return!1;if(void 0===e||e&&!(0,o.tp)(e,i.tabbableOptions))if(g(r.activeElement)>=0)e=r.activeElement;else{var t=h.tabbableGroups[0];e=t&&t.firstTabbableNode||O("fallbackFocus")}else null===e&&(e=O("fallbackFocus"));if(!e)throw new Error("Your focus-trap needs to have at least one focusable element");return e},F=function(){if(h.containerGroups=h.containers.map((function(e){var t=(0,o.Kr)(e,i.tabbableOptions),n=(0,o.nq)(e,i.tabbableOptions),r=t.length>0?t[0]:void 0,a=t.length>0?t[t.length-1]:void 0,u=n.find((function(e){return(0,o.AO)(e)})),c=n.slice().reverse().find((function(e){return(0,o.AO)(e)})),s=!!t.find((function(e){return(0,o.yT)(e)>0}));return{container:e,tabbableNodes:t,focusableNodes:n,posTabIndexesFound:s,firstTabbableNode:r,lastTabbableNode:a,firstDomTabbableNode:u,lastDomTabbableNode:c,nextTabbableNode:function(e){var r=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],a=t.indexOf(e);return a<0?r?n.slice(n.indexOf(e)+1).find((function(e){return(0,o.AO)(e)})):n.slice(0,n.indexOf(e)).reverse().find((function(e){return(0,o.AO)(e)})):t[a+(r?1:-1)]}}})),h.tabbableGroups=h.containerGroups.filter((function(e){return e.tabbableNodes.length>0})),h.tabbableGroups.length<=0&&!O("fallbackFocus"))throw new Error("Your focus-trap must have at least one container with at least one tabbable node in it at all times");if(h.containerGroups.find((function(e){return e.posTabIndexesFound}))&&h.containerGroups.length>1)throw new Error("At least one node with a positive tabindex was found in one of your focus-trap's multiple containers. Positive tabindexes are only supported in single-container focus-traps.")},k=function(e){var t=e.activeElement;if(t)return t.shadowRoot&&null!==t.shadowRoot.activeElement?k(t.shadowRoot):t},E=function(e){!1!==e&&e!==k(document)&&(e&&e.focus?(e.focus({preventScroll:!!i.preventScroll}),h.mostRecentlyFocusedNode=e,function(e){return e.tagName&&"input"===e.tagName.toLowerCase()&&"function"==typeof e.select}(e)&&e.select()):E(N()))},T=function(e){var t=O("setReturnFocus",{params:[e]});return t||!1!==t&&e},D=function(e){var t=e.target,n=e.event,r=e.isBackward,a=void 0!==r&&r;t=t||m(n),F();var u=null;if(h.tabbableGroups.length>0){var c=g(t,n),s=c>=0?h.containerGroups[c]:void 0;if(c<0)u=a?h.tabbableGroups[h.tabbableGroups.length-1].lastTabbableNode:h.tabbableGroups[0].firstTabbableNode;else if(a){var l=h.tabbableGroups.findIndex((function(e){var n=e.firstTabbableNode;return t===n}));if(l<0&&(s.container===t||(0,o.tp)(t,i.tabbableOptions)&&!(0,o.AO)(t,i.tabbableOptions)&&!s.nextTabbableNode(t,!1))&&(l=c),l>=0){var f=0===l?h.tabbableGroups.length-1:l-1,b=h.tabbableGroups[f];u=(0,o.yT)(t)>=0?b.lastTabbableNode:b.lastDomTabbableNode}else d(n)||(u=s.nextTabbableNode(t,!1))}else{var v=h.tabbableGroups.findIndex((function(e){var n=e.lastTabbableNode;return t===n}));if(v<0&&(s.container===t||(0,o.tp)(t,i.tabbableOptions)&&!(0,o.AO)(t,i.tabbableOptions)&&!s.nextTabbableNode(t))&&(v=c),v>=0){var p=v===h.tabbableGroups.length-1?0:v+1,y=h.tabbableGroups[p];u=(0,o.yT)(t)>=0?y.firstTabbableNode:y.firstDomTabbableNode}else d(n)||(u=s.nextTabbableNode(t))}}else u=O("fallbackFocus");return u},P=function(e){var t=m(e);g(t,e)>=0||(p(i.clickOutsideDeactivates,e)?n.deactivate({returnFocus:i.returnFocusOnDeactivate}):p(i.allowOutsideClick,e)||e.preventDefault())},G=function(e){var t=m(e),n=g(t,e)>=0;if(n||t instanceof Document)n&&(h.mostRecentlyFocusedNode=t);else{var r;e.stopImmediatePropagation();var a=!0;if(h.mostRecentlyFocusedNode)if((0,o.yT)(h.mostRecentlyFocusedNode)>0){var u=g(h.mostRecentlyFocusedNode),c=h.containerGroups[u].tabbableNodes;if(c.length>0){var s=c.findIndex((function(e){return e===h.mostRecentlyFocusedNode}));s>=0&&(i.isKeyForward(h.recentNavEvent)?s+1<c.length&&(r=c[s+1],a=!1):s-1>=0&&(r=c[s-1],a=!1))}}else h.containerGroups.some((function(e){return e.tabbableNodes.some((function(e){return(0,o.yT)(e)>0}))}))||(a=!1);else a=!1;a&&(r=D({target:h.mostRecentlyFocusedNode,isBackward:i.isKeyBackward(h.recentNavEvent)})),E(r||(h.mostRecentlyFocusedNode||N()))}h.recentNavEvent=void 0},A=function(e){(i.isKeyForward(e)||i.isKeyBackward(e))&&function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];h.recentNavEvent=e;var n=D({event:e,isBackward:t});n&&(d(e)&&e.preventDefault(),E(n))}(e,i.isKeyBackward(e))},x=function(e){var t;"Escape"!==(null==(t=e)?void 0:t.key)&&"Esc"!==(null==t?void 0:t.key)&&27!==(null==t?void 0:t.keyCode)||!1===p(i.escapeDeactivates,e)||(e.preventDefault(),n.deactivate())},R=function(e){var t=m(e);g(t,e)>=0||p(i.clickOutsideDeactivates,e)||p(i.allowOutsideClick,e)||(e.preventDefault(),e.stopImmediatePropagation())},I=function(){if(h.active)return s(a,n),h.delayInitialFocusTimer=i.delayInitialFocus?v((function(){E(N())})):E(N()),r.addEventListener("focusin",G,!0),r.addEventListener("mousedown",P,{capture:!0,passive:!1}),r.addEventListener("touchstart",P,{capture:!0,passive:!1}),r.addEventListener("click",R,{capture:!0,passive:!1}),r.addEventListener("keydown",A,{capture:!0,passive:!1}),r.addEventListener("keydown",x),n},j=function(){if(h.active)return r.removeEventListener("focusin",G,!0),r.removeEventListener("mousedown",P,!0),r.removeEventListener("touchstart",P,!0),r.removeEventListener("click",R,!0),r.removeEventListener("keydown",A,!0),r.removeEventListener("keydown",x),n},L="undefined"!=typeof window&&"MutationObserver"in window?new MutationObserver((function(e){e.some((function(e){return Array.from(e.removedNodes).some((function(e){return e===h.mostRecentlyFocusedNode}))}))&&E(N())})):void 0,S=function(){L&&(L.disconnect(),h.active&&!h.paused&&h.containers.map((function(e){L.observe(e,{subtree:!0,childList:!0})})))};return(n={get active(){return h.active},get paused(){return h.paused},activate:function(e){if(h.active)return this;var t=w(e,"onActivate"),n=w(e,"onPostActivate"),o=w(e,"checkCanFocusTrap");o||F(),h.active=!0,h.paused=!1,h.nodeFocusedBeforeActivation=r.activeElement,null==t||t();var a=function(){o&&F(),I(),S(),null==n||n()};return o?(o(h.containers.concat()).then(a,a),this):(a(),this)},deactivate:function(e){if(!h.active)return this;var t=u({onDeactivate:i.onDeactivate,onPostDeactivate:i.onPostDeactivate,checkCanReturnFocus:i.checkCanReturnFocus},e);clearTimeout(h.delayInitialFocusTimer),h.delayInitialFocusTimer=void 0,j(),h.active=!1,h.paused=!1,S(),l(a,n);var o=w(t,"onDeactivate"),r=w(t,"onPostDeactivate"),c=w(t,"checkCanReturnFocus"),s=w(t,"returnFocus","returnFocusOnDeactivate");null==o||o();var d=function(){v((function(){s&&E(T(h.nodeFocusedBeforeActivation)),null==r||r()}))};return s&&c?(c(T(h.nodeFocusedBeforeActivation)).then(d,d),this):(d(),this)},pause:function(e){if(h.paused||!h.active)return this;var t=w(e,"onPause"),n=w(e,"onPostPause");return h.paused=!0,null==t||t(),j(),S(),null==n||n(),this},unpause:function(e){if(!h.paused||!h.active)return this;var t=w(e,"onUnpause"),n=w(e,"onPostUnpause");return h.paused=!1,null==t||t(),F(),I(),S(),null==n||n(),this},updateContainerElements:function(e){var t=[].concat(e).filter(Boolean);return h.containers=t.map((function(e){return"string"==typeof e?r.querySelector(e):e})),h.active&&F(),S(),this}}).updateContainerElements(e),n}}}]);
//# sourceMappingURL=5995.chunk.min.js.map