!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(require("react")):"function"==typeof define&&define.amd?define("rb_wixui.thunderbolt[Column_DesignedColumn]",["react"],t):"object"==typeof exports?exports["rb_wixui.thunderbolt[Column_DesignedColumn]"]=t(require("react")):e["rb_wixui.thunderbolt[Column_DesignedColumn]"]=t(e.React)}("undefined"!=typeof self?self:this,(function(e){return function(){var t={5329:function(t){"use strict";t.exports=e},448:function(e){function t(){return e.exports=t=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var a in i)({}).hasOwnProperty.call(i,a)&&(e[a]=i[a])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,t.apply(null,arguments)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports}},i={};function a(e){var n=i[e];if(void 0!==n)return n.exports;var r=i[e]={exports:{}};return t[e](r,r.exports,a),r.exports}a.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return a.d(t,{a:t}),t},a.d=function(e,t){for(var i in t)a.o(t,i)&&!a.o(e,i)&&Object.defineProperty(e,i,{enumerable:!0,get:t[i]})},a.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},a.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};return function(){"use strict";a.r(n),a.d(n,{components:function(){return Qt}});var e={};a.r(e),a.d(e,{STATIC_MEDIA_URL:function(){return nt},ph:function(){return tt}});var t=a(448),i=a.n(t),r=a(5329),o=a.n(r),c="CJOjs7";function s(e){var t,i,a="";if("string"==typeof e||"number"==typeof e)a+=e;else if("object"==typeof e)if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(i=s(e[t]))&&(a&&(a+=" "),a+=i);else for(t in e)e[t]&&(a&&(a+=" "),a+=t);return a}var l=function(){for(var e,t,i=0,a="";i<arguments.length;)(e=arguments[i++])&&(t=s(e))&&(a&&(a+=" "),a+=t);return a};const d="wixui-",h=(e,...t)=>{const i=[];return e&&i.push(`${d}${e}`),t.forEach((e=>{e&&(i.push(`${d}${e}`),i.push(e))})),i.join(" ")};const u=e=>Object.entries(e).reduce(((e,[t,i])=>(t.includes("data-")&&(e[t]=i),e)),{});var g={root:"repeater",repeaterItem:"repeater__item"};const m="mesh-container-content",_="inline-content",p=e=>o().createElement("div",{key:e.props.id+"-rotated-wrapper","data-mesh-id":e.props.id+"-rotated-wrapper"},e),f=(e,t)=>{const{id:a,className:n,wedges:r=[],rotatedComponents:c=[],children:s,fixedComponents:d=[],extraClassName:h="",renderRotatedComponents:g=p}=e,f=o().Children.toArray(s()),T=[],I=[];f.forEach((e=>d.includes(e.props.id)?T.push(e):I.push(e)));const E=(e=>{const{wedges:t,rotatedComponents:i,childrenArray:a,renderRotatedComponents:n}=e,r=i.reduce(((e,t)=>({...e,[t]:!0})),{});return[...a.map((e=>{return r[(t=e,t.props.id.split("__")[0])]?n(e):e;var t})),...t.map((e=>o().createElement("div",{key:e,"data-mesh-id":e})))]})({childrenArray:I,rotatedComponents:c,wedges:r,renderRotatedComponents:g});return o().createElement("div",i()({},u(e),{"data-mesh-id":a+"inlineContent","data-testid":_,className:l(n,h),ref:t}),o().createElement("div",{"data-mesh-id":a+"inlineContent-gridContainer","data-testid":m},E),T)};var T=o().forwardRef(f),I="jhxvbR";const E="v1",L=2,A=1920,b=1920,w=1e3,O=1e3,M={SCALE_TO_FILL:"fill",SCALE_TO_FIT:"fit",STRETCH:"stretch",ORIGINAL_SIZE:"original_size",TILE:"tile",TILE_HORIZONTAL:"tile_horizontal",TILE_VERTICAL:"tile_vertical",FIT_AND_TILE:"fit_and_tile",LEGACY_STRIP_TILE:"legacy_strip_tile",LEGACY_STRIP_TILE_HORIZONTAL:"legacy_strip_tile_horizontal",LEGACY_STRIP_TILE_VERTICAL:"legacy_strip_tile_vertical",LEGACY_STRIP_SCALE_TO_FILL:"legacy_strip_fill",LEGACY_STRIP_SCALE_TO_FIT:"legacy_strip_fit",LEGACY_STRIP_FIT_AND_TILE:"legacy_strip_fit_and_tile",LEGACY_STRIP_ORIGINAL_SIZE:"legacy_strip_original_size",LEGACY_ORIGINAL_SIZE:"actual_size",LEGACY_FIT_WIDTH:"fitWidth",LEGACY_FIT_HEIGHT:"fitHeight",LEGACY_FULL:"full",LEGACY_BG_FIT_AND_TILE:"legacy_tile",LEGACY_BG_FIT_AND_TILE_HORIZONTAL:"legacy_tile_horizontal",LEGACY_BG_FIT_AND_TILE_VERTICAL:"legacy_tile_vertical",LEGACY_BG_NORMAL:"legacy_normal"},R={FIT:"fit",FILL:"fill",FILL_FOCAL:"fill_focal",CROP:"crop",LEGACY_CROP:"legacy_crop",LEGACY_FILL:"legacy_fill"},y={CENTER:"center",TOP:"top",TOP_LEFT:"top_left",TOP_RIGHT:"top_right",BOTTOM:"bottom",BOTTOM_LEFT:"bottom_left",BOTTOM_RIGHT:"bottom_right",LEFT:"left",RIGHT:"right"},G={[y.CENTER]:{x:.5,y:.5},[y.TOP_LEFT]:{x:0,y:0},[y.TOP_RIGHT]:{x:1,y:0},[y.TOP]:{x:.5,y:0},[y.BOTTOM_LEFT]:{x:0,y:1},[y.BOTTOM_RIGHT]:{x:1,y:1},[y.BOTTOM]:{x:.5,y:1},[y.RIGHT]:{x:1,y:.5},[y.LEFT]:{x:0,y:.5}},C={center:"c",top:"t",top_left:"tl",top_right:"tr",bottom:"b",bottom_left:"bl",bottom_right:"br",left:"l",right:"r"},S={BG:"bg",IMG:"img",SVG:"svg"},N={AUTO:"auto",CLASSIC:"classic",SUPER:"super"},v={classic:1,super:2},F={radius:"0.66",amount:"1.00",threshold:"0.01"},P={uri:"",css:{img:{},container:{}},attr:{img:{},container:{}},transformed:!1},x=25e6,k=[1.5,2,4],H={HIGH:{size:196e4,quality:90,maxUpscale:1},MEDIUM:{size:36e4,quality:85,maxUpscale:1},LOW:{size:16e4,quality:80,maxUpscale:1.2},TINY:{size:0,quality:80,maxUpscale:1.4}},Y={HIGH:"HIGH",MEDIUM:"MEDIUM",LOW:"LOW",TINY:"TINY"},B={CONTRAST:"contrast",BRIGHTNESS:"brightness",SATURATION:"saturation",HUE:"hue",BLUR:"blur"},U={JPG:"jpg",JPEG:"jpeg",JPE:"jpe",PNG:"png",WEBP:"webp",WIX_ICO_MP:"wix_ico_mp",WIX_MP:"wix_mp",GIF:"gif",SVG:"svg",AVIF:"avif",UNRECOGNIZED:"unrecognized"},$={AVIF:"AVIF",PAVIF:"PAVIF"};U.JPG,U.JPEG,U.JPE,U.PNG,U.GIF,U.WEBP;function j(e,...t){return function(...i){const a=i[i.length-1]||{},n=[e[0]];return t.forEach((function(t,r){const o=Number.isInteger(t)?i[t]:a[t];n.push(o,e[r+1])})),n.join("")}}function z(e){return e[e.length-1]}const V=[U.PNG,U.JPEG,U.JPG,U.JPE,U.WIX_ICO_MP,U.WIX_MP,U.WEBP,U.AVIF],D=[U.JPEG,U.JPG,U.JPE];function W(e,t,i){return i&&t&&!(!(a=t.id)||!a.trim()||"none"===a.toLowerCase())&&Object.values(M).includes(e);var a}function Z(e,t,i){return function(e,t,i=!1){return!((J(e)||K(e))&&t&&!i)}(e,t,i)&&(function(e){return V.includes(ae(e))}(e)||function(e,t=!1){return X(e)&&t}(e,i))&&!/(^https?)|(^data)|(^\/\/)/.test(e)}function q(e){return ae(e)===U.PNG}function J(e){return ae(e)===U.WEBP}function X(e){return ae(e)===U.GIF}function K(e){return ae(e)===U.AVIF}const Q=["/","\\","?","<",">","|","\u201c",":",'"'].map(encodeURIComponent),ee=["\\.","\\*"],te="_";function ie(e){return function(e){return D.includes(ae(e))}(e)?U.JPG:q(e)?U.PNG:J(e)?U.WEBP:X(e)?U.GIF:K(e)?U.AVIF:U.UNRECOGNIZED}function ae(e){return(/[.]([^.]+)$/.exec(e)&&/[.]([^.]+)$/.exec(e)[1]||"").toLowerCase()}function ne(e,t,i,a,n){let r;return r=n===R.FILL?function(e,t,i,a){return Math.max(i/e,a/t)}(e,t,i,a):n===R.FIT?function(e,t,i,a){return Math.min(i/e,a/t)}(e,t,i,a):1,r}function re(e,t,i,a,n,r){e=e||a.width,t=t||a.height;const{scaleFactor:o,width:c,height:s}=function(e,t,i,a,n){let r,o=i,c=a;if(r=ne(e,t,i,a,n),n===R.FIT&&(o=e*r,c=t*r),o&&c&&o*c>x){const i=Math.sqrt(x/(o*c));o*=i,c*=i,r=ne(e,t,o,c,n)}return{scaleFactor:r,width:o,height:c}}(e,t,a.width*n,a.height*n,i);return function(e,t,i,a,n,r,o){const{optimizedScaleFactor:c,upscaleMethodValue:s,forceUSM:l}=function(e,t,i,a){if("auto"===a)return function(e,t){const i=le(e,t);return{optimizedScaleFactor:H[i].maxUpscale,upscaleMethodValue:v.classic,forceUSM:!1}}(e,t);if("super"===a)return function(e){return{optimizedScaleFactor:z(k),upscaleMethodValue:v.super,forceUSM:!(k.includes(e)||e>z(k))}}(i);return function(e,t){const i=le(e,t);return{optimizedScaleFactor:H[i].maxUpscale,upscaleMethodValue:v.classic,forceUSM:!1}}(e,t)}(e,t,r,n);let d=i,h=a;if(r<=c)return{width:d,height:h,scaleFactor:r,upscaleMethodValue:s,forceUSM:l,cssUpscaleNeeded:!1};switch(o){case R.FILL:d=i*(c/r),h=a*(c/r);break;case R.FIT:d=e*c,h=t*c}return{width:d,height:h,scaleFactor:c,upscaleMethodValue:s,forceUSM:l,cssUpscaleNeeded:!0}}(e,t,c,s,r,o,i)}function oe(e,t,i,a){const n=se(i)||function(e=y.CENTER){return G[e]}(a);return{x:Math.max(0,Math.min(e.width-t.width,n.x*e.width-t.width/2)),y:Math.max(0,Math.min(e.height-t.height,n.y*e.height-t.height/2)),width:Math.min(e.width,t.width),height:Math.min(e.height,t.height)}}function ce(e){return e.alignment&&C[e.alignment]||C[y.CENTER]}function se(e){let t;return!e||"number"!=typeof e.x||isNaN(e.x)||"number"!=typeof e.y||isNaN(e.y)||(t={x:de(Math.max(0,Math.min(100,e.x))/100,2),y:de(Math.max(0,Math.min(100,e.y))/100,2)}),t}function le(e,t){const i=e*t;return i>H[Y.HIGH].size?Y.HIGH:i>H[Y.MEDIUM].size?Y.MEDIUM:i>H[Y.LOW].size?Y.LOW:Y.TINY}function de(e,t){const i=Math.pow(10,t||0);return(e*i/i).toFixed(t)}function he(e){return e&&e.upscaleMethod&&N[e.upscaleMethod.toUpperCase()]||N.AUTO}function ue(e,t){const i=J(e)||K(e);return ae(e)===U.GIF||i&&t}const ge={isMobile:!1},me=function(e){return ge[e]};function _e(){if("undefined"!=typeof window&&"undefined"!=typeof navigator){const t=window.matchMedia&&window.matchMedia("(max-width: 767px)").matches,i=/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);e=t&&i,ge["isMobile"]=e}var e}function pe(e,t){const i={css:{container:{}}},{css:a}=i,{fittingType:n}=e;switch(n){case M.ORIGINAL_SIZE:case M.LEGACY_ORIGINAL_SIZE:case M.LEGACY_STRIP_ORIGINAL_SIZE:a.container.backgroundSize="auto",a.container.backgroundRepeat="no-repeat";break;case M.SCALE_TO_FIT:case M.LEGACY_STRIP_SCALE_TO_FIT:a.container.backgroundSize="contain",a.container.backgroundRepeat="no-repeat";break;case M.STRETCH:a.container.backgroundSize="100% 100%",a.container.backgroundRepeat="no-repeat";break;case M.SCALE_TO_FILL:case M.LEGACY_STRIP_SCALE_TO_FILL:a.container.backgroundSize="cover",a.container.backgroundRepeat="no-repeat";break;case M.TILE_HORIZONTAL:case M.LEGACY_STRIP_TILE_HORIZONTAL:a.container.backgroundSize="auto",a.container.backgroundRepeat="repeat-x";break;case M.TILE_VERTICAL:case M.LEGACY_STRIP_TILE_VERTICAL:a.container.backgroundSize="auto",a.container.backgroundRepeat="repeat-y";break;case M.TILE:case M.LEGACY_STRIP_TILE:a.container.backgroundSize="auto",a.container.backgroundRepeat="repeat";break;case M.LEGACY_STRIP_FIT_AND_TILE:a.container.backgroundSize="contain",a.container.backgroundRepeat="repeat";break;case M.FIT_AND_TILE:case M.LEGACY_BG_FIT_AND_TILE:a.container.backgroundSize="auto",a.container.backgroundRepeat="repeat";break;case M.LEGACY_BG_FIT_AND_TILE_HORIZONTAL:a.container.backgroundSize="auto",a.container.backgroundRepeat="repeat-x";break;case M.LEGACY_BG_FIT_AND_TILE_VERTICAL:a.container.backgroundSize="auto",a.container.backgroundRepeat="repeat-y";break;case M.LEGACY_BG_NORMAL:a.container.backgroundSize="auto",a.container.backgroundRepeat="no-repeat"}switch(t.alignment){case y.CENTER:a.container.backgroundPosition="center center";break;case y.LEFT:a.container.backgroundPosition="left center";break;case y.RIGHT:a.container.backgroundPosition="right center";break;case y.TOP:a.container.backgroundPosition="center top";break;case y.BOTTOM:a.container.backgroundPosition="center bottom";break;case y.TOP_RIGHT:a.container.backgroundPosition="right top";break;case y.TOP_LEFT:a.container.backgroundPosition="left top";break;case y.BOTTOM_RIGHT:a.container.backgroundPosition="right bottom";break;case y.BOTTOM_LEFT:a.container.backgroundPosition="left bottom"}return i}const fe={[y.CENTER]:"center",[y.TOP]:"top",[y.TOP_LEFT]:"top left",[y.TOP_RIGHT]:"top right",[y.BOTTOM]:"bottom",[y.BOTTOM_LEFT]:"bottom left",[y.BOTTOM_RIGHT]:"bottom right",[y.LEFT]:"left",[y.RIGHT]:"right"},Te={position:"absolute",top:"auto",right:"auto",bottom:"auto",left:"auto"};function Ie(e,t){const i={css:{container:{},img:{}}},{css:a}=i,{fittingType:n}=e,r=t.alignment;switch(a.container.position="relative",n){case M.ORIGINAL_SIZE:case M.LEGACY_ORIGINAL_SIZE:e.parts&&e.parts.length?(a.img.width=e.parts[0].width,a.img.height=e.parts[0].height):(a.img.width=e.src.width,a.img.height=e.src.height);break;case M.SCALE_TO_FIT:case M.LEGACY_FIT_WIDTH:case M.LEGACY_FIT_HEIGHT:case M.LEGACY_FULL:a.img.width=t.width,a.img.height=t.height,a.img.objectFit="contain",a.img.objectPosition=fe[r]||"unset";break;case M.LEGACY_BG_NORMAL:a.img.width="100%",a.img.height="100%",a.img.objectFit="none",a.img.objectPosition=fe[r]||"unset";break;case M.STRETCH:a.img.width=t.width,a.img.height=t.height,a.img.objectFit="fill";break;case M.SCALE_TO_FILL:a.img.width=t.width,a.img.height=t.height,a.img.objectFit="cover"}if("number"==typeof a.img.width&&"number"==typeof a.img.height&&(a.img.width!==t.width||a.img.height!==t.height)){const e=Math.round((t.height-a.img.height)/2),i=Math.round((t.width-a.img.width)/2);Object.assign(a.img,Te,function(e,t,i){return{[y.TOP_LEFT]:{top:0,left:0},[y.TOP_RIGHT]:{top:0,right:0},[y.TOP]:{top:0,left:t},[y.BOTTOM_LEFT]:{bottom:0,left:0},[y.BOTTOM_RIGHT]:{bottom:0,right:0},[y.BOTTOM]:{bottom:0,left:t},[y.RIGHT]:{top:e,right:0},[y.LEFT]:{top:e,left:0},[y.CENTER]:{width:i.width,height:i.height,objectFit:"none"}}}(e,i,t)[r])}return i}function Ee(e,t){const i={css:{container:{}},attr:{container:{},img:{}}},{css:a,attr:n}=i,{fittingType:r}=e,o=t.alignment,{width:c,height:s}=e.src;let l;switch(a.container.position="relative",r){case M.ORIGINAL_SIZE:case M.LEGACY_ORIGINAL_SIZE:case M.TILE:e.parts&&e.parts.length?(n.img.width=e.parts[0].width,n.img.height=e.parts[0].height):(n.img.width=c,n.img.height=s),n.img.preserveAspectRatio="xMidYMid slice";break;case M.SCALE_TO_FIT:case M.LEGACY_FIT_WIDTH:case M.LEGACY_FIT_HEIGHT:case M.LEGACY_FULL:n.img.width="100%",n.img.height="100%",n.img.transform="",n.img.preserveAspectRatio="";break;case M.STRETCH:n.img.width=t.width,n.img.height=t.height,n.img.x=0,n.img.y=0,n.img.transform="",n.img.preserveAspectRatio="none";break;case M.SCALE_TO_FILL:Z(e.src.id)?(n.img.width=t.width,n.img.height=t.height):(l=function(e,t,i,a,n){const r=ne(e,t,i,a,n);return{width:Math.round(e*r),height:Math.round(t*r)}}(c,s,t.width,t.height,R.FILL),n.img.width=l.width,n.img.height=l.height),n.img.x=0,n.img.y=0,n.img.transform="",n.img.preserveAspectRatio="xMidYMid slice"}if("number"==typeof n.img.width&&"number"==typeof n.img.height&&(n.img.width!==t.width||n.img.height!==t.height)){let e,i,a=0,c=0;r===M.TILE?(e=t.width%n.img.width,i=t.height%n.img.height):(e=t.width-n.img.width,i=t.height-n.img.height);const s=Math.round(e/2),l=Math.round(i/2);switch(o){case y.TOP_LEFT:a=0,c=0;break;case y.TOP:a=s,c=0;break;case y.TOP_RIGHT:a=e,c=0;break;case y.LEFT:a=0,c=l;break;case y.CENTER:a=s,c=l;break;case y.RIGHT:a=e,c=l;break;case y.BOTTOM_LEFT:a=0,c=i;break;case y.BOTTOM:a=s,c=i;break;case y.BOTTOM_RIGHT:a=e,c=i}n.img.x=a,n.img.y=c}return n.container.width=t.width,n.container.height=t.height,n.container.viewBox=[0,0,t.width,t.height].join(" "),i}function Le(e,t,i){let a;switch(t.crop&&(a=function(e,t){const i=Math.max(0,Math.min(e.width,t.x+t.width)-Math.max(0,t.x)),a=Math.max(0,Math.min(e.height,t.y+t.height)-Math.max(0,t.y));return i&&a&&(e.width!==i||e.height!==a)?{x:Math.max(0,t.x),y:Math.max(0,t.y),width:i,height:a}:null}(t,t.crop),a&&(e.src.width=a.width,e.src.height=a.height,e.src.isCropped=!0,e.parts.push(be(a)))),e.fittingType){case M.SCALE_TO_FIT:case M.LEGACY_FIT_WIDTH:case M.LEGACY_FIT_HEIGHT:case M.LEGACY_FULL:case M.FIT_AND_TILE:case M.LEGACY_BG_FIT_AND_TILE:case M.LEGACY_BG_FIT_AND_TILE_HORIZONTAL:case M.LEGACY_BG_FIT_AND_TILE_VERTICAL:case M.LEGACY_BG_NORMAL:e.parts.push(Ae(e,i));break;case M.SCALE_TO_FILL:e.parts.push(function(e,t){const i=re(e.src.width,e.src.height,R.FILL,t,e.devicePixelRatio,e.upscaleMethod),a=se(e.focalPoint);return{transformType:a?R.FILL_FOCAL:R.FILL,width:Math.round(i.width),height:Math.round(i.height),alignment:ce(t),focalPointX:a&&a.x,focalPointY:a&&a.y,upscale:i.scaleFactor>1,forceUSM:i.forceUSM,scaleFactor:i.scaleFactor,cssUpscaleNeeded:i.cssUpscaleNeeded,upscaleMethodValue:i.upscaleMethodValue}}(e,i));break;case M.STRETCH:e.parts.push(function(e,t){const i=ne(e.src.width,e.src.height,t.width,t.height,R.FILL),a={...t};return a.width=e.src.width*i,a.height=e.src.height*i,Ae(e,a)}(e,i));break;case M.TILE_HORIZONTAL:case M.TILE_VERTICAL:case M.TILE:case M.LEGACY_ORIGINAL_SIZE:case M.ORIGINAL_SIZE:a=oe(e.src,i,e.focalPoint,i.alignment),e.src.isCropped?(Object.assign(e.parts[0],a),e.src.width=a.width,e.src.height=a.height):e.parts.push(be(a));break;case M.LEGACY_STRIP_TILE_HORIZONTAL:case M.LEGACY_STRIP_TILE_VERTICAL:case M.LEGACY_STRIP_TILE:case M.LEGACY_STRIP_ORIGINAL_SIZE:e.parts.push(function(e){return{transformType:R.LEGACY_CROP,width:Math.round(e.width),height:Math.round(e.height),alignment:ce(e),upscale:!1,forceUSM:!1,scaleFactor:1,cssUpscaleNeeded:!1}}(i));break;case M.LEGACY_STRIP_SCALE_TO_FIT:case M.LEGACY_STRIP_FIT_AND_TILE:e.parts.push(function(e){return{transformType:R.FIT,width:Math.round(e.width),height:Math.round(e.height),upscale:!1,forceUSM:!0,scaleFactor:1,cssUpscaleNeeded:!1}}(i));break;case M.LEGACY_STRIP_SCALE_TO_FILL:e.parts.push(function(e){return{transformType:R.LEGACY_FILL,width:Math.round(e.width),height:Math.round(e.height),alignment:ce(e),upscale:!1,forceUSM:!0,scaleFactor:1,cssUpscaleNeeded:!1}}(i))}}function Ae(e,t){const i=re(e.src.width,e.src.height,R.FIT,t,e.devicePixelRatio,e.upscaleMethod);return{transformType:!e.src.width||!e.src.height?R.FIT:R.FILL,width:Math.round(i.width),height:Math.round(i.height),alignment:C.center,upscale:i.scaleFactor>1,forceUSM:i.forceUSM,scaleFactor:i.scaleFactor,cssUpscaleNeeded:i.cssUpscaleNeeded,upscaleMethodValue:i.upscaleMethodValue}}function be(e){return{transformType:R.CROP,x:Math.round(e.x),y:Math.round(e.y),width:Math.round(e.width),height:Math.round(e.height),upscale:!1,forceUSM:!1,scaleFactor:1,cssUpscaleNeeded:!1}}function we(e,t){t=t||{},e.quality=function(e,t){const i=e.fileType===U.PNG,a=e.fileType===U.JPG,n=e.fileType===U.WEBP,r=e.fileType===U.AVIF,o=a||i||n||r;if(o){const a=z(e.parts),n=(c=a.width,s=a.height,H[le(c,s)].quality);let r=t.quality&&t.quality>=5&&t.quality<=90?t.quality:n;return r=i?r+5:r,r}var c,s;return 0}(e,t),e.progressive=function(e){return!1!==e.progressive}(t),e.watermark=function(e){return e.watermark}(t),e.autoEncode=t.autoEncode??!0,e.encoding=t?.encoding,e.unsharpMask=function(e,t){if(function(e){const t="number"==typeof(e=e||{}).radius&&!isNaN(e.radius)&&e.radius>=.1&&e.radius<=500,i="number"==typeof e.amount&&!isNaN(e.amount)&&e.amount>=0&&e.amount<=10,a="number"==typeof e.threshold&&!isNaN(e.threshold)&&e.threshold>=0&&e.threshold<=255;return t&&i&&a}(t.unsharpMask))return{radius:de(t.unsharpMask?.radius,2),amount:de(t.unsharpMask?.amount,2),threshold:de(t.unsharpMask?.threshold,2)};if(("number"!=typeof(i=(i=t.unsharpMask)||{}).radius||isNaN(i.radius)||0!==i.radius||"number"!=typeof i.amount||isNaN(i.amount)||0!==i.amount||"number"!=typeof i.threshold||isNaN(i.threshold)||0!==i.threshold)&&function(e){const t=z(e.parts);return!(t.scaleFactor>=1)||t.forceUSM||t.transformType===R.FIT}(e))return F;var i;return}(e,t),e.filters=function(e){const t=e.filters||{},i={};Oe(t[B.CONTRAST],-100,100)&&(i[B.CONTRAST]=t[B.CONTRAST]);Oe(t[B.BRIGHTNESS],-100,100)&&(i[B.BRIGHTNESS]=t[B.BRIGHTNESS]);Oe(t[B.SATURATION],-100,100)&&(i[B.SATURATION]=t[B.SATURATION]);Oe(t[B.HUE],-180,180)&&(i[B.HUE]=t[B.HUE]);Oe(t[B.BLUR],0,100)&&(i[B.BLUR]=t[B.BLUR]);return i}(t)}function Oe(e,t,i){return"number"==typeof e&&!isNaN(e)&&0!==e&&e>=t&&e<=i}function Me(e,t,i,a){const n=function(e){return e?.isSEOBot??!1}(a),r=ie(t.id),o=function(e,t){const i=/\.([^.]*)$/,a=new RegExp(`(${Q.concat(ee).join("|")})`,"g");if(t&&t.length){let e=t;const n=t.match(i);return n&&V.includes(n[1])&&(e=t.replace(i,"")),encodeURIComponent(e).replace(a,te)}const n=e.match(/\/(.*?)$/);return(n?n[1]:e).replace(i,"")}(t.id,t.name),c=n?1:function(e){return Math.min(e.pixelAspectRatio||1,L)}(i),s=ae(t.id),l=s,d=Z(t.id,a?.hasAnimation,a?.allowAnimatedTransform),h={fileName:o,fileExtension:s,fileType:r,fittingType:e,preferredExtension:l,src:{id:t.id,width:t.width,height:t.height,isCropped:!1,isAnimated:ue(t.id,a?.hasAnimation)},focalPoint:{x:t.focalPoint&&t.focalPoint.x,y:t.focalPoint&&t.focalPoint.y},parts:[],devicePixelRatio:c,quality:0,upscaleMethod:he(a),progressive:!0,watermark:"",unsharpMask:{},filters:{},transformed:d};return d&&(Le(h,t,i),we(h,a)),h}function Re(e,t,i){const a={...i},n=me("isMobile");switch(e){case M.LEGACY_BG_FIT_AND_TILE:case M.LEGACY_BG_FIT_AND_TILE_HORIZONTAL:case M.LEGACY_BG_FIT_AND_TILE_VERTICAL:case M.LEGACY_BG_NORMAL:const e=n?w:A,i=n?O:b;a.width=Math.min(e,t.width),a.height=Math.min(i,Math.round(a.width/(t.width/t.height))),a.pixelAspectRatio=1}return a}const ye=j`fit/w_${"width"},h_${"height"}`,Ge=j`fill/w_${"width"},h_${"height"},al_${"alignment"}`,Ce=j`fill/w_${"width"},h_${"height"},fp_${"focalPointX"}_${"focalPointY"}`,Se=j`crop/x_${"x"},y_${"y"},w_${"width"},h_${"height"}`,Ne=j`crop/w_${"width"},h_${"height"},al_${"alignment"}`,ve=j`fill/w_${"width"},h_${"height"},al_${"alignment"}`,Fe=j`,lg_${"upscaleMethodValue"}`,Pe=j`,q_${"quality"}`,xe=j`,quality_auto`,ke=j`,usm_${"radius"}_${"amount"}_${"threshold"}`,He=j`,bl`,Ye=j`,wm_${"watermark"}`,Be={[B.CONTRAST]:j`,con_${"contrast"}`,[B.BRIGHTNESS]:j`,br_${"brightness"}`,[B.SATURATION]:j`,sat_${"saturation"}`,[B.HUE]:j`,hue_${"hue"}`,[B.BLUR]:j`,blur_${"blur"}`},Ue=j`,enc_auto`,$e=j`,enc_avif`,je=j`,enc_pavif`,ze=j`,pstr`;function Ve(e,t,i,a={},n){if(Z(t.id,a?.hasAnimation,a?.allowAnimatedTransform)){if(J(t.id)||K(t.id)){const{alignment:r,...o}=i;t.focalPoint={x:void 0,y:void 0},delete t?.crop,n=Me(e,t,o,a)}else n=n||Me(e,t,i,a);return function(e){const t=[];e.parts.forEach((e=>{switch(e.transformType){case R.CROP:t.push(Se(e));break;case R.LEGACY_CROP:t.push(Ne(e));break;case R.LEGACY_FILL:let i=ve(e);e.upscale&&(i+=Fe(e)),t.push(i);break;case R.FIT:let a=ye(e);e.upscale&&(a+=Fe(e)),t.push(a);break;case R.FILL:let n=Ge(e);e.upscale&&(n+=Fe(e)),t.push(n);break;case R.FILL_FOCAL:let r=Ce(e);e.upscale&&(r+=Fe(e)),t.push(r)}}));let i=t.join("/");return e.quality&&(i+=Pe(e)),e.unsharpMask&&(i+=ke(e.unsharpMask)),e.progressive||(i+=He(e)),e.watermark&&(i+=Ye(e)),e.filters&&(i+=Object.keys(e.filters).map((t=>Be[t](e.filters))).join("")),e.fileType!==U.GIF&&(e.encoding===$.AVIF?(i+=$e(e),i+=xe(e)):e.encoding===$.PAVIF?(i+=je(e),i+=xe(e)):e.autoEncode&&(i+=Ue(e))),e.src?.isAnimated&&e.transformed&&(i+=ze(e)),`${e.src.id}/${E}/${i}/${e.fileName}.${e.preferredExtension}`}(n)}return t.id}const De={[y.CENTER]:"50% 50%",[y.TOP_LEFT]:"0% 0%",[y.TOP_RIGHT]:"100% 0%",[y.TOP]:"50% 0%",[y.BOTTOM_LEFT]:"0% 100%",[y.BOTTOM_RIGHT]:"100% 100%",[y.BOTTOM]:"50% 100%",[y.RIGHT]:"100% 50%",[y.LEFT]:"0% 50%"},We=Object.entries(De).reduce(((e,[t,i])=>(e[i]=t,e)),{}),Ze=[M.TILE,M.TILE_HORIZONTAL,M.TILE_VERTICAL,M.LEGACY_BG_FIT_AND_TILE,M.LEGACY_BG_FIT_AND_TILE_HORIZONTAL,M.LEGACY_BG_FIT_AND_TILE_VERTICAL],qe=[M.LEGACY_ORIGINAL_SIZE,M.ORIGINAL_SIZE,M.LEGACY_BG_NORMAL];function Je(e,t,{width:i,height:a}){return e===M.TILE&&t.width>i&&t.height>a}function Xe(e,{width:t,height:i}){if(!t||!i){const a=t||Math.min(980,e.width),n=a/e.width;return{width:a,height:i||e.height*n}}return{width:t,height:i}}function Ke(e,t,i,a="center"){const n={img:{},container:{}};if(e===M.SCALE_TO_FILL){const e=t.focalPoint&&function(e){const t=`${e.x}% ${e.y}%`;return We[t]||""}(t.focalPoint),r=e||a;t.focalPoint&&!e?n.img={objectPosition:Qe(t,i,t.focalPoint)}:n.img={objectPosition:De[r]}}else[M.LEGACY_ORIGINAL_SIZE,M.ORIGINAL_SIZE].includes(e)?n.img={objectFit:"none",top:"auto",left:"auto",right:"auto",bottom:"auto"}:Ze.includes(e)&&(n.container={backgroundSize:`${t.width}px ${t.height}px`});return n}function Qe(e,t,i){const{width:a,height:n}=e,{width:r,height:o}=t,{x:c,y:s}=i;if(!r||!o)return`${c}% ${s}%`;const l=Math.max(r/a,o/n),d=a*l,h=n*l,u=Math.max(0,Math.min(d-r,d*(c/100)-r/2)),g=Math.max(0,Math.min(h-o,h*(s/100)-o/2));return`${u&&Math.floor(u/(d-r)*100)}% ${g&&Math.floor(g/(h-o)*100)}%`}const et={width:"100%",height:"100%"};function tt(e,t,i,a={}){const{autoEncode:n=!0,isSEOBot:r,shouldLoadHQImage:o,hasAnimation:c,allowAnimatedTransform:s,encoding:l}=a;if(!W(e,t,i))return P;const d=void 0===s||s,h=Z(t.id,c,d);if(!h||o)return it(e,t,i,{...a,autoEncode:n,useSrcset:h});const u={...i,...Xe(t,i)},{alignment:g,htmlTag:m}=u,_=Je(e,t,u),p=function(e,t,{width:i,height:a},n=!1){if(n)return{width:i,height:a};const r=!qe.includes(e),o=Je(e,t,{width:i,height:a}),c=!o&&Ze.includes(e),s=c?t.width:i,l=c?t.height:a,d=r?function(e,t){return e>900?t?.05:.15:e>500?t?.1:.18:e>200?.25:1}(s,q(t.id)):1;return{width:o?1920:s*d,height:l*d}}(e,t,u,r),f=function(e,t,i){return i?0:Ze.includes(t)?1:e>200?2:3}(u.width,e,r),T=function(e,t){const i=Ze.includes(e)&&!t;return e===M.SCALE_TO_FILL||i?M.SCALE_TO_FIT:e}(e,_),I=Ke(e,t,i,g),{uri:E}=it(T,t,{...p,alignment:g,htmlTag:m},{autoEncode:n,filters:f?{blur:f}:{},hasAnimation:c,allowAnimatedTransform:d,encoding:l}),{attr:L={},css:A}=it(e,t,{...u,alignment:g,htmlTag:m},{});return A.img=A.img||{},A.container=A.container||{},Object.assign(A.img,I.img,et),Object.assign(A.container,I.container),{uri:E,css:A,attr:L,transformed:!0}}function it(e,t,i,a){let n={};if(W(e,t,i)){const r=Re(e,t,i),o=Me(e,t,r,a);n.uri=Ve(e,t,r,a,o),a?.useSrcset&&(n.srcset=function(e,t,i,a,n){const r=i.pixelAspectRatio||1;return{dpr:[`${1===r?n.uri:Ve(e,t,{...i,pixelAspectRatio:1},a)} 1x`,`${2===r?n.uri:Ve(e,t,{...i,pixelAspectRatio:2},a)} 2x`]}}(e,t,r,a,n)),Object.assign(n,function(e,t){let i;return i=t.htmlTag===S.BG?pe:t.htmlTag===S.SVG?Ee:Ie,i(e,t)}(o,r),{transformed:o.transformed})}else n=P;return n}const at="https://static.wixstatic.com/media/";"undefined"!=typeof window&&window.devicePixelRatio;_e();_e();const nt=at,{STATIC_MEDIA_URL:rt}=e,ot=({fittingType:e,src:t,target:i,options:a})=>{const n=tt(e,t,i,{...a,autoEncode:!0});return n?.uri&&!/^[a-z]+:/.test(n.uri)&&(n.uri=`${rt}${n.uri}`),n},ct=/^[a-z]+:/,st=e=>{const{id:t,containerId:i,uri:a,alt:n,name:o="",role:c,width:s,height:l,displayMode:d,devicePixelRatio:h,quality:u,alignType:g,bgEffectName:m="",focalPoint:_,upscaleMethod:p,className:f="",crop:T,imageStyles:E={},targetWidth:L,targetHeight:A,targetScale:b,onLoad:w=()=>{},onError:O=()=>{},shouldUseLQIP:M,containerWidth:R,containerHeight:y,getPlaceholder:G,isInFirstFold:C,placeholderTransition:S,socialAttrs:N,isSEOBot:v,skipMeasure:F,hasAnimation:P,encoding:x}=e,k=r.useRef(null);let H="";const Y="blur"===S,B=r.useRef(null);if(!B.current)if(G||M||C||v){const e={upscaleMethod:p,...u||{},shouldLoadHQImage:C,isSEOBot:v,hasAnimation:P,encoding:x};B.current=(G||ot)({fittingType:d,src:{id:a,width:s,height:l,crop:T,name:o,focalPoint:_},target:{width:R,height:y,alignment:g,htmlTag:"img"},options:e}),H=!B.current.transformed||C||v?"":"true"}else B.current={uri:void 0,css:{img:{}},attr:{img:{},container:{}},transformed:!1};const U=!v&&(G||M)&&!C&&B.current.transformed,$=r.useMemo((()=>JSON.stringify({containerId:i,...i&&{containerId:i},...g&&{alignType:g},...F&&{skipMeasure:!0},displayMode:d,...R&&{targetWidth:R},...y&&{targetHeight:y},...L&&{targetWidth:L},...A&&{targetHeight:A},...b&&{targetScale:b},isLQIP:U,isSEOBot:v,lqipTransition:S,encoding:x,imageData:{width:s,height:l,uri:a,name:o,displayMode:d,hasAnimation:P,...u&&{quality:u},...h&&{devicePixelRatio:h},..._&&{focalPoint:_},...T&&{crop:T},...p&&{upscaleMethod:p}}})),[i,g,F,d,R,y,L,A,b,U,v,S,x,s,l,a,o,P,u,h,_,T,p]),j=B.current,z=j?.uri,V=j?.srcset,D=j.css?.img,W=`${I} ${f}`;r.useEffect((()=>{const e=k.current;w&&e?.currentSrc&&e?.complete&&w({target:e})}),[]);const Z=j&&!j?.transformed?`max(${s}px, 100%)`:L?`${L}px`:null;return r.createElement("wow-image",{id:t,class:W,"data-image-info":$,"data-motion-part":`BG_IMG ${i}`,"data-bg-effect-name":m,"data-has-ssr-src":H,"data-animate-blur":!v&&U&&Y?"":void 0,style:Z?{"--wix-img-max-width":Z}:{}},r.createElement("img",{src:z,ref:k,alt:n||"",role:c,style:{...D,...E},onLoad:w,onError:O,width:R||void 0,height:y||void 0,...N,srcSet:C?V?.dpr?.map((e=>ct.test(e)?e:`${rt}${e}`)).join(", "):void 0,fetchpriority:C?"high":void 0,loading:!1===C?"lazy":void 0,suppressHydrationWarning:!0}))};var lt="Tj01hh";var dt=e=>{var t,a;const{id:n,alt:o,role:c,className:s,imageStyles:d={},targetWidth:h,targetHeight:u,onLoad:g,onError:m,containerWidth:_,containerHeight:p,isInFirstFold:f,socialAttrs:T,skipMeasure:I,responsiveImageProps:E,zoomedImageResponsiveOverride:L,displayMode:A}=e,b=h||_,w=u||p,{fallbackSrc:O,srcset:M,sources:R,css:y}=E||{},{width:G,height:C,...S}=(null==E||null==(t=E.css)?void 0:t.img)||{},N="original_size"===A?null==E||null==(a=E.css)?void 0:a.img:S;var v;return O&&M&&y?r.createElement("img",i()({fetchpriority:f?"high":void 0,loading:!1===f?"lazy":void 0,sizes:b+"px",srcSet:I?null==L?void 0:L.srcset:null==E?void 0:E.srcset,id:n,src:O,alt:o||"",role:c,style:{...d,...I?{...null==L||null==(v=L.css)?void 0:v.img}:{...N}},onLoad:g,onError:m,className:l(s,lt),width:b,height:w},T)):O&&R&&y?r.createElement("picture",null,R.map((e=>{let{srcset:t,media:i,sizes:a}=e;return r.createElement("source",{key:i,srcSet:t,media:i,sizes:a})})),r.createElement("img",i()({fetchpriority:f?"high":void 0,loading:!1===f?"lazy":void 0,id:n,src:R[0].fallbackSrc,alt:o||"",role:c,style:{...d,objectFit:R[0].imgStyle.objectFit,objectPosition:R[0].imgStyle.objectPosition},onLoad:g,onError:m,className:l(s,lt),width:b,height:w},T))):r.createElement(st,e)};var ht=e=>{var t,i,a;const{className:n,customIdPrefix:o,getPlaceholder:c,hasAnimation:s,...l}=e,d=r.useMemo((()=>JSON.stringify({containerId:l.containerId,alignType:l.alignType,fittingType:l.displayMode,hasAnimation:s,imageData:{width:l.width,height:l.height,uri:l.uri,name:l.name,...l.quality&&{quality:l.quality},displayMode:l.displayMode}})),[l,s]),h=r.useRef(null);h.current||(h.current=c?c({fittingType:l.displayMode,src:{id:l.uri,width:l.width,height:l.height,name:l.name},target:{width:l.containerWidth,height:l.containerHeight,alignment:l.alignType,htmlTag:"bg"},options:{hasAnimation:s,allowAnimatedTransform:!1}}):{uri:void 0,css:{img:{}},attr:{img:{},container:{}}});const u=h.current,g=null!=(t=null==u?void 0:u.uri)?t:"",m=null!=(i=null==(a=u.css)?void 0:a.container)?i:{},_=Object.assign(g?{backgroundImage:"url("+g+")"}:{},m);return r.createElement("wix-bg-image",{id:""+(o||"bgImg_")+l.containerId,class:n,style:_,"data-tiled-image-info":d,"data-has-bg-scroll-effect":l.hasBgScrollEffect||"","data-bg-effect-name":l.bgEffectName||"","data-motion-part":"BG_IMG "+l.containerId})};const ut=new RegExp("<%= compId %>","g"),gt=(e,t)=>e.replace(ut,t);var mt=e=>null==e?void 0:e.replace(":hover",""),_t="bX9O_S",pt="Z_wCwr",ft="Jxk_UL",Tt="K8MSra",It="YTb3b4";const Et={quality:{unsharpMask:{radius:.33,amount:1,threshold:0}},devicePixelRatio:1};var Lt=e=>{const{id:t,videoRef:a,videoInfo:n,posterImageInfo:o,muted:c,preload:s,loop:d,alt:h,isVideoEnabled:u,getPlaceholder:g,extraClassName:m=""}=e;n.containerId=mt(n.containerId);const _=r.useMemo((()=>JSON.stringify(n)),[n]),p=r.createElement(r.Fragment,null,o.filterEffectSvgString&&r.createElement("svg",{id:"svg_"+n.containerId,className:It},r.createElement("defs",{dangerouslySetInnerHTML:{__html:gt(o.filterEffectSvgString,n.containerId)}})),r.createElement(dt,i()({key:n.videoId+"_img",id:o.containerId+"_img",className:l(pt,ft,"bgVideoposter",m),imageStyles:{width:"100%",height:"100%"}},o,Et,{getPlaceholder:g})));return u?r.createElement("wix-video",{id:t,"data-video-info":_,"data-motion-part":"BG_IMG "+n.containerId,class:l(_t,"bgVideo",m)},r.createElement("video",{key:n.videoId+"_video",ref:a,id:n.containerId+"_video",className:Tt,crossOrigin:"anonymous","aria-label":h,playsInline:!0,preload:s,muted:c,loop:d}),p):p},At="SUz0WK";var bt=e=>{const{id:t,containerId:i,pageId:a,children:n,bgEffectName:o="",containerSize:c}=e;return r.createElement("wix-bg-media",{id:t,class:At,"data-container-id":i,"data-container-size":((null==c?void 0:c.width)||0)+", "+((null==c?void 0:c.height)||0),"data-page-id":a,"data-bg-effect-name":o,"data-motion-part":"BG_MEDIA "+i},n)};const wt="bgOverlay";var Ot="m4khSP",Mt="FNxOn5";var Rt=e=>{const{imageOverlay:t}=e;return r.createElement("div",{"data-testid":wt,className:Ot},t&&r.createElement(ht,i()({customIdPrefix:"bgImgOverlay_",className:Mt},t)))};const yt="bgLayers",Gt="colorUnderlay",Ct="mediaPadding",St="canvas";var Nt="MW5IWV",vt="N3eg0s",Ft="Kv1aVt",Pt="dLPlxY",xt="VgO9Yg",kt="LWbAav",Ht="yK6aSC",Yt="K_YxMd",Bt="NGjcJN",Ut="mNGsUM",$t="I8xA4L";const jt="bgImage";var zt=e=>{const{videoRef:t,canvasRef:a,hasBgFullscreenScrollEffect:n,image:o,backgroundImage:c,backgroundMedia:s,video:d,backgroundOverlay:h,shouldPadMedia:u,extraClass:g="",shouldRenderUnderlay:m=!d,reducedMotion:_=!1,getPlaceholder:p,hasCanvasAnimation:f,useWixMediaCanvas:T,onClick:I}=e,{onImageLoad:E}=(e=>{let{onReady:t,image:i}=e;return(0,r.useEffect)((()=>{t&&!i&&t()}),[t,i]),{onImageLoad:e=>{null!=i&&i.onLoad&&i.onLoad(e),t&&t()}}})(e),L=mt(e.containerId),A="img_"+mt(L),b=o&&r.createElement(dt,i()({id:A,className:l(Ft,Pt,Ut,jt),imageStyles:{width:"100%",height:"100%"},getPlaceholder:p},o,{onLoad:E})),w=c&&r.createElement(ht,i()({},c,{containerId:L,className:l(Ft,Pt,Ut,jt),getPlaceholder:p})),O=d&&r.createElement(Lt,i()({id:"videoContainer_"+L},d,{extraClassName:Ht,reducedMotion:_,videoRef:t,getPlaceholder:p})),M=T&&a||f?r.createElement("wix-media-canvas",{"data-container-id":L,class:f?$t:""},b,w,O,r.createElement("canvas",{id:L+"webglcanvas",className:l(Yt,"webglcanvas"),"aria-label":(null==d?void 0:d.alt)||"",role:"presentation","data-testid":St})):r.createElement(r.Fragment,null,b,w,O,a&&r.createElement("canvas",{id:L+"webglcanvas",ref:a,className:l(Yt,"webglcanvas"),"aria-label":(null==d?void 0:d.alt)||"",role:"presentation","data-testid":St})),R=s?r.createElement(bt,i()({id:"bgMedia_"+L},s),M):r.createElement("div",{id:"bgMedia_"+L,"data-motion-part":"BG_MEDIA "+L,className:xt},M),y=h&&r.createElement(Rt,h);return r.createElement("div",{id:yt+"_"+L,"data-hook":yt,"data-motion-part":"BG_LAYER "+L,className:l(Nt,g,{[vt]:n}),onClick:I},m&&r.createElement("div",{"data-testid":Gt,className:l(kt,Ft)}),u?r.createElement("div",{"data-testid":Ct,className:Bt},R,y):r.createElement(r.Fragment,null,R,y))},Vt="YzqVVZ",Dt="mwF7X1",Wt="YGilLk";var Zt=e=>{let{id:t,fillLayers:a,children:n,meshProps:r,videoRef:c,getPlaceholder:s,onReady:d}=e;const h=a.hasBgFullscreenScrollEffect;return o().createElement(o().Fragment,null,o().createElement(zt,i()({},a,{containerId:t,onReady:d,getPlaceholder:s,videoRef:c})),o().createElement(T,i()({id:t},r,{extraClassName:l({[Dt]:h})}),n))};const qt="column-strip__column",Jt=(e,t)=>{const{id:a,className:n,customClassNames:r=[],children:c,onClick:s,onDblClick:d,onMouseEnter:m,onMouseLeave:_,shouldAddTabIndex0:p,hasPlatformClickHandler:f,translations:T,fillLayers:I,onStop:E,isRepeaterItem:L,columnOverrides:A}=e,b=l(n,Vt,h(L?g.repeaterItem:A?qt:"",...r),{[Wt]:f}),w=p?{tabIndex:0,role:"region","aria-label":(null==T?void 0:T.ariaLabel)||"Interactive element, focus to trigger content change"}:{},O=function(e,t,i){const a=o().useRef(null),n=o().useRef(null);return t?n.current||(n.current={play:()=>a.current?.play(),load:()=>a.current?.load(),pause:()=>a.current?.pause(),stop:()=>{a.current&&(a.current.pause(),a.current.currentTime=0,i&&i(a.current))}}):n.current=null,o().useImperativeHandle(e,(()=>n.current||{load(){},stop(){}})),a}(t,!!I.video,E);return o().createElement("div",i()({id:a},u(e),{className:b,onClick:s,onDoubleClick:d,onMouseEnter:m,onMouseLeave:_},w),o().createElement(Zt,i()({},e,{videoRef:O}),c))};var Xt=o().forwardRef(Jt);const Kt=(e,t)=>{const a={...e.fillLayers||{},extraClass:c};return r.createElement(Xt,i()({},e,{fillLayers:a,ref:t}))};const Qt={Column_DesignedColumn:{component:r.forwardRef(Kt)}}}(),n}()}));
//# sourceMappingURL=https://static.parastorage.com/services/editor-elements-library/dist/thunderbolt/rb_wixui.thunderbolt[Column_DesignedColumn].3312fb28.bundle.min.js.map