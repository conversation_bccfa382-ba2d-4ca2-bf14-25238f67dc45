(function anonymous(_
) {
//# sourceURL=lodash.templateSources[1]
return function(obj) {
obj || (obj = {});
var __t, __p = '';
with (obj) {
__p += 'The ' +
((__t = (propertyName)) == null ? '' : __t) +
' parameter that is passed to the ' +
((__t = (functionName)) == null ? '' : __t) +
' method cannot be set to the value ' +
((__t = (wrongValue)) == null ? '' : __t) +
'. It must be of type ' +
((__t = (expectedType)) == null ? '' : __t) +
'.';

}
return __p
}
})