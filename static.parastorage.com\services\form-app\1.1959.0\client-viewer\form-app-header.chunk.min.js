"use strict";(("undefined"!=typeof self?self:this).webpackJsonp__wix_form_app=("undefined"!=typeof self?self:this).webpackJsonp__wix_form_app||[]).push([[8455],{5904:(e,t,n)=>{n.d(t,{R:()=>l});var s=n(60751),r=n.n(s),o=n(93439),i=n(41023),d="sV5FkNn";var a=n(86803),A=n(77610);const l=e=>{let{isTruncated:t,onClick:n}=e;const{t:s}=(0,a.Bd)(),l=s(t?"field.rich-text.read-more-button.label":"field.rich-text.read-less-button.label"),c=t?A.G.TOGGLE_TRUNCATE_BUTTON_READ_MORE:A.G.TOGGLE_TRUNCATE_BUTTON_READ_LESS;return r().createElement(o.Q,{priority:i.I.primary,onClick:n,className:d,"data-hook":c},l)}},24993:(e,t,n)=>{n.d(t,{n:()=>U});var s=n(60751),r=n(16307),o=n(70119),i=n(97282),d=n(99173),a=n(65054),A=n(97821),l=n(92895),c=n(44302),u=n(16199);const E=e=>t=>{const n=[];var s;return(s=(t,s)=>{e(t,s)&&n.push(t)},({nodes:e})=>{const t=(e,t)=>e.map(((e,n,s)=>[e,{parents:t,previousSibling:s[n-1]}]));let n=t(e??[],[]);for(;n.length;){const[e,r]=n.shift();s(e,r);const o=[e,...r.parents];n=[...t(e.nodes||[],o),...n]}})(t),n},L=[a.A.APP_EMBED,a.A.AUDIO,a.A.BUTTON,a.A.CODE_BLOCK,a.A.DIVIDER,a.A.EMBED,a.A.EXTERNAL,a.A.FILE,a.A.GALLERY,a.A.GIF,a.A.HEADING,a.A.HTML,a.A.IMAGE,a.A.LINK_PREVIEW,a.A.PARAGRAPH,a.A.POLL,a.A.VIDEO],T=e=>{return t=e.type,[a.A.APP_EMBED,a.A.AUDIO,a.A.BUTTON,a.A.DIVIDER,a.A.EMBED,a.A.EXTERNAL,a.A.FILE,a.A.GALLERY,a.A.GIF,a.A.HTML,a.A.IMAGE,a.A.LINK_PREVIEW,a.A.POLL,a.A.VIDEO].includes(t);var t},I=e=>{return t=e.type,[a.A.CODE_BLOCK,a.A.HEADING,a.A.PARAGRAPH].includes(t);var t},f=e=>e.type===a.A.PARAGRAPH&&0===e.nodes?.length,p=e=>e.type===a.A.TEXT,m=e=>e.id,_=e=>(0,l.o)(e).map((({textData:e})=>e?.text??"")).get().flat().join("").split(" ").filter((e=>""!==e)),h=e=>(0,l.o)(e).map((({textData:e})=>e?.text?.length??0)).get().flat().reduce(((e,t)=>e+t),0),O=(0,o.L3)(_,r.Ej),D=e=>(0,o.Fs)([e,e],d.wu(O,m)),B=(e,t)=>{if(O(e)===t)return e;const n=_(e).slice(0,t).join(" ").length,s=e.nodes?.filter(p).map(h)??[],r=s.reduce((([e,t],n)=>[[...e,Math.min(n,t)],Math.max(t-n,0)]),[[],n])[0],o=e=>r[e]>0&&r[e]<s[e]||r[e]>0&&0===r[e+1],i=e.nodes?.filter(p).map(((e,t)=>({...e,textData:{...e.textData,decorations:e.textData?.decorations??[],text:e.textData?.text?.slice(0,r[t]).concat(o(t)?"\u2026":"")??""}}))).filter((e=>e.textData?.text?.length))??[];return{...e,nodes:i}},y=e=>!f(e)&&L.includes(e.type),P=E,g=e=>t=>(0,o.Fs)((0,l.o)(t.nodes??[]).filter((0,A.Uo)([I,(0,i.AU)(f)])).get(),r.Tj(D),(e=>t=>t.reduce((([e,t],[n,s])=>[[...e,[n,Math.min(t,s)]],Math.max(t-s,0)]),[[],e])[0])(e),u.P,(e=>t=>(0,c.J)(e).filter((e=>Object.keys(t).includes(e.id))).set((e=>0===t[e.id]?[]:[B(e,t[e.id])])))(t)),C=e=>t=>(0,c.J)(e).set((e=>t.includes(e.id)?[]:[e])),R=e=>(0,c.J)(e).filter((e=>[a.A.LIST_ITEM,a.A.ORDERED_LIST,a.A.BULLETED_LIST,a.A.BLOCKQUOTE,a.A.TABLE,a.A.COLLAPSIBLE_LIST].includes(e.type))).set((e=>0===e.nodes?.length||e.type===a.A.LIST_ITEM&&e.nodes.every((e=>0===e.nodes.length))?[]:[e])),S=e=>(0,c.J)(e).filter((e=>[a.A.COLLAPSIBLE_ITEM_TITLE,a.A.COLLAPSIBLE_ITEM_BODY].includes(e.type))).set((e=>e.nodes&&0!==e.nodes.length?[e]:[])),M=e=>(0,c.J)(e).filter((e=>e.type===a.A.COLLAPSIBLE_ITEM)).set((e=>{if(!e.nodes||0===e.nodes.length)return[];return t=e,Boolean(t.nodes&&t.nodes.some((e=>e.type===a.A.COLLAPSIBLE_ITEM_TITLE))&&t.nodes.some((e=>e.type===a.A.COLLAPSIBLE_ITEM_BODY)))?[e]:[];var t})),x=e=>(0,c.J)(e).filter((e=>e.type===a.A.COLLAPSIBLE_LIST)).set((e=>e.nodes&&0!==e.nodes?.length?[e]:[])),v=e=>(0,o.Fs)(e,S,M,x),G=e=>(0,c.J)(e).filter((e=>[a.A.TABLE_ROW,a.A.COLLAPSIBLE_ITEM].includes(e.type))).set((e=>e.nodes?.every((e=>0===e.nodes?.length))?[]:[e])),F=e=>(0,o.Fs)(e,E(((e,{previousSibling:t})=>!!(f(e)&&t&&f(t)))),(e=>e.map(m)),C(e)),N=(e,t,n)=>{const{nodeCount:s,wordCount:r,maxPlugins:i}=(e=>({nodeCount:1/0,wordCount:1/0,maxPlugins:1/0,...e}))(t);return(0,o.Fs)(e,(d=i,e=>(0,o.Fs)((0,l.o)(e?.nodes??[]).filter(T).map(m).get().slice(d),C(e))),((e,t)=>n=>(0,o.Fs)(n,P(t),(t=>t.slice(e).map(m)),C(n)))(s,n?.selectCountableNodes||y),g(r),G,R,v,n?.keepEmptyParagraphs?o.D_:F);var d};function U(e,t){const[n,r]=(0,s.useState)(!0);return{displayedContent:t&&n?N(e,{nodeCount:t}):e,isTruncated:!!t&&n,isTruncateButtonShown:!!t&&e.nodes.length>t,toggleTruncate:function(){r((e=>!e))}}}},56692:(e,t,n)=>{n.r(t),n.d(t,{default:()=>l});var s=n(60751),r=n.n(s),o=n(55446),i=n(20779),d=n(24993),a=n(5904);const A={customStyles:(0,i.eb)()},l=e=>{let{content:t,maxShownParagraphs:n,id:s,FieldLayout:l}=e;const c=(0,i.BA)(A),{displayedContent:u,isTruncated:E,isTruncateButtonShown:L,toggleTruncate:T}=(0,d.n)(t,n);return r().createElement(l,{fieldId:s},r().createElement(o.A,{content:u,customTheme:c}),L&&r().createElement(a.R,{isTruncated:E,onClick:T}))}},44302:(e,t,n)=>{n.d(t,{J:()=>T});var s=n(70119),r=n(65054),o=n(16307),i=n(65327),d=n(96351),a=n(16252);function A(e=""){return`${Math.random().toString(36).substr(2,5)}${a.uniqueId(e)}`}const l=(e,t)=>n=>{class r{constructor(e,t){this.tree=e,this.traversal=t}filter(e){return new r(this.tree,this.traversal.composePrism(d.My.fromPredicate(e)))}set(e){return(0,s.Fs)(this.traversal.asFold().getAll(this.tree),o.Tj((e=>e._modId)),l(this.tree,e))}}const a=e=>Array.isArray(e)?e:o.of(e),l=(e,n)=>r=>i.AU(((e,o)=>({...e,...t(o.reduce(((e,t)=>{return(0,s.Fs)(t,((e,t)=>n=>{const{_modId:s,...r}=n,o=r;return e.includes(s)?t(o):o})(r,n),a,(o=e,e=>[...o,...e]));var o}),[]))})))(e),c=i.jh(n,(t=>[{...t,_modId:A()},e(t)]));return new r(c,(0,d.Un)(i.B2)())},c=e=>e.nodes??[],u=e=>({nodes:e}),E=e=>({id:"root",type:r.A.UNRECOGNIZED,nodes:e.nodes}),L=e=>function(t){const n={modifier:t};return{filter(e){return n.modifier=t.filter.bind(n.modifier)(e),n.modifier.set=this.set,n.modifier.filter=this.filter,n.modifier},set(s){const r=t.set.bind(n.modifier)(s);return{...e,nodes:r.nodes}}}},T=e=>(0,s.Fs)(e,E,l(c,u),L(e))}}]);
//# sourceMappingURL=form-app-header.chunk.min.js.map