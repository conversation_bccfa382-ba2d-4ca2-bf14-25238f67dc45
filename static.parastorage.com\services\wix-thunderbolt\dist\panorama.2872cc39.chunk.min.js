"use strict";(self.webpackJsonp__wix_thunderbolt_app=self.webpackJsonp__wix_thunderbolt_app||[]).push([[8934],{30945:(e,n,r)=>{r.r(n),r.d(n,{site:()=>d});var t=r(32166),o=r(77748),a=r(10553),s=r(16540);const l=(0,o.Og)([t.RV,s.k,a.n],((e,n,r)=>{const t=(e,r,t)=>{if(n.isSentryEventFromNonWixTpa(e))return e;const o=e.exception?.values??[];return o[0]?.mechanism?.handled?e?.tags?.dontReportIfPanoramaEnabled?null:e:r.originalException&&"object"==typeof r.originalException?(t(r.originalException),null):e};return{getSdkHandlers:()=>({panorama:{onUnhandledError:n=>{r["specs.thunderbolt.loadNewerSentrySdk"]?e.addEventListener("sentry-error",(e=>{const{sentryEvent:r,sentryHint:o}=e.detail??{};null===t(r,o,n)&&e.preventDefault()})):e.Sentry.onLoad((()=>e.Sentry.addGlobalEventProcessor(((e,r)=>t(e,r,n)))))},onBreadcrumb:n=>{e.onBeforeSentryBreadcrumb=n}}})}})),d=e=>{e(t.H9).to(l)}}}]);
//# sourceMappingURL=https://static.parastorage.com/services/wix-thunderbolt/dist/panorama.2872cc39.chunk.min.js.map