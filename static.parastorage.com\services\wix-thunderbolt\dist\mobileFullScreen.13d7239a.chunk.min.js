"use strict";(self.webpackJsonp__wix_thunderbolt_app=self.webpackJsonp__wix_thunderbolt_app||[]).push([[3930],{22841:(e,n,t)=>{t.r(n),t.d(n,{TpaFullScreenModeAPI:()=>d,TpaFullScreenModeAPISymbol:()=>a.U,editorPage:()=>c,name:()=>a.B,page:()=>i});var a=t(11779),l=t(77748),o=t(32166),p=t(478),r=t(10553),s=t(98757);const d=(0,l.Og)([o.RV,p.e,r.n],((e,n,t)=>({setFullScreenMobile(a,l){l?(t=>{(0,s.FA)(e),(0,s.sE)(e,!0),n.set({[`${t}-pinned-layer`]:{"z-index":"var(--above-all-z-index) !important"}})})(a):(a=>{(0,s.Lo)(e),(0,s.sE)(e,!1);const l=`${a}-pinned-layer`,o=n.getCompStyle(l);var p,r;t["specs.thunderbolt.updateTpaHandlerMobileViewInPreviewMode"]&&!o||n.set({[l]:(p=o,r="z-index",Object.keys(p).reduce(((e,n)=>(n!==r&&(e[n]=p[n]),e)),{}))})})(a)}}))),i=e=>{e(a.U).to(d)},c=i}}]);
//# sourceMappingURL=https://static.parastorage.com/services/wix-thunderbolt/dist/mobileFullScreen.13d7239a.chunk.min.js.map