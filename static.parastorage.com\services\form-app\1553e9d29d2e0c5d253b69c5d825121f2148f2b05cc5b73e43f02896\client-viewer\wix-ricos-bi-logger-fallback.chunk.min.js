"use strict";(("undefined"!=typeof self?self:this).webpackJsonp__wix_form_app=("undefined"!=typeof self?self:this).webpackJsonp__wix_form_app||[]).push([[7212],{84998:(e,t,n)=>{n.r(t),n.d(t,{webBiLogger:()=>V});var o,r,i,s=Object.create,u=Object.defineProperty,a=Object.getOwnPropertyDescriptor,c=Object.getOwnPropertyNames,l=Object.getPrototypeOf,f=Object.prototype.hasOwnProperty,d=(e,t)=>function(){return t||(0,e[c(e)[0]])((t={exports:{}}).exports,t),t.exports},h=d({"../../node_modules/@wix/wix-bi-logger-client/dist/assert.js"(e,t){var n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};var o=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e);var n=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t));return n.name=n.constructor.name,n}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(e,Error),e}();t.exports.defined=function(e,t){if(void 0===e)throw new o(t)},t.exports.object=function(e,t){if(void 0!==e&&("object"!==(void 0===e?"undefined":n(e))||Array.isArray(e)||null===e))throw new o(t)},t.exports.ok=function(e,t){if(!e)throw new o(t)},t.exports.func=function(e,t){if(void 0!==e&&"function"!=typeof e)throw new o(t)},t.exports.boolean=function(e,t){if(void 0!==e&&"boolean"!=typeof e)throw new o(t)},t.exports.number=function(e,t){if(void 0!==e&&"number"!=typeof e)throw new o(t)},t.exports.array=function(e,t){if("function"==typeof Array.isArray){if(!Array.isArray(e))throw new o(t)}else if("[object Array]"!==Object.prototype.toString.call(e))throw new o(t)},t.exports.AssertionError=o}}),p=d({"../../node_modules/@wix/wix-bi-logger-client/dist/utils/collections.js"(e,t){t.exports.mapValues=function(e,t){return e?Object.keys(e).reduce((function(n,o){return n[o]=t(e[o],o,e),n}),{}):{}},t.exports.filterValues=function(e,t){return e?Object.keys(e).reduce((function(n,o){return t(e[o],o,e)&&(n[o]=e[o]),n}),{}):{}}}}),g=d({"../../node_modules/@wix/wix-bi-logger-client/dist/utils/promise.js"(e,t){t.exports.timedPromise=function(e,t){var n=t.message,o=t.timeout,r=new Promise((function(e,t){setTimeout(t,o,n?"Timeout: "+n:"Timeout")}));return Promise.race([e,r])},t.exports.allAsObject=function(e){var t=Object.keys(e);return Promise.all(t.map((function(t){return e[t]}))).then((function(e){return e.reduce((function(e,n,o){return e[t[o]]=n,e}),{})}))}}}),b=d({"../../node_modules/@wix/wix-bi-logger-client/dist/utils/log.js"(e,t){t.exports={error:function(){var e;console&&console.error&&(e=console).error.apply(e,arguments)}}}}),v=d({"../../node_modules/@wix/wix-bi-logger-client/dist/utils/debounce.js"(e,t){t.exports=function(e,t,n){var o=void 0;return function(){var r=this,i=arguments,s=n&&!o;clearTimeout(o),o=setTimeout((function(){o=null,n||e.apply(r,i)}),t),s&&e.apply(r,i)}}}}),y=d({"../../node_modules/@wix/wix-bi-logger-client/dist/utils/throttle.js"(e,t){t.exports=function(e,t){var n=void 0;return function(){for(var o=arguments.length,r=Array(o),i=0;i<o;i++)r[i]=arguments[i];n||(n=setTimeout((function(){e.apply(void 0,r),n=null}),t))}}}}),m=d({"../../node_modules/@wix/wix-bi-logger-client/dist/utils/batch-queue.js"(e,t){var n=function(){function e(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,n,o){return n&&e(t.prototype,n),o&&e(t,o),t}}(),o=function(){return function(e,t){if(Array.isArray(e))return e;if(Symbol.iterator in Object(e))return function(e,t){var n=[],o=!0,r=!1,i=void 0;try{for(var s,u=e[Symbol.iterator]();!(o=(s=u.next()).done)&&(n.push(s.value),!t||n.length!==t);o=!0);}catch(e){r=!0,i=e}finally{try{!o&&u.return&&u.return()}finally{if(r)throw i}}return n}(e,t);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e};var i=v(),s=y(),u=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this._initilized=!1}return n(e,[{key:"_reset",value:function(){var e=this;this._startTime=Date.now(),this._resolve=null,this._promise=new Promise((function(t){return e._resolve=t}))}},{key:"init",value:function(e,t){var n=this,o=e.delayMs,r=e.maxBatchSize,u=e.useThrottle,a=e.optimizeBatch;this._initilized||(this._maxBatchSize=r,this._optimizeBatch=a,this._queue=[],this._flushHandler=t,this._flushDebounced=u?s((function(){return n.flush()}),o):i((function(){return n.flush()}),o),this._initilized=!0,this._reset())}},{key:"flush",value:function(){if(!this._queue.length)return Promise.resolve();var e=this._queue.splice(0,this._queue.length),t=this._resolve,n=this._startTime;this._reset();var i=function(e,t){return{dt:Date.now()-t,e,g:{}}}(e,n);return this._optimizeBatch&&(i=function(e){var t={},n=e.e.length,i=e.e.map((function(e){var n=Object.keys(e.f).map((function(n){var o=e.f[n],r=n+"|"+o;return t[r]=t[r]||0,t[r]++,[n,o,r]}));return r({},e,{f:n})})),s={};return i=i.map((function(e){var i=e.f.reduce((function(e,r){var i=o(r,3),u=i[0],a=i[1],c=i[2];return t[c]===n?s[u]=a:e[u]=a,e}),{});return r({},e,{f:i})})),r({},e,{e:i,g:s})}(i)),this._flushHandler(i).then(t)}},{key:"feed",value:function(e,t){return this._queue.push(function(e,t,n){return{dt:Date.now()-n,f:e,context:t}}(e,t,this._startTime)),this._queue.length===this._maxBatchSize?this.flush():(this._flushDebounced(),this._promise)}}]),e}();t.exports=u}}),_=d({"../../node_modules/@wix/wix-bi-logger-client/dist/consent-policy.js"(e,t){var n={functional:!0,analytics:!0,__default:!0},o=function(e){return!1===e.functional||!1===e.analytics};t.exports={shouldMuteNonEssentials:o,shouldMuteByCategory:function(e,t){return"essential"!==t&&("functional"===t||"analytics"===t?!1===e[t]:o(e))},getPolicy:function(e){return"function"==typeof e&&e()||n}}}}),w=d({"../../node_modules/@wix/wix-bi-logger-client/dist/bi-logger.js"(e,t){var n=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},o=function(){function e(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,n,o){return n&&e(t.prototype,n),o&&e(t,o),t}}();var r=h(),i=p(),s=i.mapValues,u=i.filterValues,a=g(),c=b(),l=m(),f=_(),d=f.shouldMuteByCategory,v=f.shouldMuteNonEssentials,y=f.getPolicy,w=function(){function e(t,n){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this._publishers=t.publishers,this._validators=t.validators||[],this._defaults=t.defaults,this._ownDefaults={},this._events=t.events||{},this._context=n||{},this._defaultValueTimeout=t.defaultValueTimeout||5e3,this._defaultContinueOnFail=t.defaultContinueOnFail||!1,this._onPublisherFailHandler=t.onPublisherFailHandler||e._defaultPublisherFailHandler,this._isMuted=t.isMuted||function(){return!1},this._eventTransformer=t.eventTransformer||function(e){return e},this._payloadTransformer=t.payloadTransformer||function(e){return e},this._consentPolicyGetter=t.consentPolicyGetter||function(){return null},this._nonEssentialDefaults=t.nonEssentialDefaults||{},this._maxBatchSize=t.maxBatchSize||100,this._globalBatchQueue=t.globalBatchQueue}return o(e,[{key:"report",value:function(e){r.defined(e,"Data must be provided"),r.object(e,"Data must be an object");var t=e.src,o=e.evid,i=e.params,s=function(e,t){var n={};for(var o in e)t.indexOf(o)>=0||Object.prototype.hasOwnProperty.call(e,o)&&(n[o]=e[o]);return n}(e,["src","evid","params"]);return this.log(n({src:t,evid:o},i),s)}},{key:"log",value:function(e,t,o){var i=this;r.defined(e,"Event object or event key must be provided.");var s=this._extractEventAndContext(e,t,o),u=s.event,a=s.context,c=y(this._consentPolicyGetter),l=n({},this._context,a);if(this._isMuted()||d(c,l.category))return Promise.resolve();if(l.useBatch){var f=this._initQueue(l,c),h=function(e){var t=i._eventTransformer(e,l);return f.feed(t,l)};if(this._globalBatchQueue)return this._getDefaults(this._defaults).then((function(e){var t=n({},e,i._getDynamicNonEssentialDefaults(c),i._getStaticNonEssentialDefaults(c),u,i._getPolicyFields(c,l.category));return h(t)}));var p=n({},this._getDynamicDefaults(this._defaults),this._getDynamicNonEssentialDefaults(c),u,this._getPolicyFields(c,l.category));return h(p)}return this._getDefaults(this._defaults).then((function(e){var t=Object.assign(e,i._getDynamicNonEssentialDefaults(c),i._getStaticNonEssentialDefaults(c),u,i._getPolicyFields(c,l.category));if(!(0===i._validators.length||i._validators.some((function(e){return e.match(t)&&(e.execute(t)||!0)}))))throw new Error("No validator accepted the event. Source: "+t.src+" Evid: "+(t.evid||t.evtId));var n=i._eventTransformer(t,l);return n=i._payloadTransformer(n,l),i._send(n,l)}))}},{key:"flush",value:function(){return this._queue?this._queue.flush():Promise.resolve()}},{key:"updateDefaults",value:function(e){return r.defined(e,"Defaults must be provided"),r.object(e,"Defaults must be an object"),Object.assign(this._ownDefaults,e),this}},{key:"_send",value:function(e){var t=this,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return Promise.all(this._publishers.map((function(r){var i=n({},e);return Promise.resolve().then((function(){return r(i,o)})).catch((function(n){return t._onPublisherFailHandler(n,{publisherName:r.name,payload:e})}))}))).then((function(){}))}},{key:"_extractEventAndContext",value:function(e,t,o){var i=void 0,s={};if("string"!=typeof e)i=e,s=t||s;else{if(!(i=this._events[e]))throw new r.AssertionError("Event with key '"+e+"' not found in event map.");t&&(i=n({},i,t),s=o||s)}return{event:i,context:s}}},{key:"_initQueue",value:function(e,t){var n=this;if(this._queue)return this._queue;this._queue=this._globalBatchQueue||new l;return this._queue.init({delayMs:!0===e.useBatch?300:e.useBatch,maxBatchSize:this._maxBatchSize,useThrottle:!!this._globalBatchQueue,optimizeBatch:!!this._globalBatchQueue},(function(o){n._globalBatchQueue||(o.g=Object.assign(n._getStaticDefaults(n._defaults),n._getStaticNonEssentialDefaults(t)));var r=n._payloadTransformer(o,e);return n._send(r,e)})),this._queue}},{key:"_handleDefaultsError",value:function(e){return this._defaultContinueOnFail?(c.error(e),null):Promise.reject(e)}},{key:"_getDynamicNonEssentialDefaults",value:function(e){if(!v(e))return this._getDynamicDefaults(this._nonEssentialDefaults)}},{key:"_getStaticNonEssentialDefaults",value:function(e){if(!v(e))return this._getStaticDefaults(this._nonEssentialDefaults)}},{key:"_withOwnDefaults",value:function(e){return Object.assign({},e,this._ownDefaults)}},{key:"_getDynamicDefaults",value:function(e){e=this._withOwnDefaults(e);var t=u(e,(function(e){return"function"==typeof e}));return s(t,(function(e){return e()}))}},{key:"_getStaticDefaults",value:function(e){return e=this._withOwnDefaults(e),u(e,(function(e){return"function"!=typeof e}))}},{key:"_getDefaults",value:function(e){var t=this;if(!(e=this._withOwnDefaults(e)))return Promise.resolve({});var n=s(e,(function(e,n){if("function"==typeof e)try{e=e()}catch(e){return t._handleDefaultsError(e)}return e&&"function"==typeof e.then?a.timedPromise(e,{message:"Cannot get default value '"+n+" for BI Event'",timeout:t._defaultValueTimeout}).catch((function(e){return t._handleDefaultsError(e)})):e}));return a.allAsObject(n)}},{key:"_encodePolicyValue",value:function(e,t){return e?"boolean"==typeof e[t]?e[t]?1:0:e[t]:1}},{key:"_getPolicyFields",value:function(e,t){return{_isca:this._encodePolicyValue(e,"analytics"),_iscf:this._encodePolicyValue(e,"functional"),_ispd:e.__default?1:0,_ise:"essential"===t?1:0}}}],[{key:"_defaultPublisherFailHandler",value:function(e,t){return t.publisherName}}]),e}();t.exports=w}}),P=d({"../../node_modules/@wix/wix-bi-logger-client/dist/bi-logger-manager.js"(e,t){var n=function(){function e(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,n,o){return n&&e(t.prototype,n),o&&e(t,o),t}}();var o=h(),r=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.reset()}return n(e,[{key:"reset",value:function(){this._handlers=[]}},{key:"onLoggerCreated",value:function(e){var t=this;return o.defined(e,"Handler must be provided."),o.func(e,"Handler must be a function."),this._handlers.push(e),function(){var n=t._handlers.indexOf(e);-1!==n&&t._handlers.splice(n,1)}}},{key:"notifyLoggerCreated",value:function(e){this._handlers.forEach((function(t){return t(e)}))}}]),e}();t.exports={manager:new r,BiLoggerManager:r}}}),x=d({"../../node_modules/@wix/wix-bi-logger-client/dist/bi-logger-factory.js"(e,t){var n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o=function(){function e(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,n,o){return n&&e(t.prototype,n),o&&e(t,o),t}}();var r=h(),i=w(),s=P(),u=m(),a=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this._publishers=[],this._validators=[],this._defaults={},this._nonEssentialDefaults={},this._events={},this._isMuted=!1,this._eventTransformer=null,this._payloadTransformer=null,this._consentPolicyGetter=null,this._maxBatchSize=null,this._batchQueue=null}return o(e,[{key:"addPublisher",value:function(e){return r.defined(e,"Publisher must be provided"),r.ok("function"==typeof e,"Expected a publisher function"),this._publishers.push(e),this}},{key:"addValidator",value:function(e){return r.defined(e,"Validator must be provided"),r.ok("object"===(void 0===e?"undefined":n(e))&&e,"Expected a validator object"),r.ok(e.execute&&e.match,"Provided validator does not match the interface"),this._validators.push(e),this}},{key:"setDefaults",value:function(e){return r.defined(e,"Defaults must be provided"),r.object(e,"Defaults must be an object"),this._defaults=e,this}},{key:"updateDefaults",value:function(e){return r.defined(e,"Defaults must be provided"),r.object(e,"Defaults must be an object"),Object.assign(this._defaults,e),this}},{key:"updateNonEssentialDefaults",value:function(e){return r.defined(e,"Non-essential Defaults must be provided"),r.object(e,"Non-essential Defaults must be an object"),Object.assign(this._nonEssentialDefaults,e),this}},{key:"setEvents",value:function(e){return r.defined(e,"Events must be provided"),r.object(e,"Events must be an object"),this._events=e,this}},{key:"setDefaultValueTimeout",value:function(e){return r.defined(e,"Default Value Timeout must be provided"),this._defaultValueTimeout=e,this}},{key:"setDefaultContinueOnFail",value:function(e){return r.defined(e,"Default Continue On Fail must be provided"),this._defaultContinueOnFail=e,this}},{key:"setPublisherFailHandler",value:function(e){return r.defined(e,"Publisher Fail Handler must be provided"),this._onPublisherFailHandler=e,this}},{key:"setMuted",value:function(e){return r.defined(e,"Is Muted must be provided"),r.boolean(e,"Is Muted must be a boolean"),this._isMuted=e,this}},{key:"setMaxBatchSize",value:function(e){return r.defined(e,"Max Batch Size must be provided"),r.number(e,"Max Batch Size must be a number"),r.ok(e>0,"Max Batch Size must be higher than 0"),this._maxBatchSize=e,this}},{key:"setGlobalBatchQueue",value:function(e){return r.defined(e,"Global Batch Queue must be provided"),r.ok(e instanceof u,"Global Batch Queue must be an instance of BatchQueue"),this._globalBatchQueue=e,this}},{key:"withEventTransformer",value:function(e){return r.defined(e,"Event Transformer must be provided"),r.func(e,"Event Transformer must be a function"),this._eventTransformer=e,this}},{key:"withPayloadTransformer",value:function(e){return r.defined(e,"Payload Transformer must be provided"),r.func(e,"Payload Transformer must be a function"),this._payloadTransformer=e,this}},{key:"withConsentPolicyGetter",value:function(e){return r.defined(e,"Consent Policy Getter must be provided"),r.func(e,"Consent Policy Getter must be a function"),this._consentPolicyGetter=e,this}},{key:"logger",value:function(e){var t=this,n=new i({publishers:this._publishers,validators:this._validators,defaults:this._defaults,events:this._events,defaultValueTimeout:this._defaultValueTimeout,defaultContinueOnFail:this._defaultContinueOnFail,onPublisherFailHandler:this._onPublisherFailHandler,isMuted:function(){return t._isMuted},eventTransformer:this._eventTransformer,payloadTransformer:this._payloadTransformer,consentPolicyGetter:this._consentPolicyGetter,nonEssentialDefaults:this._nonEssentialDefaults,maxBatchSize:this._maxBatchSize,globalBatchQueue:this._globalBatchQueue},e);return s.manager.notifyLoggerCreated(n),n}}]),e}();t.exports=a}}),j=d({"../../node_modules/@wix/wix-bi-logger-client/dist/index.js"(e,t){var n=x(),o=w(),r=P(),i=m();t.exports.BiLoggerFactory=n,t.exports.BiLogger=o,t.exports.BiLoggerManager=r.BiLoggerManager,t.exports.factory=function(){return new n},t.exports.manager=r.manager,t.exports.createBatchQueue=function(){return new i}}}),O=d({"../../node_modules/@wix/web-bi-logger/dist/src/lib/browser.js"(e){Object.defineProperty(e,"__esModule",{value:!0}),e.readCookie=e.sendBeacon=e.getWindowSize=e.getDesktopSize=e.now=void 0,e.now=function(){return"undefined"!=typeof performance&&performance&&performance.now?performance.now():-1},e.getDesktopSize=function(e){var t=e.screen&&e.screen.width||0,n=e.screen&&e.screen.height||0;return"".concat(t,"x").concat(n)},e.getWindowSize=function(e){var t=0,n=0;return e.innerWidth?(t=e.innerWidth,n=e.innerHeight):e.document&&(e.document.documentElement&&e.document.documentElement.clientWidth?(t=e.document.documentElement.clientWidth,n=e.document.documentElement.clientHeight):e.document.body&&e.document.body.clientWidth&&(t=e.document.body.clientWidth,n=e.document.body.clientHeight)),"".concat(t,"x").concat(n)},e.sendBeacon=function(e,t){return!("undefined"==typeof navigator||!navigator||!navigator.sendBeacon)&&navigator.sendBeacon(e,t)},e.readCookie=function(e){if("undefined"==typeof document)return null;if(window.__ENABLE_COOKIE_READ_OPTIMIZATION__&&window.__BI_cookie_cache&&void 0!==window.__BI_cookie_cache[e])return window.__BI_cookie_cache[e];for(var t=0,n=document.cookie.split(";");t<n.length;t++){for(var o=n[t].split("="),r=o[0],i=o[1];" "===r[0];)r=r.substr(1);if(r===e)return window.__ENABLE_COOKIE_READ_OPTIMIZATION__&&(window.__BI_cookie_cache||(window.__BI_cookie_cache={}),window.__BI_cookie_cache[e]=i),i}return""}}}),C=d({"../../node_modules/@wix/web-bi-logger/dist/src/constants.js"(e){var t;Object.defineProperty(e,"__esModule",{value:!0}),e.DefaultCommonConfig=e.BrandToHostMap=e.HeadlessHost=e.DefaultBrand=e.EventContextMap=e.BackofficeDomains=e.LoggerVersion=void 0,e.LoggerVersion="2.0.985|C",e.BackofficeDomains=[".wix.com",".editorx.com"],(t=e.EventContextMap||(e.EventContextMap={})).msid="_msid",t.clientId="_client_id",t.uuid="_uuid",t.visitorId="_visitorId",t.siteMemberId="_siteMemberId",t.brandId="_brandId",t.siteBranchId="_siteBranchId",t.ms="_ms",t.lv="_lv",t.isHeadless="_isHeadless",t.hostingPlatform="_hostingPlatform",e.DefaultBrand="wix",e.HeadlessHost="VIEWER_HEADLESS",e.BrandToHostMap={wix:"wix",editorx:"editorx"},e.DefaultCommonConfig={brand:e.DefaultBrand}}}),E=d({"../../node_modules/@wix/web-bi-logger/dist/src/lib/env.js"(e){Object.defineProperty(e,"__esModule",{value:!0}),e.getGlobal=e.getWindowIfTop=e.isBackoffice=e.isWebWorker=e.getHost=e.setHost=void 0;var t,n,o=C();function r(e){if("undefined"!=typeof window&&window)try{return window.top===window.self?e?e(window):window:null}catch(e){return null}return null}function i(e,t){return void 0===t&&(t=null),"undefined"!=typeof self&&self&&e(self)||t}e.setHost=function(e){t=e},e.getHost=function(e){return void 0===e&&(e=o.DefaultBrand),t||(void 0===n&&(n=i((function(e){return e.location&&e.location.hostname&&(e.location.hostname.match(/\.(wix|editorx)\.com$/)||[])[1]||null}),null)),r=n||o.BrandToHostMap[e]||o.BrandToHostMap[o.DefaultBrand],"frog.".concat(r,".com"));var r},e.isWebWorker=function(){return"undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope},e.isBackoffice=function(){return r((function(e){var t=e.document;return o.BackofficeDomains.some((function(e){return-1!==t.location.host.indexOf(e)}))}))},e.getWindowIfTop=r,e.getGlobal=i}}),B=d({"../../node_modules/@wix/web-bi-logger/dist/src/lib/utils.js"(e){Object.defineProperty(e,"__esModule",{value:!0}),e.guid=e.buildBiUrl=e.getCookies=void 0;var t=O(),n=E(),o=0;e.getCookies=function(e){return Object.keys(e).reduce((function(n,o){var r="string"==typeof e[o]?{name:e[o]}:e[o],i=r.name,s=r.transform,u=(void 0===s?function(e){return e}:s)((0,t.readCookie)(i));return u&&(n[o]=u),n}),{})},e.buildBiUrl=function(e,t,r){var i=e.host,s=void 0===i?"":i,u=e.endpoint,a=void 0===u?"":u;void 0===r&&(r={}),s=(s="function"==typeof s?s():s)||(0,n.getHost)(),a=r.endpoint||a,r.useBatch||(t._=""+(new Date).getTime()+o++);var c=r.useBatch?[]:Object.keys(t).map((function(e){return"".concat(encodeURIComponent(e),"=").concat(encodeURIComponent(t[e]))}),[]);return["//".concat(s,"/").concat(a)].concat(c.length?c.join("&"):[]).join("?")},e.guid=function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)}))}}}),M=d({"../../node_modules/@wix/web-bi-logger/dist/src/lib/navigator.js"(e){Object.defineProperty(e,"__esModule",{value:!0}),e.listen=void 0;var t=E();function n(e){setTimeout((function(){return(0,t.getWindowIfTop)((function(t){return e(t.location.href)}))}))}function o(e){(0,t.getWindowIfTop)((function(t){var n=t.history;return["pushState","replaceState"].forEach((function(t){!function(e,t,n){if(e&&e[t]){var o=e[t];e[t]=function(){return n.apply(null,arguments),o.apply(e,arguments)}}}(n,t,(function(t,n,o){return e(o)}))}))}))}function r(e){(0,t.getWindowIfTop)((function(t){return t.addEventListener("popstate",(function(){return e(t.location.href)}))}))}e.listen=function(e){(0,t.getWindowIfTop)((function(t){var i=t.document.referrer;[n,o,r].forEach((function(t){return t((function(t){e(i,t),i=t}))}))}))}}}),T=d({"../../node_modules/@wix/web-bi-logger/dist/src/lib/tracker.js"(e){Object.defineProperty(e,"__esModule",{value:!0}),e.track=void 0;var t=B(),n=M(),o=E(),r=O(),i="__isTrackingPageViews__";function s(e){var i=function(e){return e({endpoint:"p"}).updateDefaults({src:19,evid:3,vsi:(0,t.guid)()}).logger()}(e),s=1;(0,n.listen)((function(e,t){var n=(0,o.getWindowIfTop)((function(e){return{sr:(0,r.getDesktopSize)(e),wr:(0,r.getWindowSize)(e)}})),u=n.sr,a=n.wr;i.log({from:e,to:t,fis:s,sr:u,wr:a}),s=0}))}e.track=function(e,t){void 0===t&&(t={force:!1}),(t.force||!(0,o.isWebWorker)()&&(0,o.isBackoffice)()&&(0,o.getWindowIfTop)((function(e){return!e[i]})))&&((0,o.getWindowIfTop)((function(e){return e[i]=!0})),s(e))}}}),D=d({"../../node_modules/@wix/consent-policy-client-accessor/dist/src/consts.js"(e){Object.defineProperty(e,"__esModule",{value:!0}),e.ALLOW_ALL_POLICY=e.PROPS_ACCESS=e.METHODS=void 0,e.METHODS={policy:"getCurrentConsentPolicy",header:"_getConsentPolicyHeader",isMethod:!0},e.PROPS_ACCESS={policy:"consentPolicy",header:"consentPolicyHeader",isMethod:!1},e.ALLOW_ALL_POLICY={essential:!0,dataToThirdParty:!0,advertising:!0,functional:!0,analytics:!0}}}),k=d({"../../node_modules/@wix/consent-policy-client-accessor/dist/src/consent-policy-accessor.js"(e){Object.defineProperty(e,"__esModule",{value:!0}),e.getConsentPolicyManager=e.getGlobalAccessor=void 0;var t,n=D();function o(){return[u,s,i,r].forEach((function(e){try{t||(t=e())}catch(e){}})),t}function r(){return window}function i(){return self}function s(){return global}function u(){return globalThis}function a(e,t){return{getCurrentConsentPolicy:function(){var n=e[t.policy];return t.isMethod?n():n},_getConsentPolicyHeader:function(){var n=e[t.header];return t.isMethod?n():n},deleteReference:!t.isMethod}}e.getGlobalAccessor=o,e.getConsentPolicyManager=function(e){var r;void 0===e&&(e=void 0),e&&(t=e),t||e||(t=o());try{"object"==typeof t.commonConfig&&t.commonConfig.consentPolicy&&(r=a(t.commonConfig,n.PROPS_ACCESS)),r||"object"!=typeof t.consentPolicyManager||(r=a(t.consentPolicyManager,n.METHODS)),r||"object"!=typeof t.Wix||"object"!=typeof t.Wix.Utils||"function"!=typeof t.Wix.Utils.getCurrentConsentPolicy||(r=a(t.Wix.Utils,n.METHODS))}catch(e){}return r}}}),S=d({"../../node_modules/@wix/consent-policy-client-accessor/dist/src/index.js"(e){Object.defineProperty(e,"__esModule",{value:!0}),e.ConsentPolicyAccessor=void 0;var t=k(),n=D(),o=function(){function e(e){e&&(this.env=e),this.consentPolicyAccess=(0,t.getConsentPolicyManager)(this.env)}return e.prototype.clearReference=function(){this.consentPolicyAccess&&this.consentPolicyAccess.deleteReference&&(this.consentPolicyAccess=void 0)},e.prototype.getCurrentConsentPolicy=function(){this.consentPolicyAccess||(this.consentPolicyAccess=(0,t.getConsentPolicyManager)(this.env));var e=this.consentPolicyAccess&&this.consentPolicyAccess.getCurrentConsentPolicy()||n.ALLOW_ALL_POLICY;return this.clearReference(),e.policy?e.policy:e},e.prototype.getConsentPolicyHeader=function(e){void 0===e&&(e=!1),this.consentPolicyAccess||(this.consentPolicyAccess=(0,t.getConsentPolicyManager)(this.env));var n=this.consentPolicyAccess&&this.consentPolicyAccess._getConsentPolicyHeader()||{"consent-policy":""};return this.clearReference(),e?n["consent-policy"]:n},e}();e.ConsentPolicyAccessor=o}}),A=d({"../../node_modules/@wix/web-bi-logger/dist/src/lib/errors.js"(e){var t=e&&e.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(t,n)};return function(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}();Object.defineProperty(e,"__esModule",{value:!0}),e.APINotSupportedError=e.AssertionError=void 0;var n=function(e){function n(t){var n=e.call(this,t)||this;return n.name=n.constructor.name,n}return t(n,e),n}(Error);e.AssertionError=n;var o=function(e){function n(t){var n=e.call(this,t)||this;return n.name=n.constructor.name,n}return t(n,e),n}(Error);e.APINotSupportedError=o}}),H=d({"../../node_modules/@wix/web-bi-logger/dist/src/lib/assert.js"(e){Object.defineProperty(e,"__esModule",{value:!0}),e.ok=e.object=e.defined=void 0;var t=A();e.defined=function(e,n){if(void 0===e)throw new t.AssertionError(n)};e.object=function(e,n){if(void 0!==e&&("object"!=typeof e||Array.isArray(e)||null===e))throw new t.AssertionError(n)};e.ok=function(e,n){if(!e)throw new t.AssertionError(n)}}}),I=d({"../../node_modules/@wix/web-bi-logger/dist/src/types.js"(e){var t,n,o;Object.defineProperty(e,"__esModule",{value:!0}),e.Errors=e.EventCategories=e.PublishMethods=void 0,(t=e.PublishMethods||(e.PublishMethods={})).Auto="auto",t.PostMessage="postMessage",t.Fetch="fetch",(n=e.EventCategories||(e.EventCategories={})).Essential="essential",n.Functional="functional",n.Analytics="analytics",(o=e.Errors||(e.Errors={}))[o.Unsupported=0]="Unsupported"}}),F=d({"../../node_modules/@wix/web-bi-logger/dist/src/lib/transport.js"(e){Object.defineProperty(e,"__esModule",{value:!0}),e.postMessageTransport=e.xhrTransport=e.fetchTransport=e.pixelTransport=e.beaconTransport=void 0;var t=O(),n=A();e.beaconTransport=function(e,n,o){return void 0===o&&(o=!1),new Promise((function(r,i){return(0,t.sendBeacon)(e,o?JSON.stringify(n):void 0)?r():i(new Error("Transport Error: Cannot send bi using beacon"))}))},e.pixelTransport=function(e,t){return new Promise((function(n,o){var r=t||new window.Image(0,0);r.onload=function(){return n()},r.onerror=function(){return o(new Error("Transport Error: Cannot send bi using pixel"))},r.src=e}))},e.fetchTransport=function(e,t,o,r){if(void 0===o&&(o="GET"),void 0===r){if("undefined"==typeof fetch)return Promise.reject(new n.APINotSupportedError("fetch"));r=fetch}var i={method:o,credentials:"include"};return"POST"===o?i.body=JSON.stringify(t):i.keepalive=!0,r(e,i).then((function(e){if(!e.ok)throw Error("Transport Error: Cannot send bi using fetch. Status: ".concat(e.status))}))},e.xhrTransport=function(e,t,n){return void 0===n&&(n="GET"),new Promise((function(o,r){var i=new XMLHttpRequest;i.open(n,"".concat(location.protocol).concat(e)),i.onload=o,i.onerror=function(){r(new Error("Transport Error: Cannot send bi using xhr."))},i.withCredentials=!0,"POST"===n?i.send(JSON.stringify(t)):i.send()}))},e.postMessageTransport=function(e,t){void 0===t&&(t=self.postMessage);var n=[e];return"undefined"==typeof WorkerGlobalScope&&n.push("*"),t.apply(self,n)}}}),W=d({"../../node_modules/@wix/web-bi-logger/dist/src/lib/publishers.js"(e){var t=e&&e.__assign||function(){return t=Object.assign||function(e){for(var t,n=1,o=arguments.length;n<o;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},t.apply(this,arguments)},n=e&&e.__rest||function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};Object.defineProperty(e,"__esModule",{value:!0}),e.getTransformers=e.getPublisher=e.postMessagePublisher=e.xhrPublisher=e.fetchPublisher=e.beaconPublisher=e.imagePublisher=e.resolvePublisher=void 0;var o=B(),r=E(),i=A(),s=I(),u=F(),a=function(e){return e.useBatch?"POST":"GET"};function c(e){return void 0===e&&(e={}),function(t,n){void 0===n&&(n={});var r=(0,o.buildBiUrl)(e,t,n);return(0,u.beaconTransport)(r,t,!!n.useBatch).catch((function(){var e=a(n);return n.useBatch?(0,u.fetchTransport)(r,t,e).catch((function(n){return n instanceof i.APINotSupportedError?(0,u.xhrTransport)(r,t,e):Promise.reject(n)})):(0,u.pixelTransport)(r,n.image)}))}}function l(e,t){return void 0===e&&(e={}),function(n,r){void 0===r&&(r={});var s=(0,o.buildBiUrl)(e,n,r),c=a(r);return(0,u.fetchTransport)(s,n,c,t).catch((function(e){return e instanceof i.APINotSupportedError?(0,u.xhrTransport)(s,n,c):Promise.reject(e)}))}}function f(e,t){return void 0===e&&(e={}),function(e){return(0,u.postMessageTransport)(e,t)}}e.resolvePublisher=function(e,t){var n,o;return e.publishMethod===s.PublishMethods.PostMessage?(n=f,o=t&&t[s.PublishMethods.PostMessage]):e.publishMethod===s.PublishMethods.Fetch?(n=l,o=t&&t[s.PublishMethods.Fetch]):n=(0,r.isWebWorker)()?l:c,n(e,o)},e.imagePublisher=function(e){return void 0===e&&(e={}),function(t,n){if(void 0===n&&(n={}),n.useBatch)throw new i.APINotSupportedError("Can't use image publisher to send batched events.");var r=(0,o.buildBiUrl)(e,t,n);return(0,u.pixelTransport)(r,n.image)}},e.beaconPublisher=c,e.fetchPublisher=l,e.xhrPublisher=function(e){return void 0===e&&(e={}),function(t,n){void 0===n&&(n={});var r=(0,o.buildBiUrl)(e,t,n),i=a(n);return(0,u.xhrTransport)(r,t,i)}},e.postMessagePublisher=f,e.getPublisher=function(t,n){return(0,e.resolvePublisher)(t,n)};e.getTransformers=function(e,o){var r,i;o&&("function"!=typeof o&&o.postMessage&&e.publishMethod===s.PublishMethods.PostMessage?i=o.postMessage:"function"==typeof o&&(r=o));return{eventTransformer:r,payloadTransformer:function(o,r){if(r.useBatch){var s=function(t){return(null==t?void 0:t.endpoint)||r.endpoint||e.endpoint};o.e=o.e.map((function(e){var o=e.context,r=n(e,["context"]);return t(t({},r),{f:t(t({},r.f),{_rp:s(o)})})}))}return i?i(o):o}}}}}),L=d({"../../node_modules/@wix/web-bi-logger/dist/src/lib/common-config.js"(e){Object.defineProperty(e,"__esModule",{value:!0}),e.getWixHost=e.getBranchId=e.getBrand=e.getCommonConfigValue=void 0;var t=E(),n=C();e.getCommonConfigValue=function(e,o){return void 0===o&&(o=(0,t.getGlobal)((function(e){return e.Wix&&e.Wix.Utils&&e.Wix.Utils.commonConfig||e.commonConfig}))),(o=o&&"function"==typeof o.getAll?o.getAll():o)&&void 0!==o[e]?o[e]:n.DefaultCommonConfig[e]};e.getBrand=function(t){return(0,e.getCommonConfigValue)("brand",t())};e.getBranchId=function(t){return(0,e.getCommonConfigValue)("branchId",t())};e.getWixHost=function(t){return(0,e.getCommonConfigValue)("host",t())}}}),N=d({"../../node_modules/@wix/web-bi-logger/dist/src/lib/event-defaults.js"(e){Object.defineProperty(e,"__esModule",{value:!0}),e.getCommonDefaults=e.getUserDefaults=e.transformDefaults=void 0;var t=C(),n=O(),o=L(),r=B();e.transformDefaults=function(e){return Object.keys(e).reduce((function(n,o){return n[t.EventContextMap[o]||o]=e[o],n}),{})};e.getUserDefaults=function(){return(0,r.getCookies)({clientId:"_wixCIDX",uuid:{name:"_wixUIDX",transform:function(e){return"string"==typeof e&&e.split("|")[1]}}})};e.getCommonDefaults=function(r,s){return(0,e.transformDefaults)({brandId:function(){return(0,o.getBrand)(r)},siteBranchId:function(){return(0,o.getBranchId)(r)},ms:function(){return Math.round((0,n.now)())},isHeadless:function(){return i(r)},hostingPlatform:function(){return(0,o.getWixHost)(r)},lv:t.LoggerVersion})};var i=function(e){return(0,o.getWixHost)(e)===t.HeadlessHost||void 0}}}),G=d({"../../node_modules/@wix/web-bi-logger/dist/src/logger.js"(e){var t=e&&e.__assign||function(){return t=Object.assign||function(e){for(var t,n=1,o=arguments.length;n<o;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},t.apply(this,arguments)},n=e&&e.__createBinding||(Object.create?function(e,t,n,o){void 0===o&&(o=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,o,r)}:function(e,t,n,o){void 0===o&&(o=n),e[o]=t[n]}),o=e&&e.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),r=e&&e.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)"default"!==r&&Object.prototype.hasOwnProperty.call(e,r)&&n(t,e,r);return o(t,e),t};Object.defineProperty(e,"__esModule",{value:!0}),e.BiLoggerClientFactory=e.createBatchQueue=e.manager=e.factory=e.Factory=e.setHost=e.EventCategories=e.PublishMethods=void 0;var i=j(),s=S(),u=r(H()),a=W(),c=L(),l=E(),f=I(),d=N(),h=I();Object.defineProperty(e,"PublishMethods",{enumerable:!0,get:function(){return h.PublishMethods}}),Object.defineProperty(e,"EventCategories",{enumerable:!0,get:function(){return h.EventCategories}});var p=E();Object.defineProperty(e,"setHost",{enumerable:!0,get:function(){return p.setHost}});var g=function(){function e(t){void 0===t&&(t={}),this.options=t,this.commonConfigGetter=function(){},this.initialized=!1,u.ok(!t.publishMethod||-1!==Object.keys(f.PublishMethods).map((function(e){return f.PublishMethods[e]})).indexOf(t.publishMethod),'Unsupported publish method "'.concat(t.publishMethod,'"')),e.consentPolicyAccessor=e.consentPolicyAccessor||new s.ConsentPolicyAccessor,this.loggerClientFactory=(0,i.factory)()}return e.prototype.initFactory=function(){var n=this;if(this.initialized)return this.loggerClientFactory;this.initialized=!0,this.updateDefaults((0,d.getCommonDefaults)(this.commonConfigGetter,e.consentPolicyAccessor)),this.withUserContext((0,d.getUserDefaults)());var o=t(t({},this.options),{host:this.options.host||function(){return(0,l.getHost)((0,c.getBrand)(n.commonConfigGetter))}}),r=(0,a.getPublisher)(o,this.publishFunctions),i=(0,a.getTransformers)(o,this.transformer),s=i.eventTransformer,u=i.payloadTransformer,f=this.loggerClientFactory.addPublisher(r).withConsentPolicyGetter((function(){return e.consentPolicyAccessor.getCurrentConsentPolicy()}));return s&&f.withEventTransformer(s),u&&f.withPayloadTransformer(u),f},e.prototype.withTransformer=function(e){return u.defined(e,"Transformer must be provided"),u.ok("function"==typeof e||e&&"function"==typeof e[f.PublishMethods.PostMessage],"Valid transformer must be provided"),this.transformer=e,this},e.prototype.withPublishFunction=function(e){return u.defined(e,"Publish functions object must be provided"),u.ok(this.options.publishMethod&&this.options.publishMethod!==f.PublishMethods.Auto,"Publish function can be used only when using a custom publish method"),u.ok(e&&"function"==typeof e[this.options.publishMethod],"Valid publish function must be provided"),this.publishFunctions=e,this},e.prototype.withUserContext=function(e){return u.defined(e,"User context object must be provided"),this.updateDefaults((0,d.transformDefaults)(e)),this},e.prototype.withUoUContext=function(e){return u.defined(e,"UoU context object must be provided"),this.updateDefaults((0,d.transformDefaults)(e)),this},e.prototype.withNonEssentialContext=function(e){return u.defined(e,"Non-essential context object must be provided"),this.loggerClientFactory.updateNonEssentialDefaults((0,d.transformDefaults)(e)),this},e.prototype.withCommonConfigGetter=function(e){return u.defined(e,"Common config getter must be provided"),u.ok("function"==typeof e,"Common config getter must be a function"),this.commonConfigGetter=e,this},e.prototype.updateDefaults=function(e){return this.loggerClientFactory.updateDefaults(e),this},e.prototype.setMuted=function(e){return this.loggerClientFactory.setMuted(e),this},e.prototype.setEvents=function(e){return this.loggerClientFactory.setEvents(e),this},e.prototype.setGlobalBatchQueue=function(e){return this.loggerClientFactory.setGlobalBatchQueue(e),this},e.prototype.onError=function(e){return this.loggerClientFactory.setPublisherFailHandler(e),this},e.prototype.logger=function(e){void 0===e&&(e={});var n=this.options,o=n.endpoint,r=n.useBatch;return this.initFactory().logger(t({endpoint:o,useBatch:r},e))},e}();e.Factory=g;e.factory=function(e){return void 0===e&&(e={}),new g(e)},e.manager=i.manager,e.createBatchQueue=i.createBatchQueue,e.BiLoggerClientFactory=i.BiLoggerFactory}}),z=d({"../../node_modules/@wix/web-bi-logger/dist/src/tracking-logger.js"(e){var t=e&&e.__createBinding||(Object.create?function(e,t,n,o){void 0===o&&(o=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,o,r)}:function(e,t,n,o){void 0===o&&(o=n),e[o]=t[n]}),n=e&&e.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=e&&e.__importStar||function(e){if(e&&e.__esModule)return e;var o={};if(null!=e)for(var r in e)"default"!==r&&Object.prototype.hasOwnProperty.call(e,r)&&t(o,e,r);return n(o,e),o};Object.defineProperty(e,"__esModule",{value:!0}),e.BiLoggerClientFactory=e.EventCategories=e.createBatchQueue=e.manager=e.setHost=e.factory=void 0;var r=j(),i=o(T()),s=o(G());e.factory=function(e){void 0===e&&(e={});var t=e.trackSession||void 0===e.trackSession;return t&&i.track(s.factory,{force:"force"===t}),s.factory(e)},e.setHost=s.setHost,e.manager=r.manager,e.createBatchQueue=r.createBatchQueue,e.EventCategories=s.EventCategories,e.BiLoggerClientFactory=s.BiLoggerClientFactory,e.default={factory:e.factory,setHost:s.setHost,manager:r.manager,createBatchQueue:r.createBatchQueue,EventCategories:s.EventCategories,BiLoggerClientFactory:s.BiLoggerClientFactory}}}),V=(o=z(),i=null!=o?s(l(o)):{},((e,t,n,o)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let r of c(t))f.call(e,r)||r===n||u(e,r,{get:()=>t[r],enumerable:!(o=a(t,r))||o.enumerable});return e})(!r&&o&&o.__esModule?i:u(i,"default",{value:o,enumerable:!0}),o)).default}}]);
//# sourceMappingURL=wix-ricos-bi-logger-fallback.chunk.min.js.map