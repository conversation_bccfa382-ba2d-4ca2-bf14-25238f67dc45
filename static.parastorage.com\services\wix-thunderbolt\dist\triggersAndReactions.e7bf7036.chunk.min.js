"use strict";(self.webpackJsonp__wix_thunderbolt_app=self.webpackJsonp__wix_thunderbolt_app||[]).push([[693],{36655:(t,o,r)=>{r.d(o,{f:()=>i});var n=r(17709),e=r.n(n);const s=(t,o)=>o?t.scrollLeft:t.scrollTop,l=(t,o,r)=>{const n=s(t,r),e=((t,o)=>o?t.scrollWidth:t.scrollHeight)(t,r),l=((t,o)=>{const r=t.getBoundingClientRect();return o?r.width:r.height})(t,r),i="forward"===o?1:-1,{minScrollPosition:c,maxScrollPosition:a}=((t,o,r)=>{const n=r?"left":"top",e=s(t,r);t.scrollTo({[n]:o,behavior:"instant"});const l=s(t,r);t.scrollTo({[n]:0,behavior:"instant"});const i=s(t,r);return t.scrollTo({[n]:e,behavior:"instant"}),{minScrollPosition:i,maxScrollPosition:l}})(t,e,r);if(Math.trunc(n)<=c&&"backward"===o)return e;if(Math.trunc(n)>=a&&"forward"===o)return 0;const h=n+i*l;return h+l>e?e:h<0?0:Math.round(h)},i=(t,o,r)=>{const n=t.containerProps?.overlowWrapperClassName||t.responsiveContainerProps?.overlowWrapperClassName;if(!n)return;const s=window.document.getElementById(o).getElementsByClassName(n)[0];e().measure((()=>{const t=(t=>{const o=t.getBoundingClientRect();return t.scrollWidth-o.width>t.scrollHeight-o.height})(s),o=l(s,r,t);e().mutate((()=>{s.scrollTo({[t?"left":"top"]:o,behavior:"smooth"})}))}))}}}]);
//# sourceMappingURL=https://static.parastorage.com/services/wix-thunderbolt/dist/triggersAndReactions.e7bf7036.chunk.min.js.map