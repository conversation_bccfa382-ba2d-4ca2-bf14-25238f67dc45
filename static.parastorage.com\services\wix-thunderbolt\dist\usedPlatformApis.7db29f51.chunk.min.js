"use strict";(self.webpackJsonp__wix_thunderbolt_app=self.webpackJsonp__wix_thunderbolt_app||[]).push([[7562],{61387:(t,e,s)=>{s.r(e),s.d(e,{UsedPlatformApisSymbol:()=>o.k,name:()=>o.U,site:()=>r});var a=s(77748);const d=(0,a.Og)([],(()=>({addUsedPlatformApi(t){false},getUsedPlatformApis:()=>JSON.parse(document.getElementById("used-platform-apis-data")?.textContent||"[]")})));var o=s(32939),p=s(32166);const l=(0,a.Og)([o.k],(t=>({getSdkHandlers:()=>({addUsedPlatformApi:t.addUsedPlatformApi,getUsedPlatformApis:t.getUsedPlatformApis})}))),r=t=>{t(o.k).to(d),t(p.H9).to(l)}}}]);
//# sourceMappingURL=https://static.parastorage.com/services/wix-thunderbolt/dist/usedPlatformApis.7db29f51.chunk.min.js.map