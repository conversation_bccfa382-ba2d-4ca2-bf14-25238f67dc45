"use strict";(self.webpackJsonp__wix_thunderbolt_app=self.webpackJsonp__wix_thunderbolt_app||[]).push([[2545],{81095:(e,t,n)=>{n.d(t,{createComponentsRegistryCSR:()=>p});var o=n(76904);var s=n(40983);const r=(0,n(8338).Iq)({host:"thunderbolt"});var i=n(56900);const a=(0,s.K)({host:"viewer"});async function p({runAndReport:e}){window.componentsRegistry&&await window.componentsRegistry.runtimeReady;const t=window.componentsRegistry?.runtime,n=window.viewerModel.componentsLibrariesTopology||[],s=function(){const e=window.viewerModel.experiments;return t=>(0,o.k)(t,e)}(),p=await e(a,(()=>r({options:{useScriptsInsteadOfEval:!0,usePartialManifests:!0},mode:"lazy",modes:{mobui:"eager"},libraries:[...t?.libraries||[],...n],isExperimentOpen:s,getSentryClient:(0,i.S)(s)})));let d=null;return{getComponentsLibrariesAPI:()=>({getComponents:()=>p.getComponentsLoaders(),getAllComponentsLoaders:async()=>(d||(d=p.ensureManifestsAreLoaded()),await d,p.getComponentsLoaders())}),getRegistryAPI:()=>p}}}}]);
//# sourceMappingURL=https://static.parastorage.com/services/wix-thunderbolt/dist/thunderbolt-components-registry.b89104dd.chunk.min.js.map