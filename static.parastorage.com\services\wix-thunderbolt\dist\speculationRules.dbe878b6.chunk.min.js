"use strict";(self.webpackJsonp__wix_thunderbolt_app=self.webpackJsonp__wix_thunderbolt_app||[]).push([[9497],{28843:(e,t,s)=>{s.r(t),s.d(t,{site:()=>o});var p=s(77748),a=s(20590),r=s(94715),i=s(87813),l=s(20069);const n=(0,p.Og)([(0,p.KT)(a.YG,l.U),r.wN,(0,p.lq)(i.z)],((e,t,s)=>({async appWillMount(){if(!e.isMpaSpeculationRulesEnabled)return;if(s?.isEligible()??!1){const e={prefetch:[{tag:"mpa-prefetch-moderate",where:{href_matches:"/*"},eagerness:"moderate"}]};t.setHead(`<script type="speculationrules">${JSON.stringify(e)}<\/script>`)}}})));var c=s(16537);const o=e=>{e(c.$.AppWillMountHandler).to(n)}}}]);
//# sourceMappingURL=https://static.parastorage.com/services/wix-thunderbolt/dist/speculationRules.dbe878b6.chunk.min.js.map