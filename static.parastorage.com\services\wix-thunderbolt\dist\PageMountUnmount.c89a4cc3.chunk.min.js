"use strict";(self.webpackJsonp__wix_thunderbolt_app=self.webpackJsonp__wix_thunderbolt_app||[]).push([[1663,9737],{65282:(t,i,e)=>{e.d(i,{EW:()=>y,HN:()=>h,QZ:()=>E,vP:()=>d});var n=Symbol.for("preact-signals");function r(){if(f>1)f--;else{for(var t,i=!1;void 0!==c;){var e=c;for(c=void 0,u++;void 0!==e;){var n=e.o;if(e.o=void 0,e.f&=-3,!(8&e.f)&&l(e))try{e.c()}catch(e){i||(t=e,i=!0)}e=n}}if(u=0,f--,i)throw t}}var o=void 0;function s(t){var i=o;o=void 0;try{return t()}finally{o=i}}var c=void 0,f=0,u=0,v=0;function a(t){if(void 0!==o){var i=t.n;if(void 0===i||i.t!==o)return i={i:0,S:t,p:o.s,n:void 0,t:o,e:void 0,x:void 0,r:i},void 0!==o.s&&(o.s.n=i),o.s=i,t.n=i,32&o.f&&t.S(i),i;if(-1===i.i)return i.i=0,void 0!==i.n&&(i.n.p=i.p,void 0!==i.p&&(i.p.n=i.n),i.p=o.s,i.n=void 0,o.s.n=i,o.s=i),i}}function h(t,i){this.v=t,this.i=0,this.n=void 0,this.t=void 0,this.W=null==i?void 0:i.watched,this.Z=null==i?void 0:i.unwatched}function d(t,i){return new h(t,i)}function l(t){for(var i=t.s;void 0!==i;i=i.n)if(i.S.i!==i.i||!i.S.h()||i.S.i!==i.i)return!0;return!1}function p(t){for(var i=t.s;void 0!==i;i=i.n){var e=i.S.n;if(void 0!==e&&(i.r=e),i.S.n=i,i.i=-1,void 0===i.n){t.s=i;break}}}function S(t){for(var i=t.s,e=void 0;void 0!==i;){var n=i.p;-1===i.i?(i.S.U(i),void 0!==n&&(n.n=i.n),void 0!==i.n&&(i.n.p=n)):e=i,i.S.n=i.r,void 0!==i.r&&(i.r=void 0),i=n}t.s=e}function g(t,i){h.call(this,void 0),this.x=t,this.s=void 0,this.g=v-1,this.f=4,this.W=null==i?void 0:i.watched,this.Z=null==i?void 0:i.unwatched}function y(t,i){return new g(t,i)}function w(t){var i=t.u;if(t.u=void 0,"function"==typeof i){f++;var e=o;o=void 0;try{i()}catch(i){throw t.f&=-2,t.f|=8,m(t),i}finally{o=e,r()}}}function m(t){for(var i=t.s;void 0!==i;i=i.n)i.S.U(i);t.x=void 0,t.s=void 0,w(t)}function b(t){if(o!==this)throw new Error("Out-of-order effect");S(this),o=t,this.f&=-2,8&this.f&&m(this),r()}function x(t){this.x=t,this.u=void 0,this.s=void 0,this.o=void 0,this.f=32}function E(t){var i=new x(t);try{i.c()}catch(t){throw i.d(),t}var e=i.d.bind(i);return e[Symbol.dispose]=e,e}h.prototype.brand=n,h.prototype.h=function(){return!0},h.prototype.S=function(t){var i=this,e=this.t;e!==t&&void 0===t.e&&(t.x=e,this.t=t,void 0!==e?e.e=t:s((function(){var t;null==(t=i.W)||t.call(i)})))},h.prototype.U=function(t){var i=this;if(void 0!==this.t){var e=t.e,n=t.x;void 0!==e&&(e.x=n,t.e=void 0),void 0!==n&&(n.e=e,t.x=void 0),t===this.t&&(this.t=n,void 0===n&&s((function(){var t;null==(t=i.Z)||t.call(i)})))}},h.prototype.subscribe=function(t){var i=this;return E((function(){var e=i.value,n=o;o=void 0;try{t(e)}finally{o=n}}))},h.prototype.valueOf=function(){return this.value},h.prototype.toString=function(){return this.value+""},h.prototype.toJSON=function(){return this.value},h.prototype.peek=function(){var t=o;o=void 0;try{return this.value}finally{o=t}},Object.defineProperty(h.prototype,"value",{get:function(){var t=a(this);return void 0!==t&&(t.i=this.i),this.v},set:function(t){if(t!==this.v){if(u>100)throw new Error("Cycle detected");this.v=t,this.i++,v++,f++;try{for(var i=this.t;void 0!==i;i=i.x)i.t.N()}finally{r()}}}}),g.prototype=new h,g.prototype.h=function(){if(this.f&=-3,1&this.f)return!1;if(32==(36&this.f))return!0;if(this.f&=-5,this.g===v)return!0;if(this.g=v,this.f|=1,this.i>0&&!l(this))return this.f&=-2,!0;var t=o;try{p(this),o=this;var i=this.x();(16&this.f||this.v!==i||0===this.i)&&(this.v=i,this.f&=-17,this.i++)}catch(t){this.v=t,this.f|=16,this.i++}return o=t,S(this),this.f&=-2,!0},g.prototype.S=function(t){if(void 0===this.t){this.f|=36;for(var i=this.s;void 0!==i;i=i.n)i.S.S(i)}h.prototype.S.call(this,t)},g.prototype.U=function(t){if(void 0!==this.t&&(h.prototype.U.call(this,t),void 0===this.t)){this.f&=-33;for(var i=this.s;void 0!==i;i=i.n)i.S.U(i)}},g.prototype.N=function(){if(!(2&this.f)){this.f|=6;for(var t=this.t;void 0!==t;t=t.x)t.t.N()}},Object.defineProperty(g.prototype,"value",{get:function(){if(1&this.f)throw new Error("Cycle detected");var t=a(this);if(this.h(),void 0!==t&&(t.i=this.i),16&this.f)throw this.v;return this.v}}),x.prototype.c=function(){var t=this.S();try{if(8&this.f)return;if(void 0===this.x)return;var i=this.x();"function"==typeof i&&(this.u=i)}finally{t()}},x.prototype.S=function(){if(1&this.f)throw new Error("Cycle detected");this.f|=1,this.f&=-9,w(this),p(this),f++;var t=o;return o=this,b.bind(this,t)},x.prototype.N=function(){2&this.f||(this.f|=2,this.o=c,c=this)},x.prototype.d=function(){this.f|=8,1&this.f||m(this)},x.prototype.dispose=function(){this.d()}},98319:(t,i,e)=>{
/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var n=e(41594);var r="function"==typeof Object.is?Object.is:function(t,i){return t===i&&(0!==t||1/t==1/i)||t!=t&&i!=i},o=n.useState,s=n.useEffect,c=n.useLayoutEffect,f=n.useDebugValue;function u(t){var i=t.getSnapshot;t=t.value;try{var e=i();return!r(t,e)}catch(t){return!0}}var v="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(t,i){return i()}:function(t,i){var e=i(),n=o({inst:{value:e,getSnapshot:i}}),r=n[0].inst,v=n[1];return c((function(){r.value=e,r.getSnapshot=i,u(r)&&v({inst:r})}),[t,e,i]),s((function(){return u(r)&&v({inst:r}),t((function(){u(r)&&v({inst:r})}))}),[t]),f(e),e};i.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:v},73634:(t,i,e)=>{t.exports=e(98319)},88353:(t,i,e)=>{e.d(i,{q:()=>n});const n=(0,e(16473).P)("core:signals")},16473:(t,i,e)=>{function n(t){return t}function r(t,i){return i}e.d(i,{P:()=>n,n:()=>r}),r.withConfig=function(){return function(t,i){return i}}},72666:(t,i,e)=>{e.d(i,{j:()=>n});const n=(0,e(16473).P)("core:provide-component")},50640:(t,i,e)=>{e.r(i),e.d(i,{ServicesManagerProvider:()=>M,WixServices:()=>_,useGetService:()=>N,useService:()=>W,useServices:()=>j});var n=e(65282),r=e(41594),o=e(73634),s=r.version.split(".").map(Number)[0],c=Symbol.for(s>=19?"react.transitional.element":"react.element");var f,u=Symbol.dispose||Symbol.for("Symbol.dispose");function v(t,i){var e=i.effect.S();return f=i,a.bind(i,t,e)}function a(t,i){i(),f=t}var h,d,l=function(){},p=((h={o:0,effect:{s:void 0,c:function(){},S:function(){return l},d:function(){}},subscribe:function(){return l},getSnapshot:function(){return 0},S:function(){},f:function(){}})[u]=function(){},h),S=Promise.prototype.then.bind(Promise.resolve());function g(){var t;d=void 0,null==(t=f)||t.f()}var y="undefined"!=typeof window?r.useLayoutEffect:r.useEffect;function w(t){void 0===t&&(t=0),d||(d=S(g));var i=(0,r.useRef)();null==i.current&&("undefined"==typeof window?i.current=p:i.current=function(t){var i,e,r,o,s=0,c=(0,n.QZ)((function(){e=this}));return e.c=function(){s=s+1|0,o&&o()},(i={o:t,effect:e,subscribe:function(t){return o=t,function(){s=s+1|0,o=void 0,c()}},getSnapshot:function(){return s},S:function(){if(null!=f){var t=f.o,i=this.o;0==t&&0==i||0==t&&1==i?(f.f(),r=v(void 0,this)):1==t&&0==i||2==t&&0==i||(r=v(f,this))}else r=v(void 0,this)},f:function(){var t=r;r=void 0,null==t||t()}})[u]=function(){this.f()},i}(t));var e=i.current;return(0,o.useSyncExternalStore)(e.subscribe,e.getSnapshot,e.getSnapshot),e.S(),0===t&&y(g),e}Object.defineProperties(n.HN.prototype,{$$typeof:{configurable:!0,value:c},type:{configurable:!0,value:function(t){var i=t.data,e=w(1);try{return i.value}finally{e.f()}}},props:{configurable:!0,get:function(){return{data:this}}},ref:{configurable:!0,value:null}}),globalThis.WixReactContext||(globalThis.WixReactContext=(0,r.createContext)(void 0));var m=globalThis.WixReactContext,b=e(72666),x=e(88353);const E=(0,e(16473).n)(x.q,(()=>({signal:t=>{const i=(0,n.vP)(t);return{get:()=>i.value,set:t=>i.value=t}},computed:t=>{const i=(0,n.EW)(t);return{get:()=>i.value}},effect:n.QZ})));function C(t,i){const e=P().addService(x.q,E),n=new Map([...t?t.registeredServices:[],...e.registeredServices].map((t=>[t.definition.toString(),t]))),r=new Map,o={getService(t){if(!r.has(t.toString())){const e=n.get(t.toString());if(!e){if(i)return i.getService(t);throw new Error(`Service ${t} is not provided`)}r.set(t.toString(),e.impl({config:e.config,getService:o.getService}))}return r.get(t.toString())},hasService:t=>n.has(t.toString()),addService(t,i,e){if(o.hasService(t))throw new Error(`Service ${t.toString()} is already provided`);n.set(t.toString(),{definition:t,impl:i,config:e||{}}),r.has(t.toString())||r.set(t.toString(),i({config:e||{},getService:o.getService}))},addServices(t){t.registeredServices.forEach((({definition:t})=>{if(o.hasService(t))throw new Error(`Service ${t.toString()} is already provided`)})),t.registeredServices.forEach((({definition:t,impl:i,config:e})=>{n.set(t.toString(),{definition:t,impl:i,config:e||{}})})),t.registeredServices.forEach((({definition:t,impl:i,config:e})=>{r.has(t.toString())||r.set(t.toString(),i({config:e||{},getService:o.getService}))}))}};return n.forEach((({definition:t,impl:i,config:e})=>{r.has(t.toString())||r.set(t,i({config:e||{},getService:o.getService}))})),o}function P(t=[]){return{registeredServices:t,addService:(i,e,n)=>P([...t,{definition:i,impl:e,config:n}])}}function M({servicesManager:t,children:i}){const{TopComponents:e,BottomComponents:n}=(()=>{const i=t.hasService(b.j)?t.getService(b.j):{getAllTopComponents:()=>null,getAllBottomComponents:()=>null};return{TopComponents:i?.getAllTopComponents(),BottomComponents:i?.getAllBottomComponents()}})();return r.createElement(m.Provider,{value:{getService:t.getService}},e&&r.createElement(e,null),i,n&&r.createElement(n,null))}function N(){const t=(0,r.useContext)(m);if(!t)throw new Error("No ServiceManagerProvider found in the component tree, make sure to wrap your app with ServicesManagerProvider");return t.getService}function W(t){const i=N();return function(t){w(t)}(),i(t)}function j(t){const i=N();if(!i)throw new Error("No ServiceManagerProvider found in the component tree, make sure to wrap your app with ServicesManagerProvider");return t.map((t=>i(t)))}const O=(0,r.createContext)(void 0);function _(t){const i=(0,r.useContext)(O),[e]=(0,r.useState)((()=>C(t.servicesMap,i)));return r.createElement(O.Provider,{value:e},r.createElement(M,{servicesManager:e},t.children))}}}]);
//# sourceMappingURL=https://static.parastorage.com/services/wix-thunderbolt/dist/PageMountUnmount.c89a4cc3.chunk.min.js.map