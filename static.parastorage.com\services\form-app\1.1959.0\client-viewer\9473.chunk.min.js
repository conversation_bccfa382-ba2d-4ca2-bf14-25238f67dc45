"use strict";(("undefined"!=typeof self?self:this).webpackJsonp__wix_form_app=("undefined"!=typeof self?self:this).webpackJsonp__wix_form_app||[]).push([[9473],{71081:(e,o,t)=>{t.r(o),t.d(o,{classes:()=>i,cssStates:()=>l,keyframes:()=>s,layers:()=>c,namespace:()=>r,st:()=>h,stVars:()=>a,style:()=>d,vars:()=>n});var r="oDurft5",i={root:"sSBZFAl",focusRing:"s__2pZzQY",container:"svtMMhH",core:"sXpkjM8",icon:"sYXMVXZ",label:"sZPBWtj",iconToolTip:"sc0r86E",errorIcon:"sAMMjEb",suffixed:"sgxLQXZ",suffix:"s__1h7hT0",disabled:"sUS_qyQ"},s={},c={},a={},n={"wix-color-1":"--wix-color-1","wix-color-8":"--wix-color-8","wix-color-5":"--wix-color-5","wix-color-4":"--wix-color-4","wix-color-29":"--wix-color-29","wix-ui-tpa-checkbox-text-color":"--wix-ui-tpa-checkbox-text-color","wix-ui-tpa-checkbox-border-color":"--wix-ui-tpa-checkbox-border-color","wix-ui-tpa-checkbox-icon-color":"--wix-ui-tpa-checkbox-icon-color","wix-ui-tpa-checkbox-hover-icon-color":"--wix-ui-tpa-checkbox-hover-icon-color","wix-ui-tpa-checkbox-disabled-icon-color":"--wix-ui-tpa-checkbox-disabled-icon-color","wix-ui-tpa-checkbox-disabled-box-border-color":"--wix-ui-tpa-checkbox-disabled-box-border-color","wix-ui-tpa-checkbox-box-border-color":"--wix-ui-tpa-checkbox-box-border-color","wix-ui-tpa-checkbox-box-border-radius":"--wix-ui-tpa-checkbox-box-border-radius","wix-ui-tpa-checkbox-box-color":"--wix-ui-tpa-checkbox-box-color","wix-ui-tpa-checkbox-label-font":"--wix-ui-tpa-checkbox-label-font","wix-ui-tpa-checkbox-disabled-label-color":"--wix-ui-tpa-checkbox-disabled-label-color","wix-ui-tpa-checkbox-error-color":"--wix-ui-tpa-checkbox-error-color","wix-ui-tpa-checkbox-error-message-min-height":"--wix-ui-tpa-checkbox-error-message-min-height","wix-ui-tpa-checkbox-input-main-border-opacity":"--wix-ui-tpa-checkbox-input-main-border-opacity","wix-ui-tpa-checkbox-input-hover-border-color":"--wix-ui-tpa-checkbox-input-hover-border-color","wix-ui-tpa-checkbox-input-background-color":"--wix-ui-tpa-checkbox-input-background-color","wix-ui-tpa-checkbox-input-size":"--wix-ui-tpa-checkbox-input-size","wix-ui-tpa-checkbox-input-background-opacity":"--wix-ui-tpa-checkbox-input-background-opacity","wix-ui-tpa-checkbox-input-hover-background-color":"--wix-ui-tpa-checkbox-input-hover-background-color","wix-ui-tpa-checkbox-input-hover-background-opacity":"--wix-ui-tpa-checkbox-input-hover-background-opacity","wix-ui-tpa-checkbox-input-checked-background-color":"--wix-ui-tpa-checkbox-input-checked-background-color","wix-ui-tpa-checkbox-input-checked-background-opacity":"--wix-ui-tpa-checkbox-input-checked-background-opacity","wix-ui-tpa-checkbox-input-disabled-background-color":"--wix-ui-tpa-checkbox-input-disabled-background-color","wix-ui-tpa-checkbox-input-disabled-background-opacity":"--wix-ui-tpa-checkbox-input-disabled-background-opacity","wix-ui-tpa-checkbox-hover-text-color":"--wix-ui-tpa-checkbox-hover-text-color","wix-ui-tpa-checkbox-input-border-width":"--wix-ui-tpa-checkbox-input-border-width","wix-ui-tpa-checkbox-input-border-radius":"--wix-ui-tpa-checkbox-input-border-radius","wix-ui-tpa-checkbox-input-hover-border-width":"--wix-ui-tpa-checkbox-input-hover-border-width","wix-ui-tpa-checkbox-input-hover-border-radius":"--wix-ui-tpa-checkbox-input-hover-border-radius","wut-error-color":"--wut-error-color","wix-ui-tpa-checkbox-label-font-line-height":"--wix-ui-tpa-checkbox-label-font-line-height","wix-ui-tpa-checkbox-margin-inline-end":"--wix-ui-tpa-checkbox-margin-inline-end","default-error-color":"--Checkbox3267277565-default-error-color",transparent:"--Checkbox3267277565-transparent","wix-ui-tpa-checkbox-box-color-opacity":"--Checkbox3267277565-wix-ui-tpa-checkbox-box-color-opacity"},l=t.stc.bind(null,r),d=t.sts.bind(null,r),h=d},7184:(e,o,t)=>{t.r(o),t.d(o,{classes:()=>i,cssStates:()=>l,keyframes:()=>s,layers:()=>c,namespace:()=>r,st:()=>h,stVars:()=>a,style:()=>d,vars:()=>n});var r="o__6II8Rx",i={root:"s__32TwL0",srOnly:"smnzlkd",box:"sekWQMC",nativeCheckbox:"s__6BZFbM",childContainer:"s__6azF8v"},s={},c={},a={},n={},l=t.stc.bind(null,r),d=t.sts.bind(null,r),h=d},49473:(e,o,t)=>{t.d(o,{s:()=>F});var r=t(55530),i=t.n(r),s=t(60751),c=t.n(s),a=t(1954),n=t(80211),l=t(8938);const d="SjtImx";var h={root:"shgWzNA",error:"sWERMBC",hover:"ss9_2Zr"};var u=t(37616),p=t(77940),b=t(55446),x=t(65054),k=t(86803),m=t(20779);const w="Jxfq8S",f="bGJcYi",g="UyG2QN",E="uWXF5t";var I=t(75075),_=t.n(I);const v=e=>{let{label:o,required:t,disabled:r,requiredIndicatorType:a=u.Tr.ASTERISK,requiredIndicatorPlacement:n=u.ak.AFTER_FIELD_TITLE,ricosTheme:l,requiredIndicatorClassName:d}=e;const h=(0,m.BA)(l),{t:p}=(0,k.Bd)(),I=function(e,o){switch(e){case u.Tr.ASTERISK:return"*";case u.Tr.TEXT:return o("settings.required-indicator-text");default:return""}}(a,p),v=(0,s.useMemo)((()=>{var e;if(!t)return o;const r=e=>({stringValue:e}),s={type:x.A.EXTERNAL,id:"",nodes:[],externalData:{fields:{"aria-hidden":r("true"),className:r(i()(g,d))}},textData:{text:I,decorations:[]}},c=_()(o);switch(n){case u.ak.BEFORE_FIELD_TITLE:const o=null==c?void 0:c.nodes.find(Boolean);o&&(o.nodes??=[],o.nodes.unshift(s));break;case u.ak.AFTER_FIELD_TITLE:const t=null==c||null==(e=c.nodes)?void 0:e.findLast(Boolean);t&&(t.nodes??=[],t.nodes.push(s))}return c}),[t,d,I,o,n]);return(0,s.useMemo)((()=>c().createElement(b.A,{content:v,className:i()({[w]:r,[E]:n===u.ak.BEFORE_FIELD_TITLE,[f]:n===u.ak.AFTER_FIELD_TITLE}),customTheme:h})),[r,n,v,h])},y={customStyles:{p:{fontSize:"var(--wix-forms-formInputValueFont-size, 16px)",fontFamily:"var(--wix-forms-formInputValueFont-family)",color:"rgb(var(--wix-forms-formInputValueColor, 0,0,0))"}}},T=e=>c().createElement(v,(0,p.A)({ricosTheme:y},e));var C=t(24703);const F=e=>{let{id:o,label:t,disabled:r=!1,value:p=!1,onChange:b,onFocus:x,onBlur:k,error:m,customErrors:w,fieldType:f,required:g,FieldLayout:E,requiredIndicatorType:I=u.Tr.ASTERISK,requiredIndicatorPlacement:_=u.ak.AFTER_FIELD_TITLE}=e;const{hasError:v,errorMessage:y}=(0,l.q)(m,w,f),F=(0,n.EG)().includes(n.bD.INPUT_FIELDS_HOVER),R=(0,s.useRef)(null),S=(0,s.useCallback)((e=>{null==b||b(e.checked)}),[b]);return(0,C.cj)((()=>{var e;null==(e=R.current)||e.focus({preventScroll:!0})}),o),c().createElement(E,{fieldId:o,renderLabel:()=>null,renderInput:()=>c().createElement("div",{onBlur:k,onFocus:x},c().createElement(a.S,{newErrorMessage:!0,className:i()(d,h.root,h.checkbox,{[h.error]:v,[h.hover]:F}),label:c().createElement(T,{label:t,disabled:r,required:g,requiredIndicatorType:I,requiredIndicatorPlacement:_}),checked:Boolean(p),onChange:S,disabled:r,error:v,errorMessage:y,required:g,showAsterisk:!1,ref:R})),renderDescription:()=>null})}},1954:(e,o,t)=>{t.d(o,{S:()=>y});var r=t(60751),i=t(55530),s=t.n(i),c=t(41278);const a=({size:e,...o})=>r.createElement("svg",{viewBox:"0 0 16 16",fill:"currentColor",width:e||"16",height:e||"16",...o},r.createElement("path",{d:"M4 7h8v2H4z"}));a.displayName="IndeterminateXSmall";var n=a;const l=({size:e,...o})=>r.createElement("svg",{viewBox:"0 0 16 16",fill:"currentColor",width:e||"16",height:e||"16",...o},r.createElement("path",{fillRule:"evenodd",d:"M2.58594 8.13691L6.78515 12.3361L13.4144 5.70691L11.2932 3.58569L6.78515 8.09369L4.70715 6.01569L2.58594 8.13691Z",clipRule:"evenodd"}));l.displayName="CheckXBold";var d=l;const h=({size:e,...o})=>r.createElement("svg",{viewBox:"0 0 16 16",fill:"currentColor",width:e||"16",height:e||"16",...o},r.createElement("path",{fillRule:"evenodd",d:"M6.7847,10.9609 L6.07759322,10.2537932 L6.0777,10.2537932 L3.9997,8.1759 L4.70680678,7.46879322 L6.7847,9.54679322 L11.2925932,5.03879322 L11.9997,5.7459 L6.7847,10.9609 Z"}));h.displayName="CheckXSmall";var u=h;const p=({size:e,...o})=>r.createElement("svg",{viewBox:"0 0 16 16",fill:"currentColor",width:e||"16",height:e||"16",...o},r.createElement("path",{fillRule:"evenodd",d:"M11.2931 4.29297L12.7072 5.70708L6.78508 11.6292L3.29297 8.13708L4.70707 6.72297L6.78508 8.80097L11.2931 4.29297Z",clipRule:"evenodd"}));p.displayName="CheckXMedium";var b=p,x=t(50197),k=t(71081),m=t(88787),w=t(14507),f=t(46973),g=t(50815),E=t(18061),I=t(68403),_=t(99528),v=t(96616);class y extends r.Component{constructor(e){super(e),this.inputRef=r.createRef(),this._rootRef=r.createRef(),this._errorIconRef=r.createRef(),this.state={focused:!1,isErrorTooltipShown:!1,errorIconClicked:!1,isWithinRoot:!1},this.focus=e=>{this.inputRef.current&&(this.inputRef.current.focus(e),this.setState({focused:!0}))},this.showErrorTooltip=()=>{this._toggleTooltip(!0)},this.hideErrorTooltip=()=>{this._toggleTooltip(!1)},this._renderIcon=()=>{const{checked:e,indeterminate:o}=this.props,t=this.getIcon();return r.createElement("span",{className:s()(k.classes.icon),"data-hook":x.sp.IconWrapper},e?r.createElement(t,null):o?r.createElement(n,null):"")},this._onRootFocus=()=>{const{isWithinRoot:e,errorIconClicked:o}=this.state;e||o||(this.setState({isWithinRoot:!0}),this._toggleTooltip(!0))},this._onRootKeyDown=e=>{const{id:o}=document.activeElement||{};document.activeElement&&(o===this._errorIconId&&this._wasTabClicked(e)||o===this._checkboxId&&this._wasShiftTabClicked(e))&&(this._toggleTooltip(!1),this.setState({isWithinRoot:!1}))},this._wasShiftTabClicked=e=>e.shiftKey&&"Tab"===e.key,this._wasTabClicked=e=>!e.shiftKey&&"Tab"===e.key,this._onFocus=()=>{this.setState({focused:!0})},this._onBlur=()=>{this.setState({focused:!1})},this._toggleTooltip=e=>{this.setState({isErrorTooltipShown:e})},this._onErrorIconMouseDown=()=>{this.setState({errorIconClicked:!0})},this._onErrorIconClick=()=>{const{isErrorTooltipShown:e}=this.state;this._toggleTooltip(!e),this.setState({errorIconClicked:!1})},this._onClickOutside=()=>{this._rootRef.current&&!this._rootRef.current.contains(document.activeElement)&&(this._toggleTooltip(!1),this.setState({isWithinRoot:!1}))},this._getErrorSuffix=()=>{const{isErrorTooltipShown:e}=this.state,{errorMessage:o}=this.props;return r.createElement("div",{className:k.classes.iconToolTip},r.createElement(m.F,{"data-hook":x.sp.ErrorIcon,message:o,icon:r.createElement(w.A,{className:k.classes.errorIcon}),tooltipId:this._errorMessageId,tooltipSkin:f.j.Error,showTooltip:e,onMouseDown:this._onErrorIconMouseDown,onClick:this._onErrorIconClick,onClickOutside:this._onClickOutside,contentRef:this._errorIconRef,id:this._errorIconId}))},this.getIcon=()=>{const{checkmarkStyle:e}=this.props;return e===x.zs.Bold?d:e===x.zs.Regular?u:b},this._errorMessageId=(0,I.A)("error-message"),this._errorIconId=(0,I.A)("error-icon"),this._checkboxId=(0,I.A)("checkbox")}getDataAttributes(){const{disabled:e,error:o,indeterminate:t,checked:r}=this.props,{focused:i}=this.state;return{[x.YF.Disabled]:e,[x.YF.Error]:o,[x.YF.Indeterminate]:t,[x.YF.Checked]:r,[x.YF.Focused]:i}}render(){const{theme:e,checked:o,disabled:t,label:i,error:a,errorAppearance:n,errorMessage:l,newErrorMessage:d,indeterminate:h,onChange:u,name:p,suffix:b,className:m,"aria-describedby":w,"aria-label":f,required:I,showAsterisk:_=!0,verticalAlignment:y=x.D8.Center,lang:T}=this.props,{mobile:C}=this.context,F=this._renderIcon(),R=a&&l&&!d,S=d?g.B:r.Fragment,L="box"===e,M={error:a,newErrorMessage:d,box:L,disabled:t,checked:!!o},B=l&&l.length>0,D=I&&_,N=(0,k.st)(k.classes.root,{disabled:t,error:a,newErrorMessage:d,checked:!!o,box:L},m),A=k.classes.container,q=d?A:N,z=(0,E.g)([w,l?this._errorMessageId:""]);return r.createElement(S,{...d&&{visible:a&&B,message:l,messageId:this._errorMessageId,className:(0,k.st)(k.classes.root,M,m),"data-hook":this.props["data-hook"],errorAppearance:n,lang:T}},r.createElement("div",{className:(0,k.st)(q,!C&&this.state.focused&&k.classes.focused),"data-hook":this.props["data-hook"],ref:this._rootRef,onFocus:this._onRootFocus,onKeyDown:this._onRootKeyDown,...this.getDataAttributes(),...d?{}:{lang:T}},r.createElement(c.J,{className:(0,k.st)(k.classes.core,{verticalAlignment:y}),checkedIcon:F,uncheckedIcon:F,indeterminateIcon:F,indeterminate:h,checked:o,onChange:u,name:p,disabled:t,ref:this.inputRef,onFocusByKeyboard:this._onFocus,onBlur:this._onBlur,"data-hook":x.sp.CheckboxCore,"aria-invalid":a,"aria-describedby":z,"aria-label":f,id:this._checkboxId,required:I},r.createElement("div",{"data-hook":x.sp.LabelWrapper,className:s()(k.classes.label,{[k.classes.suffixed]:!!b})},i,D&&r.createElement(v.n,null)),b&&r.createElement("div",{className:`${k.classes.label} ${k.classes.suffix}`},b),R?this._getErrorSuffix():null)))}}y.displayName="Checkbox",y.defaultProps={checked:!1,disabled:!1,label:"",error:!1,errorMessage:"",indeterminate:!1,theme:x.pu.Default,"data-hook":x.sp.CheckboxWrapper},y.contextType=_.rs},50197:(e,o,t)=>{var r,i,s,c,a;t.d(o,{D8:()=>i,YF:()=>a,pu:()=>r,sp:()=>c,zs:()=>s}),function(e){e.Default="default",e.Box="box"}(r||(r={})),function(e){e.Center="center",e.Top="top"}(i||(i={})),function(e){e.Regular="regular",e.Medium="medium",e.Bold="bold"}(s||(s={})),function(e){e.CheckboxWrapper="checkbox-wrapper",e.IconWrapper="icon-wrapper",e.LabelWrapper="label-wrapper",e.CheckboxCore="checkbox-core",e.ErrorIcon="error-icon"}(c||(c={})),function(e){e.Error="data-error",e.Disabled="data-disabled",e.Checked="data-checked",e.Indeterminate="data-indeterminate",e.Focused="data-focused"}(a||(a={}))},96616:(e,o,t)=>{t.d(o,{n:()=>c});var r=t(60751),i=t.n(r),s="sqq2p13";const c=({dataHook:e,...o})=>i().createElement("span",{className:s,"aria-hidden":"true","data-hook":e,...o},"*")},41278:(e,o,t)=>{t.d(o,{J:()=>a});var r=t(60751),i=t(7184),s=t(216);const c=()=>null;class a extends r.Component{constructor(){super(...arguments),this.focusedByMouse=!1,this.state={isFocused:!1,focusVisible:!1},this.handleMouseDown=e=>{this.props.disabled||(e.preventDefault(),this.focusedByMouse=!0,this.setState({isFocused:!0}))},this.handleInputKeyDown=()=>{this.setState({focusVisible:!0})},this.handleInputBlur=e=>{this.state.isFocused&&this.setState({isFocused:!1,focusVisible:!1}),this.focusedByMouse=!1,this.props.onBlur&&this.props.onBlur(e)},this.handleInputFocus=e=>{!this.focusedByMouse&&this.props.onFocusByKeyboard&&this.props.onFocusByKeyboard(e),this.setState({isFocused:!0,focusVisible:!this.focusedByMouse})},this.handleChange=e=>{this.props.onChange({checked:!this.props.checked,...e})}}focus(e){this.checkbox?.focus(e)}render(){const{checked:e,disabled:o,error:t,indeterminate:c,indeterminateIcon:a,checkedIcon:n,uncheckedIcon:l}=this.props;return r.createElement("label",{className:(0,i.st)(i.classes.root,{checked:e,disabled:o,readonly:this.props.readOnly,error:t,indeterminate:c,focus:this.state.isFocused,"focus-visible":this.state.focusVisible},this.props.className),onMouseDown:this.handleMouseDown,...(0,s.r)(this.props)},r.createElement("input",{type:"checkbox",className:i.classes.nativeCheckbox,onClick:e=>e.stopPropagation(),onChange:this.handleChange,onKeyDown:this.handleInputKeyDown,onFocus:this.handleInputFocus,onBlur:this.handleInputBlur,ref:e=>this.checkbox=e,checked:e,disabled:o,readOnly:this.props.readOnly,tabIndex:this.props.tabIndex,id:this.props.id,required:this.props.required,autoFocus:this.props.autoFocus,name:this.props.name,"aria-controls":this.props["aria-controls"],"aria-label":this.props["aria-label"],"aria-invalid":this.props["aria-invalid"],"aria-describedby":this.props["aria-describedby"]}),r.createElement("span",{className:i.classes.box},this.props.indeterminate?a:this.props.checked?n:l),this.props.children?r.createElement("div",{className:i.classes.childContainer},this.props.children):null)}}a.displayName="CoreCheckbox",a.defaultProps={onChange:c,checked:!1,indeterminate:!1,tabIndex:0,onBlur:c}}}]);
//# sourceMappingURL=9473.chunk.min.js.map