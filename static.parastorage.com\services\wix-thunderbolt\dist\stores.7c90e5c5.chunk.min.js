"use strict";(self.webpackJsonp__wix_thunderbolt_app=self.webpackJsonp__wix_thunderbolt_app||[]).push([[5221],{89648:(e,t,n)=>{n.d(t,{E$:()=>A,_3:()=>g,P2:()=>b});class r{constructor(e){this.enqueueIndex=0,this.dequeueIndex=0,this.initialQueueSize=0,this.queue=new Array(e),this.initialQueueSize=e}enqueue(e){this.enqueueIndex<this.initialQueueSize?this.queue[this.enqueueIndex++]=e:++this.enqueueIndex&&this.queue.push(e)}dequeue(){const e=this.queue[this.dequeueIndex++];return this.dequeueIndex===this.enqueueIndex&&(this.enqueueIndex=0,this.dequeueIndex=0),e}isEmpty(){return 0===this.enqueueIndex}}const o="~~",s=16,i=Symbol.for("EMPTY_SCHEMA"),u=(e,t)=>{let n=e;for(const e of t)if(n=n[e],void 0===n)return;return n},d=(e,t,n)=>{let r=e,o=0;for(;o<t.length-1;o++)r[t[o]]=r[t[o]]||{},r=r[t[o]];r[t[o]]=n},a=(e,t,n,r)=>e[t]?.[n]?.[r],l=(e,t,n,r,o)=>{e[t]=e[t]||{},e[t][n]=e[t][n]||{},e[t][n][r]=o},c=([e,t,n])=>f(e,t,n),f=(e,t,n)=>`${e}${o}${t}${o}${n}`,h=e=>e.split(o),p=(e,t,n)=>{const o=new r(n);for(o.enqueue({path:[],val:e});!o.isEmpty();){const e=o.dequeue();if(!t(e.val,e.path)){const t=typeof e.val;if(!e.val||"string"===t||"number"===t||"boolean"===t||"symbol"===t||"function"===t)continue;if(Array.isArray(e.val))for(let t=0;t<e.val.length;t++)o.enqueue({path:[...e.path,t],val:e.val[t]});else{const t=Object.keys(e.val);for(const n of t)o.enqueue({path:[...e.path,n],val:e.val[n]})}}}},x=e=>"object"==typeof e&&null!==e&&e.hasOwnProperty("$type")&&"ref"===e.$type,v=e=>{if(x(e))return e;if(t=e,!Array.isArray(t)&&"object"!=typeof t)return i;var t;let n;return p(e,((e,t)=>Array.isArray(e)?(n=n||{},d(n,t,[]),!1):x(e)?(n=n||{},d(n,t,e),!0):void 0),s),n};class y{constructor(e){this.dependencyIndex=e,this.schemas={}}addSchemaToIndex(e,t){p(e,(e=>{if(x(e))return this.dependencyIndex.addRefToIndex(e,t),!0}),s)}removeSchemaFromIndex(e,t){e!==i&&p(e,(e=>this.dependencyIndex.removeRefFromIndex(e,t)),s)}}const I="PERMANENT",m="TEMPORARY",w=(e,t,n,r)=>{const o=t.get(e);if(o!==I){if(o===m)throw new Error("Cyclic dependency");if(t.set(e,m),n.has(e))for(const o of n.get(e))w(o,t,n,r);t.set(e,I),r(e)}};class q{constructor(){this.index=new Map}addRefToIndex(e,t){const n=c(e.refPath);this.index.has(n)||this.index.set(n,new Set),this.index.get(n).add(t)}removeRefFromIndex(e,t){if(x(e)){const n=c(e.refPath);return this.index.get(n)?.delete(t),!0}}toposort(e){return((e,t)=>{const n=new Map,r=new Array(e.size);let o=e.size;const s=e=>{--o,r[o]=e};for(const r of e)w(r,n,t,s);return r})(e,this.index)}getAllInvalidations(e){return((e,t)=>{const n=new Map,r=new Set,o=e=>{r.add(e)};for(const r of e)w(r,n,t,o);return r})(e,this.index)}clean(){for(const[e]of this.index.entries()){const t=this.index.get(e);t?.clear()}this.index.clear()}}const A=({transform:e}={})=>{let t={},n={},r={},o=new Set,c=new q,I=new y(c),m=!0;const w=(e,n,r,s)=>{o.add(f(e,n,r)),void 0!==s?l(t,e,n,r,s):delete t[e]?.[n]?.[r]},A=(e,t,n,o)=>{const u=f(e,t,n),h=a(r,e,t,n);if(o===i||x(o))return l(r,e,t,n,o),I.removeSchemaFromIndex(h,u),void(o!==i&&c.addRefToIndex(o,u));if(!h||h===i)return l(r,e,t,n,o),void I.addSchemaToIndex(o,u);let v;p(h,(e=>c.removeRefFromIndex(e,u)),s),p(o,((e,t)=>{if(x(e))return v=v||h||{},d(v,t,e),c.addRefToIndex(e,u),!0}),s),(v||h)&&l(r,e,t,n,v||{})},g=(f=(e=>e()))=>{const x=f((()=>((o,f=(e=>e()))=>{const x=f((()=>m?o:c.getAllInvalidations(o)),"getAllInvalidations"),v=f((()=>c.toposort(x).map(h)),"toposort");return f((()=>{for(const o of v){const[c,f,h]=o;if(!t[c]?.[f])continue;const x=a(t,c,f,h),v=a(r,c,f,h);if(v===i){l(n,c,f,h,e?e(x,o):x);continue}let y={};p(x,((e,t)=>{const r=u(v,t);if(!r)return d(y,t,e),!0;if(r.hasOwnProperty("$type")){const e=u(n,r.refPath)??r.defaultValue;return t.length>0?d(y,t,e):y=e,!0}Array.isArray(e)&&d(y,t,[])}),s),l(n,c,f,h,e?e(y,o):y)}}),"traverse path"),m=!1,v})(o,f)),"populate");return f((()=>o.clear()),"pendingInvalidations.clear()"),x},b=(e,o,s,u,d=v(u))=>{if(void 0===s){if(!t[e]?.[o])return;for(const n in t[e][o])A(e,o,n,i),w(e,o,n,void 0);return delete r[e]?.[o],delete t[e]?.[o],void delete n[e]?.[o]}A(e,o,s,d??i),w(e,o,s,u)};function S(e){for(const t in e){for(const n in e[t]){for(const r in e[t][n])delete e[t][n][r];delete e[t][n]}delete e[t]}}return{cleanAll(){S(t),t={},S(n),n={},S(r),r={},o&&o.clear(),o=new Set,c&&c.clean(),c=new q,I=new y(c)},update:(e,t,n,r,o)=>(b(e,t,n,r,o),g()),batch:(e,t=(e=>e()))=>(e(b),t((()=>g(t)),"flush")),get:e=>u(n,Array.isArray(e)?e:e.split("."))}},g=(e,t)=>{if(e.length<3)throw new Error("Unsupported ref path, must be 3 or more levels deep");return{$type:"ref",refPath:e,defaultValue:t}};function b(e){return(t,n)=>g([...e,...t],n)}}}]);
//# sourceMappingURL=https://static.parastorage.com/services/wix-thunderbolt/dist/stores.7c90e5c5.chunk.min.js.map