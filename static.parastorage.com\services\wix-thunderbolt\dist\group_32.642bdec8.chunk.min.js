"use strict";(self.webpackJsonp__wix_thunderbolt_app=self.webpackJsonp__wix_thunderbolt_app||[]).push([[8908],{13396:(e,t,n)=>{n.r(t),n.d(t,{PageTransitionsSymbol:()=>r.e$,editor:()=>B,editorPage:()=>S,page:()=>v});var o=n(77748),i=n(20590),s=n(39218),a=n(32166),r=n(86046),d=n(41596);const l=(0,o.Og)([(0,o.KT)(i.Gp,r.UU),(0,o.KT)(i.wk,r.UU),s.dB,d.s,a.RV],((e,t,n,o,i)=>{const s=!!i&&"startViewTransition"in i.document&&e.shouldUseViewTransition;return{name:"pageTransitions",pageDidMount(o){const i=t.get(),s=i?.mountDoneHandler;s&&s({transitionName:e.transitionName,pageId:o}),(i?.isFirstMount??1)&&n.notifyPageTransitionsCompleted(o),t.update((e=>({...e,isFirstMount:!1})))},pageWillUnmount({contextId:e}){const a=t.get();if(s){const e=()=>{o.getScrollYHistoryState()||o.scrollToTop()},s=e=>{n.notifyPageTransitionsCompleted(e),o.getScrollYHistoryState()&&o.restoreScrollPosition()};!function(e,t,n,o){let i,s;t.document.documentElement.dataset.viewTransition="page-transition";const a=document.startViewTransition((()=>e.then((e=>{s=e.transitionName,i=e.pageId,t.document.documentElement.dataset.pageTransition=s}))));a.ready.then(n),a.finished.then((()=>{delete t.document.documentElement.dataset.viewTransition,o(i)}))}(new Promise((e=>{t.update((t=>({...t,mountDoneHandler:e})))})),i,e,s)}a?.propsUpdateListenersUnsubscribers?.[e]?.(),t.update((t=>{const n=t?.propsUpdateListenersUnsubscribers??{};return delete n[e],{...t,propsUpdateListenersUnsubscribers:n}}))}}})),p=(0,o.Og)([(0,o.KT)(i.wk,r.UU)],(e=>({disableNextTransition:()=>e.update((e=>({...e,nextTransitionEnabled:!1})))}))),c=(0,o.Og)([(0,o.KT)(i.Gp,r.UU)],(e=>{let t=[];return{hasTransition:"none"!==e.transitionName,onPageTransitionsCompleted:e=>{t.push(e)},notifyPageTransitionsCompleted:e=>{t.forEach((t=>t(e))),t=[]}}}));var u=n(16537),g=n(20636),m=n(87711),T=n(10553);const U=(0,o.Og)([(0,o.KT)(i.Gp,r.UU),m.Ji,s.dB,d.s,(0,o.KT)(i.wk,r.UU),a.RV,T.n],((e,t,n,o,i,s,a)=>({componentTypes:["Page"],componentWillMount(r){const d=i.get(),l=!d||d.nextTransitionEnabled,p=!d||d.isFirstMount,c=!!s&&"startViewTransition"in s.document&&e.shouldUseViewTransition,u=r.id;t.update({SITE_PAGES:{transitionEnabled:l,...c?{}:{onTransitionStarting:()=>{if(o.getScrollYHistoryState()||o.scrollToTop(a["specs.thunderbolt.pageTransitionScrollSmoothly"]?"smooth":void 0),a["specs.thunderbolt.postTransitionElementFocus"]){const e=window.document.querySelector("main section");e?.focus({preventScroll:!0})}},onTransitionComplete:()=>{n.notifyPageTransitionsCompleted(u),o.getScrollYHistoryState()&&o.restoreScrollPosition()}}}}),i.update((()=>({...d,isFirstMount:p,nextTransitionEnabled:!0})))}})));var y=n(62155),b=n.n(y),f=n(57188);const P=["video.videoInfo.videoId","image.uri","image.link.href","image.displayMode","backgroundImage.uri","backgroundImage.link.href","backgroundImage.displayMode"],h=["type","alignType","fittingType","scrollType","colorOverlay","colorOverlayOpacity","color","opacity"],k=(e,t,n,o)=>o.every((n=>(e&&e[n])===(t&&t[n])))&&n.every((i=>!e&&!t||k(e&&e[i],t&&t[i],n,o))),w=(0,o.Og)([(0,o.KT)(i.Gp,r.UU),m.Ji,(0,o.KT)(i.wk,r.UU),s.Is,s.DR,m.eZ,T.n],((e,t,n,o,i,s,a)=>{const r=(0,f.P)(o),d=(i,d)=>{n.update((t=>({...t,pageBackgroundProp:i,pageBackground:e.pageBackground})));const l=a["specs.thunderbolt.pageBGTransitionHandler"],p=n.get()?.lastRenderedPageBgId,c=!p||s.get(p),u=(n.get()?.nextTransitionEnabled??!0)&&d;let g;if(l){const t=e.isEditor&&!e.isPreview;g=u||t&&!c}else g=u||!c;g&&n.update((e=>({...e,lastRenderedPageBgId:r}))),t.update({BACKGROUND_GROUP:{key:`BACKGROUND_GROUP_${e.viewMode}`,transitionEnabled:g,className:`backgroundGroup_${o}`}})},l=t=>{const o=n.get(),i=o?.pageBackgroundProp,s=e.pageBackground,a=o?.pageBackground,r=!((e,t)=>{if(!t||!e)return!1;const n=t.mediaRef,o=n&&n.type,i=e.mediaRef,s=i&&i.type,a=!n&&!i,r=a||o===s;let d=[...h];if("WixVideo"===o&&r){const e=d.indexOf("color");d.splice(e,1)}else a&&(d=["color"]);return r&&k(t,e,["mediaRef","imageOverlay"],d)})(s,a);return d=t,!((l=i)&&d&&P.every((e=>b().get(l,e)===b().get(d,e))))||r;var d,l};if(t.get(r)){const e=t.get(r).fillLayers,n=l(e);d(e,n)}const p=t.subscribeToChanges((e=>{if(r in e){const t=e[r]?.fillLayers;l(t)&&d(t,!0)}}));return n.update((e=>{const t=e?.propsUpdateListenersUnsubscribers??{};return t[i]=p,{...e||{},propsUpdateListenersUnsubscribers:t}})),{componentTypes:["PageBackground"],componentWillMount(){const e=n.get(),t=e?.isFirstMount??!0;n.update((()=>({...e,isFirstMount:t})))}}})),v=e=>{e(s.dB).to(c),e(r.e$).to(p),e(u.$.PageDidMountHandler,u.$.PageWillUnmountHandler).to(l),e(g.ls).to(U),e(g.ls).to(w)},S=e=>{e(u.$.PageDidMountHandler,u.$.PageWillUnmountHandler).to(l),e(g.ls).to(U),e(g.ls).to(w)},B=e=>{e(r.e$).to(p),e(s.dB).to(c)}}}]);
//# sourceMappingURL=https://static.parastorage.com/services/wix-thunderbolt/dist/group_32.642bdec8.chunk.min.js.map