"use strict";(self.webpackJsonp__wix_thunderbolt_app=self.webpackJsonp__wix_thunderbolt_app||[]).push([[3909],{57111:(e,t,n)=>{n.r(t),n.d(t,{AnimationManager:()=>h,MotionSymbol:()=>l.h,animationApiFactory:()=>v,name:()=>l.U,page:()=>M});var s=n(77748),i=n(20590),r=n(39218),a=n(87711),o=n(32166),c=n(54157),l=n(59058),d=n(82658),u=n(82634);class h{constructor(e,t,n){this.api=e,this.isResponsive=n.isResponsive,this.isReducedMotion=n.reducedMotion,this.useCSSTime=n.useCSSTime||!1,this.viewportManager=n.reducedMotion?null:t({manager:this}),this.animationData={},this.scrubManagers=[],this.scrubTriggers={},this.callbacks={},this.breakpointRanges=[],this.activeListeners=[],this.scrubUpdateRequest=null,this.breakpointChangeHandler=this._breakpointChangeHandler.bind(this),this.disabledPointerScenes={},this.played={}}init(e,t){this.animationData=e,this.breakpointRanges=t,this.scrubUpdateRequest=null,this._observeBreakpointChange()}addExternalAnimationData(e,t,n){Object.assign(this.animationData,e);const s=Object.keys(t).filter((e=>"pointer-move"!==t[e].trigger)).reduce(((e,n)=>(e[n]=t[n],e)),{});this.trigger({scrub:n?t:s})}trigger(e={},t){if(e.scrub)return void(t?(this.scrubTriggers=e.scrub,this.scrubUpdateRequest||(this.scrubUpdateRequest=setTimeout((()=>{this._updateScrubManagers(this.scrubTriggers,!0),this.scrubUpdateRequest=null}),0))):(Object.assign(this.scrubTriggers,e.scrub),this._updateScrubManagers(e.scrub)));const n=this.isResponsive?window.innerWidth:0;e.play?.length&&e.play.forEach((({effectId:e,targetId:t,toggle:s})=>{const i=this._getEffectVariationForCurrentBreakpoint(e,n);(i?.namedEffect||i?.customEffect)&&this._playAnimation(i,e,{targetId:t,toggle:s})})),e.resume?.length&&e.resume.forEach((({effectId:e,targetId:t})=>{if(this.disabledPointerScenes[e])return void this.disabledPointerScenes[e].forEach((e=>e.disabled=!1));const s=this._getEffectVariationForCurrentBreakpoint(e,n);(s?.namedEffect||s?.customEffect)&&this._resumeOrPlayAnimation(s,e,{targetId:t})})),e.hold?.length&&e.hold.forEach((({effectId:e,targetId:t})=>{if(this.disabledPointerScenes[e])return void this.disabledPointerScenes[e].forEach((e=>e.disabled=!0));const s=this._getEffectVariationForCurrentBreakpoint(e,n);(s?.namedEffect||s?.customEffect)&&this._pauseAnimation(t,s)}))}cancelAll(){this.api.cancelScrub(this.scrubManagers),this.api.cancelAll(),this.scrubTriggers={},this.played={},this.clear()}clear(){this.animationData={},this.activeListeners.forEach((e=>e.removeEventListener("change",this.breakpointChangeHandler))),this.activeListeners.length=0,this.disabledPointerScenes={},this.viewportManager?.disconnect()}addEffectCallback(e,t,n){const s="animation-end"===t?"end":"start";this.callbacks[e]||(this.callbacks[e]={end:[],start:[]}),this.callbacks[e][s].push(n)}clearEffectCallbacks(e){delete this.callbacks[e]}_updateScrubManagers(e={},t=!1){this.scrubManagers.length&&t&&(this.scrubManagers.forEach((e=>e.destroy())),this.scrubManagers.length=0);const n=Object.keys(e),s=this.isResponsive?window.innerWidth:0,i={};for(const e of n){const t=this._getEffectVariationForCurrentBreakpoint(e,s);"ScrubAnimationOptions"===t?.type&&(i[e]=t)}this.scrubManagers.push(...this.api.startScrub(e,i)),this.scrubManagers.forEach((e=>{e instanceof u.g&&e.config.scenes.forEach((e=>{if(e.target&&e.centeredToTarget&&e.isHitAreaRoot){const t=e.target.closest("[data-block-level-container]"),n=e.effectId;t?this.viewportManager&&n&&(this.disabledPointerScenes[n]||(this.disabledPointerScenes[n]=[]),this.disabledPointerScenes[n].push(e),this.viewportManager.observe(t,{effectId:n,targetId:e.target.id})):e.disabled=!1}}))}))}_getEffectVariationForCurrentBreakpoint(e,t){const n=this.animationData[e];if(!n)return;const s=n.find((e=>!e.variants?.length));return t&&n.findLast((e=>e.variants?.some((e=>!(e.max&&e.max<t)&&!(e.min&&e.min>t)))))||s}_playAnimation(e,t,n={}){const s={...e,...n,effectId:t},{targetId:i,iterations:r,allowReplay:a}=s;if(0===r)return void this._setAnimationPlaystateTrigger(t,i);if("never"===a&&this.played[i])return void this._setAnimationState(i);const o=this._getAnimationCallbacks(t,i,s);this.useCSSTime?this.api.playCSS(i,s,o):this.api.play(i,s,o),this.played[i]=!0}_resumeOrPlayAnimation(e,t,n={}){const s={...e,...n,effectId:t},i=s.targetId,r=this.api.getTargetAnimation(i,s),a=this._getAnimationCallbacks(t,i,s);r?r.play():this.api.play(i,s,a)}_pauseAnimation(e,t){const n=this.api.getTargetAnimation(e,t);n?.pause()}_setAnimationPlaystateTrigger(e,t){const n=document.getElementById(t);if(n&&this.viewportManager){const s=n.closest("[data-block-level-container]")||n;this.viewportManager.observe(s,{effectId:e,targetId:t})}}_observeBreakpointChange(){this.breakpointRanges.forEach((e=>{const t=g(e),n=window.matchMedia(t);this.activeListeners.push(n),n.addEventListener("change",this.breakpointChangeHandler)}))}_breakpointChangeHandler(e){e.matches&&(this.scrubUpdateRequest||(this.scrubUpdateRequest=setTimeout((()=>{this._updateScrubManagers(this.scrubTriggers,!0),this.scrubUpdateRequest=null}),0)))}_setAnimationState(e){const t=document.getElementById(e);t&&(t.dataset.motionEnter="done")}_getAnimationCallbacks(e,t,n){const s=[];return"backwards"!==n.fill&&"both"!==n.fill||s.push((()=>{this._setAnimationState(t)})),s.push(...this.callbacks[e]?.start||[]),{start:s,end:this.callbacks[e]?.end}}}const g=e=>{const t=[];return e.max&&t.push(`(max-width:${e.max}px)`),e.min&&t.push(`(min-width:${e.min}px)`),t.join(" and ")};var f=n(8242),m=n(60367),p=n(789);function b(e){if(null==e)return window;const t=function(e){const t=window.getComputedStyle(e).overflowY;return"visible"!==t&&"clip"!==t}(e);return t?e:b(e.parentElement)}const v=(e,t)=>({getTargetAnimation:(e,t)=>(0,m.ml)(e,t),play(e,t,n){if(t.toggle){const n=(0,m.Db)(e,t.effectId);if(n)return n.reverse(),n}"reverse"===t.toggle&&(t={...t,reversed:!0});const s=(0,m.Nz)(e,t);let i;return t.registerCustomEffect&&t.registerCustomEffect(s.getProgress),n?.start?.length&&(i=()=>{n.start.forEach((e=>e()))}),n?.end?.length&&s.onFinish((()=>{n.end.forEach((e=>e()))})),s.play(i),s},playCSS(e,t,n){const s=document.getElementById(e),i=(0,m.ml)(e,t);i&&(s.addEventListener("animationend",(()=>{n?.end?.length&&n.end.forEach((e=>e()))}),{once:!0}),(0,m.M0)(e,t,(()=>i.play((()=>{n?.start?.length&&n.start.forEach((e=>e()))})))))},cancelAll(){document.documentElement.getAnimations({subtree:!0}).forEach((e=>e.cancel()))},startScrub(e,t,n){const s=new Map,i=[],r=new Map,a=[],o="ViewTimeline"in window;function c(e,t,n){const i=e(n),r=Array.isArray(i)?i:[i];let a;o?a=document.documentElement:(r.forEach((e=>{e.viewSource||(e.viewSource=t),e.groupId=`${n}-${t.id||""}`})),a=b(t.parentElement)),s.has(a)||s.set(a,[]),s.get(a).push(...r)}function l(e,t,s,i,a){const o="root"===a.hitArea,c=e(s,o&&!n?.forceEnableScene),l={isHitAreaRoot:o,effectId:i,eventSource:n?.pointerSource,...c},d=o?document.documentElement:t;r.has(d)||r.set(d,[]),r.get(d).push(l)}return Object.entries(e).forEach((([e,n])=>{if(!t[e])return;const s="view-progress"===n.trigger,i="pointer-move"===n.trigger,{targetId:r,namedEffect:a,customEffect:o}=t[e];if((a||o)&&(i||s)){const i=document.getElementById(n.componentId);if(i){this._getScrubTargets(n.componentId,r).forEach((r=>{const a=this._createScrub(t[e],{...n,element:i});return s?c(a.factory,i,r):l(a.factory,i,r,e,n.params)}))}else{Array.from(document.querySelectorAll(`[id^="${n.componentId}${p.Jx}"]`)).forEach((i=>{const a=this._createScrub(t[e],{...n,element:i}),o=(0,p.Zr)((0,p.vC)(r),(0,p.D2)(i.id));s?c(a.factory,i,o):l(a.factory,i,o,e,n.params)}))}}})),s.forEach(((e,t)=>{if(e.length)if(o)i.push(...e);else{const n=new f.O({root:t,scenes:e,observeViewportEntry:!1,observeViewportResize:!1,observeSourcesResize:!1,observeContentResize:!0,contentRoot:document.querySelector("#site-root")});i.push(n),Promise.all(e.map((e=>e.ready||Promise.resolve()))).then((()=>{n.start()}))}})),r.forEach(((e,t)=>{const n=e.find((e=>e.transitionDuration)),s=n?.transitionDuration,i=n?.transitionEasing,r=e.find((e=>e.eventSource))?.eventSource,o=e.some((e=>e.allowActiveEvent)),c=new u.g({root:t===document.documentElement?void 0:t,scenes:e,transitionDuration:s,transitionEasing:i,eventSource:r,allowActiveEvent:o});c.start(),a.push(c)})),[...i,...a]},cancelScrub(e){e.length&&(e.forEach((e=>e.destroy())),e.length=0)},_createScrub:(e,t)=>({targetId:e.targetId,factory:(n,s=!1)=>{const i=(0,m.Cn)(n||e.targetId,e,t,{disabled:s,ignoreScrollMoveOffsets:!0});return e.registerCustomEffect&&(Array.isArray(i)?e.registerCustomEffect(i[0].getProgress):(i.allowActiveEvent=!0,e.registerCustomEffect(i.getProgress))),i}}),_getScrubTargets(n,s){const i=e?.[s],{items:r=[]}=i&&t?t.get(i):{};return r.length?r.map((e=>(0,p.Zr)(s,e))):[s]}});function E({manager:e}){const t={entry:s((function(t){const s=t.filter((e=>{const{isIntersecting:t,target:s}=e;return t&&(n.get(s).isIntersecting=!0),t})).flatMap((e=>i(e.target)));e.trigger({resume:s})}),0,"10%"),exit:s((function(t){const s=t.filter((e=>{const{isIntersecting:t,target:s}=e;return t||(n.get(s).isIntersecting=!1),!t})).flatMap((e=>i(e.target)));e.trigger({hold:s})}),0,"50%")},n=new WeakMap;function s(e,t,n="0px"){const s={root:null,rootMargin:n,threshold:[t]};return new window.IntersectionObserver(e,s)}const i=e=>Array.from(n.get(e)?.effects||[]);return{observe:function(s,i){const r=n.get(s);r?(r.effects.add(i),r.isIntersecting&&e.trigger({resume:[i]})):(n.set(s,{effects:new Set([i]),isIntersecting:!1}),t.entry.observe(s),t.exit.observe(s))},disconnect:function(){t.entry.disconnect(),t.exit.disconnect()}}}const y=(0,s.Og)([(0,s.KT)(i.Gp,l.U),(0,s.KT)(i.wk,l.U),r.Is,a.Ji,o.RV,c.n],((e,t,n,s,i,r)=>{const{animationDataByCompId:a,scrubAnimationBreakpoints:o,repeaterTemplateToParentMap:c,isResponsive:l,useCSSTimeAnimations:u=!1}=e;if((0,d.fU)(i))return{name:"motion",async pageWillUnmount(){},getManager(){}};const g=v(c,s);let f=t.get()?.[n];f||(f=new h(g,E,{isResponsive:l,reducedMotion:r,useCSSTime:u}),t.update((e=>({...e,[n]:f}))));const m=r?{}:Object.assign({},...Object.values(a||{}));return f.init(m,o),{name:"motion",async pageWillUnmount(){f?.clear()},getManager:()=>f}}));var S=n(16537),w=n(478),I=n(35406);const A=(0,s.Og)([l.h,w.e,o.RV,o.HW,c.n,I.Q],((e,t,n,s,i,r)=>({getSdkHandlers:()=>({[l.U]:{runAnimation:async(a,o)=>{const c=Array.isArray(a.targetId)?a.targetId:[a.targetId];return new Promise((async l=>{const d=e.getManager(),u=[];if(!d)return void l();const h=()=>{_(n,c),"in"===o&&t.update(c.reduce(((e,t)=>({...e,[t]:{visibility:null}})),{}))},g=()=>{"out"===o&&t.update(c.reduce(((e,t)=>({...e,[t]:{visibility:"hidden !important"}})),{})),k(n,c),setTimeout((()=>{u.forEach((e=>e.cancel())),u.length=0}),0)};if(await Promise.all([s,...c.map((e=>r.waitForComponentToRender((0,p.qO)(e))))]),i)return h(),g(),void l();a.fill="both",c.forEach((e=>{u.push(d.api.play(e,a,{start:[h],end:[g,l]}))}))}))}}})}))),_=(e,t)=>{t.forEach((t=>{const n=e.document.getElementById(t);n&&n.classList.add("is-animating")}))},k=(e,t)=>{e.requestAnimationFrame((()=>{t.forEach((t=>{const n=e.document.getElementById(t);n&&n.classList.remove("is-animating")}))}))},M=e=>{e(o.H9).to(A),e(S.$.PageWillUnmountHandler,l.h).to(y)}}}]);
//# sourceMappingURL=https://static.parastorage.com/services/wix-thunderbolt/dist/motion.c02a3132.chunk.min.js.map