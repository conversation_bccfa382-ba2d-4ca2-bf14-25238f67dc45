"use strict";(self.webpackJsonp__wix_thunderbolt_app=self.webpackJsonp__wix_thunderbolt_app||[]).push([[5899],{61618:(r,e,o)=>{function n(r,e){const{webWindow:o}=e,n=null==o?void 0:o.location;return{applicationError:{CAPTCHA_REQUIRED:()=>({message:r("errorHandler.common-error.captchaRequired")})},statusCodeError:{UNAUTHENTICATED:()=>({message:r("errorHandler.common-error.unauthenticated"),action:o?{text:r("errorHandler.common-error.unauthenticated.cta"),onClick:()=>{const r=new URL("https://users.wix.com/signin");r.searchParams.set("redirectTo",window.location.href),null==o.open||o.open(r.href,"_blank")}}:void 0}),INVALID_ARGUMENT:()=>({message:r("errorHandler.common-error.validationBasic")}),PERMISSION_DENIED:()=>({message:r("errorHandler.common-error.permission-denied.site")}),NOT_FOUND:()=>({message:r("errorHandler.common-error.not-found")}),ALREADY_EXISTS:()=>({message:r("errorHandler.common-error.conflict"),action:n?{text:r("errorHandler.common-error.refresh.cta"),onClick:()=>{n.reload()}}:void 0}),UNAVAILABLE:()=>({message:r("errorHandler.common-error.technicalIssue")}),RESOURCE_EXHAUSTED:()=>({message:r("errorHandler.common-error.resource-exhausted")}),BAD_GATEWAY:()=>({message:r("errorHandler.common-error.technicalIssue")}),GATEWAY_TIMEOUT:()=>({message:r("errorHandler.common-error.technicalIssue")}),INTERNAL:()=>({message:r("errorHandler.common-error.technicalIssue")})}}}function s(r){return{message:r("errorHandler.common-error.noConnection")}}function a(r){return{message:r("errorHandler.common-error.technicalIssue")}}function t(r){return e=>{var o;const n=null==(o=e.exceptionMessage)?void 0:o.toLowerCase();return"network error"===n?{message:r("errorHandler.common-error.networkError")}:"request aborted"===n||"timeout exceeded"===n?{message:r("errorHandler.common-error.technicalIssue.generic")}:{message:r("errorHandler.common-error.technicalIssue-unmappedError")}}}async function c(r){const{translations:e,errorMonitor:o}=r;try{const o=await e.getTranslateFn();r.commonErrors={commonErrorsMap:n(o,r),connectionErrorShowErrorProps:s(o),defaultErrorShowErrorProps:a(o),getNetworkErrorShowErrorProps:t(o)}}catch(r){o.captureException(r)}}o.r(e),o.d(e,{createCommonErrorsMapping:()=>n,createGetNetworkErrorShowErrorProps:()=>t,getConnectionErrorShowErrorProps:()=>s,getDefaultErrorShowErrorProps:()=>a,setCommonErrors:()=>c})}}]);
//# sourceMappingURL=error-handler-commonErrorsMapping.d74ac92e.chunk.min.js.map