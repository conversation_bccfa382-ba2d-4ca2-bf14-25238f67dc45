(("undefined"!=typeof self?self:this).webpackJsonp__wix_form_app=("undefined"!=typeof self?self:this).webpackJsonp__wix_form_app||[]).push([[778],{66726:(e,t,n)=>{var o={"./messages_ar.json":[22141,7621],"./messages_bg.json":[41489,4113],"./messages_ca.json":[35402,3830],"./messages_cs.json":[93608,1656],"./messages_da.json":[38061,7841],"./messages_de.json":[74713,9965],"./messages_el.json":[55395,7351],"./messages_en.json":[5245,7445],"./messages_es.json":[60566,7022],"./messages_fi.json":[39639,3555],"./messages_fr.json":[90318,5366],"./messages_he.json":[29573,7169],"./messages_hi.json":[52857,9429],"./messages_hr.json":[33524,5052],"./messages_hu.json":[79381,5233],"./messages_id.json":[93575,2259],"./messages_it.json":[81527,3843],"./messages_ja.json":[9267,9959],"./messages_ko.json":[91316,8756],"./messages_lt.json":[98678,6514],"./messages_lv.json":[64324,2916],"./messages_ms.json":[86206,1382],"./messages_nl.json":[26048,7140],"./messages_no.json":[87805,7733],"./messages_pl.json":[62138,2622],"./messages_pt.json":[63154,838],"./messages_ro.json":[88329,9273],"./messages_ru.json":[54519,4883],"./messages_sk.json":[88688,7584],"./messages_sl.json":[90969,8477],"./messages_sv.json":[66023,199],"./messages_th.json":[45962,5062],"./messages_tl.json":[48422,6498],"./messages_tr.json":[28720,5296],"./messages_uk.json":[21662,7606],"./messages_vi.json":[57063,1843],"./messages_zh.json":[74040,2732]};function i(e){if(!n.o(o,e))return Promise.resolve().then((()=>{var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}));var t=o[e],i=t[0];return n.e(t[1]).then((()=>n.t(i,17)))}i.keys=()=>Object.keys(o),i.id=66726,e.exports=i},12242:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>Ji});var o=n(60751),i=n(16252);const r="10.137.0";var a=r;const s=()=>"undefined"==typeof window,l=(e=!1)=>!s()&&(/ricos-debug/i.test(window.location.search)||e),c=o.lazy((()=>Promise.all([n.e(699),n.e(857)]).then(n.bind(n,3093))));var d=n(65054);Object.freeze({ONE:"header-one",TWO:"header-two",THREE:"header-three",FOUR:"header-four",FIVE:"header-five",SIX:"header-six",PARAGRAPH:"unstyled"});const u="wix-draft-plugin-emoji",p="wix-draft-plugin-file-upload",m="wix-draft-plugin-gallery",g="wix-draft-plugin-image",h="wix-rich-content-plugin-spoiler",_="wix-draft-plugin-link-preview",E="wix-draft-plugin-vertical-embed",f="wix-draft-plugin-video",y="ricos-audio",b="wix-draft-plugin-poll",v=(d.A.DIVIDER,d.A.IMAGE,d.A.CAPTION,d.A.GALLERY,d.A.FILE,d.A.GIF,d.A.VIDEO,d.A.AUDIO,d.A.LINK_PREVIEW,d.A.EMBED,d.A.POLL),w=(d.A.APP_EMBED,d.A.COLLAPSIBLE_LIST,d.A.COLLAPSIBLE_ITEM,d.A.COLLAPSIBLE_ITEM_TITLE,d.A.COLLAPSIBLE_ITEM_BODY,d.A.HTML,d.A.CODE_BLOCK,d.A.TABLE,d.A.TABLE_ROW,d.A.TABLE_CELL,d.A.LAYOUT,d.A.LAYOUT_CELL,{[d.A.BUTTON]:"buttonData",[d.A.DIVIDER]:"dividerData",[d.A.FILE]:"fileData",[d.A.GALLERY]:"galleryData",[d.A.GIF]:"gifData",[d.A.HTML]:"htmlData",[d.A.IMAGE]:"imageData",[d.A.CAPTION]:"captionData",[d.A.COLLAPSIBLE_LIST]:"collapsibleListData",[d.A.LINK_PREVIEW]:"linkPreviewData",[d.A.APP_EMBED]:"appEmbedData",[d.A.VIDEO]:"videoData",[d.A.POLL]:"pollData",[d.A.TABLE]:"tableData",[d.A.TABLE_CELL]:"tableCellData",[d.A.PARAGRAPH]:"paragraphData",[d.A.LIST_ITEM]:"paragraphData",[d.A.HEADING]:"headingData",[d.A.CODE_BLOCK]:"codeBlockData",[d.A.BLOCKQUOTE]:"paragraphData",[d.A.EMBED]:"embedData",[d.A.EXTERNAL]:"externalData",[d.A.AUDIO]:"audioData",[d.A.TEXT]:"textData",[d.A.BULLETED_LIST]:"bulletedListData",[d.A.ORDERED_LIST]:"orderedListData",[d.A.COLLAPSIBLE_ITEM]:void 0,[d.A.COLLAPSIBLE_ITEM_TITLE]:void 0,[d.A.COLLAPSIBLE_ITEM_BODY]:void 0,[d.A.TABLE_ROW]:void 0,[d.A.UNRECOGNIZED]:void 0,[d.A.MAP]:void 0,[d.A.LAYOUT]:void 0,[d.A.LAYOUT_CELL]:"layoutCellData"});var A=n(92895);const T=e=>{const t=(({type:e})=>w[e])(e);return t&&t in e?e[t]:void 0},x=e=>i.countBy(e,(e=>e.type)),S={[d.A.DIVIDER]:"wix-draft-plugin-divider",[d.A.IMAGE]:g,[d.A.GALLERY]:m,[d.A.GIF]:"wix-draft-plugin-giphy",[d.A.HTML]:"wix-draft-plugin-html",[d.A.VIDEO]:f,[d.A.FILE]:p,[d.A.POLL]:b,[d.A.COLLAPSIBLE_LIST]:"wix-rich-content-plugin-collapsible-list",[d.A.TABLE]:"wix-rich-content-plugin-table",[d.A.EMBED]:_,[d.A.LINK_PREVIEW]:_,[d.A.APP_EMBED]:"wix-draft-plugin-vertical-embed",[d.A.AUDIO]:y,[d.A.BUTTON]:"wix-draft-plugin-link-button",[d.A.CODE_BLOCK]:"code-block",[d.A.BLOCKQUOTE]:"blockquote"},C={1:"header-one",2:"header-two",3:"header-three",4:"header-four",5:"header-five",6:"header-six"},I={[d.g.MENTION]:"mention",[d.g.LINK]:"LINK",[d.g.ANCHOR]:"ANCHOR"},L=/\u00a9|\u00ae|[\u2000-\u3300]|\ud83c[\ud000-\udfff]|\ud83d[\ud000-\udfff]|\ud83e[\ud000-\udfff]/g;var V=n(70119);const D=e=>`${e?.nofollow?"nofollow ":""}${e?.sponsored?"sponsored ":""}${e?.ugc?"ugc ":""}${e?.noreferrer?"noreferrer":""}`.trim();var P=n(16307),O=n(87029),k=(n(22562),n(29056)),B=n(20681),M=n(80666);n(8920);k.Ej,k.Im;const R=e=>t=>Object.keys(t).sort(e.compare);M._k;function U(e){if("function"==typeof e)return U(M._k)(e);const t=R(e);return e=>n=>{const o=[];for(const i of t(n))o.push(e(i,n[i]));return o}}const N=U(M._k)(((e,t)=>[e,t]));k.HM,k.zy;k.Ww,k.$I,k.rO,k.Tj;k.Gr;function F(e){return k.rF(e)}k.XI;k.Jc;k.Si,k.zN,k.Mu;const j=k.K2,H=(k.e,k.C),z=k.fP,$=k.Q,W=(k.qe,k.K5,k.S,k.ge,k.Yz),G=k.ji,K=k.eb,Y=(k.kM,k.Kw,k.WF,k.wL,k.UX),q=(k.l7,e=>t=>{const n=R(e);return(e,o)=>{const i=n(e);if(0===i.length)return t.of({});let r=t.of({});for(const n of i)r=t.ap(t.map(r,(e=>t=>(e[n]=t,e))),o(n,e[n]));return r}});k.pb,k.x1,k.jB,k.rT;k.oE,k.sj;const Z="Record";k.nZ;const J=k.JI,X=(M._k,M._k,M._k,M._k,M._k,M._k,M._k,M._k,M._k,M._k,M._k,M._k,M._k,M._k,M._k,M._k,M._k,M._k,M._k,M._k,M._k,M._k,M._k,M._k,M._k,k.mQ,M._k,M._k,M._k,M._k,M._k,M._k,M._k,M._k,(0,O.w3)({config:J((0,B.HV)()),typeMappers:(0,P.JI)(),nodeViewDecorators:(0,P.JI)(),nodeViewRenderers:J((0,B.HV)())}));function Q({plugins:e=[],viewerProps:t}){return(0,V.Fs)(e,(0,P.Tj)((e=>{const t={config:{[e.type]:e.config},typeMappers:[],nodeViewRenderers:{},nodeViewDecorators:[]};return e.nodeViewRenderers?t.nodeViewRenderers=e.nodeViewRenderers:e.typeMapper&&(t.typeMappers=[e.typeMapper]),t.nodeViewDecorators=e.nodeViewDecorators?e.nodeViewDecorators:[],t})),(0,O.AU)(X),(n=(({config:e={},typeMappers:t=[],nodeViewRenderers:n,nodeViewDecorators:o})=>({config:e,typeMappers:t,nodeViewRenderers:n,nodeViewDecorators:o}))(t),e=>i.merge({},e,n)));var n}const ee=(e,...t)=>!e||e.some((e=>"function"!=typeof e))?(console.warn(`${e} is expected to be a function array`),{}):e.reduce(((e,n)=>Object.assign(e,n(...t))),{});function te({_rcProps:e,isMobile:t,addAnchors:n,onError:o,mediaSettings:a={},linkSettings:s={},textAlignment:l,experiments:c,iframeSandboxDomain:d,theme:u,plugins:p=[],content:m,viewerProps:g,onNodeClick:h,debugMode:_,jsdomMode:E,baseUrl:f}){const y=Q({plugins:p,viewerProps:g}),{pauseMedia:b,disableRightClick:v}=a,{anchorTarget:w="_blank",customAnchorScroll:A}=s;let{relValue:T,rel:x}=s;T&&(console.warn("relValue is deprecated, Please use rel prop instead."),x=(e=>({nofollow:e?.includes("nofollow"),sponsored:e?.includes("sponsored"),ugc:e?.includes("ugc"),noreferrer:e?.includes("noreferrer")}))(T)||x),T=D(x);const S={addAnchors:n,isMobile:t,config:{uiSettings:{disableDownload:a?.disableDownload||v}},content:m,onError:o,helpers:{},disabled:b,anchorTarget:w,relValue:T,customAnchorScroll:A,textAlignment:l,experiments:c,iframeSandboxDomain:d,theme:u,onNodeClick:h,jsdomMode:E,baseUrl:f},C=(0,V.Fs)(i.merge({locale:"en",config:{}},y,e,S,g),(I=()=>m?.metadata?.id,({helpers:e={},...t})=>({...t,helpers:{...e,onViewerLoaded:t=>e.onViewerLoaded?.({...t,version:r,contentId:I()}),onViewerAction:(t,n,o,i,r)=>e.onViewerAction?.(t,n,o,i||I(),r)}})));var I;return{...C,debugMode:_,typeMappers:ee(C.typeMappers||[])}}var ne=n(73141),oe=n(43373);const ie=(0,V.L3)((e=>Array.isArray(e)?{id:"root",type:"UNRECOGNIZED",nodes:e}:e),(0,oe.N)((e=>Array.isArray(e.nodes)?e.nodes:[]))),re={PARAGRAPH:"PARAGRAPH",TEXT:"TEXT",HEADING:"HEADING",BULLETED_LIST:"BULLETED_LIST",ORDERED_LIST:"ORDERED_LIST",LIST_ITEM:"LIST_ITEM",BLOCKQUOTE:"BLOCKQUOTE",CODE_BLOCK:"CODE_BLOCK",VIDEO:"VIDEO",DIVIDER:"DIVIDER",FILE:"FILE",GALLERY:"GALLERY",GIF:"GIF",HTML:"HTML",IMAGE:"IMAGE",LINK_PREVIEW:"LINK_PREVIEW",POLL:"POLL",APP_EMBED:"APP_EMBED",BUTTON:"BUTTON",COLLAPSIBLE_LIST:"COLLAPSIBLE_LIST",COLLAPSIBLE_ITEM:"COLLAPSIBLE_ITEM",COLLAPSIBLE_ITEM_TITLE:"COLLAPSIBLE_ITEM_TITLE",COLLAPSIBLE_ITEM_BODY:"COLLAPSIBLE_ITEM_BODY",TABLE:"TABLE",EMBED:"EMBED",TABLE_CELL:"TABLE_CELL",TABLE_ROW:"TABLE_ROW",EXTERNAL:"EXTERNAL",AUDIO:"AUDIO",CAPTION:"CAPTION",LAYOUT:"LAYOUT",LAYOUT_CELL:"LAYOUT_CELL",UNRECOGNIZED:"UNRECOGNIZED",MAP:"MAP"},ae=()=>Promise.all([n.e(4660),n.e(5995),n.e(8160)]).then(n.bind(n,5348)),se=o.lazy(ae),le=e=>{const{children:t,isMobile:n,fullscreenProps:i,content:r}=e,[a,s]=o.useState({type:"closed"}),l=o.useMemo((()=>r?ce(r):{images:[],imageMap:{}}),[e.content]);return o.createElement(o.Fragment,null,t({config:(e=>{const t=(e,t=0)=>{s({type:"open",activeIndex:l?l.imageMap[e]+t:0})},n={...e},o=e["wix-draft-plugin-image"],i=e["wix-draft-plugin-gallery"];return o&&!o.onExpand&&(n["wix-draft-plugin-image"]={...o,onExpand:t}),i&&!i.onExpand&&(n["wix-draft-plugin-gallery"]={...i,onExpand:t}),n})(e.config),onHover:()=>ae()}),"open"===a.type&&o.createElement(o.Suspense,{fallback:o.createElement("div",null)},o.createElement(se,{images:l.images,onClose:()=>s({type:"closed"}),index:a.activeIndex,isMobile:n,...i,jsdomMode:e.jsdomMode,t:e.t})))},ce=e=>{let t=0;return ie(e.nodes).filter((e=>[re.IMAGE,re.GALLERY].includes(e.type))).get().reduce(((e,n)=>{const{images:o,imageMap:i}=e,r=de(n),a=t;return t+=r.length,{images:[...o,...r],imageMap:{...i,[n.id??""]:a}}}),{images:[],imageMap:{}})},de=e=>{switch(e.type){case re.IMAGE:return e.imageData.image?.src&&!e.imageData?.disableExpand?[{image:{media:{...e.imageData.image}},disableDownload:e.imageData.disableDownload,altText:e.imageData.altText}]:[];case re.GALLERY:return e.galleryData?.disableExpand?[]:e.galleryData.items.map((t=>({...t,disableDownload:e.galleryData.disableDownload})));default:return[]}};var ue=n(55530);const pe={fontWeight:"normal",fontStyle:"normal",lineHeight:"1.5"},me={bgColor:"#ffffff",textColor:"#000000"},ge={desktop:{customStyles:{p:{...pe,fontSize:"16px"},h1:{...pe,fontSize:"40px"},h2:{...pe,fontSize:"28px"},h3:{...pe,fontSize:"24px"},h4:{...pe,fontSize:"20px"},h5:{...pe,fontSize:"18px"},h6:{...pe,fontSize:"16px"},quote:{lineHeight:"1.5",fontSize:"18px"},codeBlock:{lineHeight:"1.5"}},palette:me},mobile:{customStyles:{p:{...pe,fontSize:"14px"},h1:{...pe,fontSize:"32px"},h2:{...pe,fontSize:"24px"},h3:{...pe,fontSize:"20px"},h4:{...pe,fontSize:"20px"},h5:{...pe,fontSize:"16px"},h6:{...pe,fontSize:"14px"},quote:{lineHeight:"1.5",fontSize:"18px"},codeBlock:{lineHeight:"1.5"}},palette:me}};var he=n(99173),_e=n(16199);class Ee{constructor(){this.type=d.g.UNRECOGNIZED}getDecoration(){return{}}fromCustomStyle(e){return new Ee}toCustomStyle(){return{}}overrideWith(e){return e}}class fe{constructor(e){this.type=d.g.BOLD,this.customStyle=e}static of(e){if(e.type!==d.g.BOLD)throw new TypeError(`invalid decoration initializer ${e}`);return new fe({fontWeight:e.fontWeightValue})}getDecoration(){return{type:this.type,fontWeightValue:(e=this.customStyle.fontWeight,"bold"===e?700:"normal"===e?400:parseInt(e,10))};var e}static fromCustomStyle(e){return new fe(e)}fromCustomStyle(e){return fe.fromCustomStyle(e)}toCustomStyle(){return this.customStyle}overrideWith(e){if(!(e instanceof fe||e instanceof Ee))throw new TypeError(`invalid merge decoration ${e}`);const t={...this.customStyle,...e.toCustomStyle()};return new fe(t)}}class ye{constructor(e){this.type=d.g.COLOR,this.customStyle=e}static of(e){if(e.type!==d.g.COLOR)throw new TypeError(`invalid decoration initializer ${e}`);const{foreground:t,background:n}=e.colorData||{},o=i.pickBy({color:t,backgroundColor:n});return new ye(o)}getDecoration(){return{type:this.type,colorData:{...this.customStyle.color&&{foreground:this.customStyle.color},...this.customStyle.backgroundColor&&{background:this.customStyle.backgroundColor}}}}static fromCustomStyle(e){return new ye(e)}fromCustomStyle(e){return ye.fromCustomStyle(e)}toCustomStyle(){return this.customStyle}overrideWith(e){if(!(e instanceof ye||e instanceof Ee))throw new TypeError(`invalid merge decoration ${e}`);const t={...this.customStyle,...e.toCustomStyle()};return new ye(t)}}var be=(e=>(e.PX="PX",e.EM="EM",e.UNRECOGNIZED="UNRECOGNIZED",e))(be||{});class ve{constructor(e){this.type=d.g.FONT_SIZE,this.customStyle=e}static of(e){if(e.type!==d.g.FONT_SIZE)throw new TypeError(`invalid decoration initializer ${e}`);const t=e.fontSizeData?.value,n=e.fontSizeData?.unit?.toLocaleLowerCase();return new ve(t?{fontSize:`${t}${n}`}:{})}getDecoration(){return{type:this.type,fontSizeData:{value:parseInt(this.customStyle.fontSize),unit:be.PX}}}static fromCustomStyle(e){return new ve(e)}fromCustomStyle(e){return ve.fromCustomStyle(e)}toCustomStyle(){return this.customStyle}overrideWith(e){if(!(e instanceof ve||e instanceof Ee))throw new TypeError(`invalid merge decoration ${e}`);const t={...this.customStyle,...e.toCustomStyle()};return new ve(t)}}class we{constructor(e){this.type=d.g.ITALIC,this.customStyle=e}static of(e){if(e.type!==d.g.ITALIC)throw new TypeError(`invalid decoration initializer ${e}`);return new we(e.italicData?{fontStyle:"italic"}:{})}getDecoration(){return{type:this.type,italicData:this.customStyle.fontStyle?.includes("italic")}}static fromCustomStyle(e){return new we(e)}fromCustomStyle(e){return we.fromCustomStyle(e)}toCustomStyle(){return this.customStyle}overrideWith(e){if(!(e instanceof we||e instanceof Ee))throw new TypeError(`invalid merge decoration ${e}`);const t={...this.customStyle,...e.toCustomStyle()};return new we(t)}}var Ae=n(66005);const Te=(e,t,n)=>{const o=Ae.CA(B.$1()),i=B.KO(o)(Ae.kb(t));return(0,V.Fs)(i((0,V.Fs)(n,P.Tj((n=>(0,V.Fs)(e,Ae.aN(n[0],(()=>t)),Ae.Tj(n[1])))))),Ae.AU(V.D_,V.D_))},xe=class e{constructor(e){this.decorations=e}static of(t){const n=(t||[]).map(e.toTextDecoration);return new e(n)}static fromCustomStyle(t){const{color:n,backgroundColor:o,...i}=t,r={color:n??"",backgroundColor:o??""},a=Object.entries(i).map((([e,t])=>({[e]:t}))).concat([r]).map(e.styleToDecoration);return new e(a)}static styleToDecoration(t){return Te(t,e.empty,[[e=>!!e.fontWeight,e=>fe.fromCustomStyle(e)],[e=>!!e.fontSize,e=>ve.fromCustomStyle(e)],[e=>!!e.fontStyle,e=>we.fromCustomStyle(e)],[e=>!!e.color||!!e.backgroundColor,e=>ye.fromCustomStyle(e)]])}static toTextDecoration(t){return Te(t,e.empty,[[e=>e.type===d.g.BOLD,e=>fe.of(e)],[e=>e.type===d.g.ITALIC,e=>we.of(e)],[e=>e.type===d.g.FONT_SIZE,e=>ve.of(e)],[e=>e.type===d.g.COLOR,e=>ye.of(e)]])}toDecorationArray(){return this.decorations.filter((e=>e.type!==d.g.UNRECOGNIZED)).map((e=>e.getDecoration()))}byType(t){return this.decorations.find((e=>e.type===t))||e.empty}toCustomStyle(){return this.decorations.reduce(((e,t)=>({...e,...t.toCustomStyle()})),{})}overrideWith(t){const n=t.map(e.toTextDecoration).map((e=>this.byType(e.type).overrideWith(e))),o=n.map((e=>e.type)),i=this.decorations.filter((e=>!o.includes(e.type))).concat(n);return new e(i)}};xe.empty=new Ee;let Se=xe;const Ce=class e{constructor(t){this.getNodeStyle=()=>this.nodeStyle,this.toCustomStyle=()=>({paddingBottom:this.nodeStyle.paddingBottom,paddingTop:this.nodeStyle.paddingTop}),this.overrideWith=(t={})=>e.of({...this.nodeStyle,...t}),this.nodeStyle=t}static of(t){return new e(t||{})}};Ce.fromCustomStyle=e=>{const{paddingBottom:t,paddingTop:n}=e,o={paddingBottom:t,paddingTop:n};return Ce.of(o)};let Ie=Ce;const Le=class e{constructor(t){this.getTextStyle=()=>this.textStyle,this.toCustomStyle=()=>({lineHeight:this.textStyle.lineHeight}),this.overrideWith=t=>e.of({...this.textStyle,...t}),this.textStyle=this.preprocess(t)}preprocess(e){let t=e.lineHeight;return t&&!Number(t)&&(t.includes("px")||t.includes("em"))&&(t=t.slice(0,t.length-2)),i.pickBy({...e,lineHeight:t})}static of(t){return new e(t||{})}};Le.fromCustomStyle=e=>{const{lineHeight:t}=e,n={lineHeight:t};return Le.of(n)};let Ve=Le;var De={darkTheme:{textColor:"#000000",bgColor:"#FFFFFF",actionColor:"#15E0FB"}};const Pe={BG_COLOR:11,DISABLED_COLOR:13,TEXT_COLOR_LOW:14,TEXT_COLOR:15,ACTION_COLOR:18},Oe=(e,t)=>((e,t)=>e[t<=5?t-1:t-6])(e,t).value;function ke(e,t){const n=e.map(((e,t)=>{if(e.indexOf("%")>-1){const n=parseFloat(e.substr(0,e.length-1))/100;return t<3?Math.round(255*n):n}return parseFloat(e)}));let o=Number(n[0]).toString(16),i=Number(n[1]).toString(16),r=Number(n[2]).toString(16),a=Math.round(255*Number(n[3])).toString(16);return 1===o.length&&(o="0"+o),1===i.length&&(i="0"+i),1===r.length&&(r="0"+r),1===a.length&&(a="0"+a),"#"+o+i+r+(t?a:"")}function Be(e){if("transparent"===e)return"#FFFFFF00";if((e.startsWith("rgb(")||e.startsWith("rgba("))&&e.endsWith(")")){const t=e.replace(/^(rgba\()|^(rgb\()|(\s)|(\))$/g,"").split(",");if(4===t.length)return ke(t,!0);if(3===t.length)return ke(t);throw Error("[ricos-common] themeUtils.ts: Bad RGB / RGBA value: "+e)}if(!e.startsWith("#"))throw Error(`[ricos-common] themeUtils.ts: Bad Hex (${e}).\n      Ricos color can only accept "transparent" or a HEX formatted color as its value`);return e}function Me(e){if("transparent"===e)return!0;if((e.startsWith("rgb(")||e.startsWith("rgba("))&&e.endsWith(")")){const t=e.replace(/^(rgba\()|^(rgb\()|(\s)|(\))$/g,"").split(",");return 4===t.length||3===t.length}return!!e.startsWith("#")}function Re(e){const t=e.replace("#","");return(299*parseInt(t.substr(0,2),16)+587*parseInt(t.substr(2,2),16)+114*parseInt(t.substr(4,2),16))/1e3}function Ue(e,t){if(e)return Re(e)<150?e:t||"#000000"}function Ne(e){const t=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})?$/i.exec(e.toLowerCase());if(t)return{r:parseInt(t[1],16),g:parseInt(t[2],16),b:parseInt(t[3],16),a:parseInt(t[4],16)};throw new Error("Bad Hex")}function Fe(e){if(!e)return;const{r:t,g:n,b:o}=Ne(e);return`${t}, ${n}, ${o}`}function je(e){return`rgb(${e})`}const He=e=>e.replace(/([A-Z])/g,((e,t)=>"-"+t.toLowerCase())),ze=(" ".repeat(4),e=>"object"==typeof e&&"type"in e&&"rgb"===e.type),$e=(e,t)=>{const{contentBgColor:n=!1,focusActionColor:o,settingsActionColor:i}=t||{},r=ze(e),{textColor:a,bgColor:s,actionColor:l,fallbackColor:c=(r?"0, 0, 0":"#000000"),disabledColor:d,textColorLow:u,textOnActionColor:p}=e,m=(g={textColor:a,actionColor:l,backgroundColor:s,fallbackColor:c,disabledColor:d,textColorLow:u,settingsActionColor:i,focusActionColor:o,textOnActionColor:p},Object.keys(g).reduce(((e,t)=>{const n=g[t];if(void 0!==n){const o=Me(n)&&r?Fe(Be(n)):void 0,i=void 0!==o?o:n;e[t]=r?je(i):i,e[`${t}Tuple`]=r?i:Fe(i)}return e}),{}));var g;const h=n&&void 0!==s?r?je(s):s:void 0,_=r?`rgb(${c})`:Ue(l,c),E=r?c:Fe(Ue(l,c));return{...m,..._&&{actionColorFallback:_},...E&&{actionColorFallbackTuple:E},...h&&{bgColorContainer:h}}},We=e=>"string"==typeof e?e in De?De[e]:(console.error(`Palette ${e} is unknown. Supported themes: ${De}`),{}):Array.isArray(e)?((e=>{if(0===e.length)throw Error("Received empty palette colors array");const t=e.map((e=>e.name)),n=i.difference(Object.values(Pe).map((e=>`color_${e}`)),t);if(n.length>0){const e=[];throw e.push("Some palette colors were not supplied:",`\n${n}\n`,"Palette array must include the following colors:",Object.entries(Pe).map((e=>`${e[1]} - ${e[0]}`)).toString().split(",").join("\n"),""),Error(e.join("\n"))}})(e),{actionColor:Oe(e,Pe.ACTION_COLOR),bgColor:Oe(e,Pe.BG_COLOR),textColor:Oe(e,Pe.TEXT_COLOR),disabledColor:Oe(e,Pe.DISABLED_COLOR),textColorLow:Oe(e,Pe.TEXT_COLOR_LOW)}):e&&((e=>e&&"string"!=typeof e&&!Array.isArray(e)&&(e.actionColor||e.textColor||e.bgColor))(e)||ze(e))?e:(console.error("Unrecognized Palette object. Please refer to Ricos Theme Documentation"),{});function Ge(e,t){if(!e)return{paletteVarsObject:{}};const n=ze(e),o=We(e),i=function(e,t){return!!Object.entries(e).length&&Object.entries(e).every((([e,n])=>!(n&&"string"==typeof n&&!t&&!Me(n))||(console.error(`Invalid color value '${n}' for ${e}. Please refer to Ricos Theme Documentation`),!1)))}(o,n);if(!i)return{paletteVarsObject:{},colors:o};const r=function(e,t=!1){return Object.entries(e).reduce(((e,[n,o])=>(e[n]=o&&"string"==typeof o&&!t&&Me(o)?Be(o):o,e)),{})}(o,n);return{paletteVarsObject:$e(r,t),colors:r}}class Ke{constructor(e,t){this.key=e,this.customStyle=t}getKey(){return this.key}toCustomStyle(){return this.customStyle}overrideWith(e){if("empty"!==e.key&&this.key!==e.key)throw new Error(`Different keys, ${this.key} doesn't equal to ${e.key}`);return new Ke(this.key,{...this.customStyle,...e.customStyle})}}const Ye=["h1","h2","h3","h4","h5","h6","p","quote","codeBlock"],qe=class e{constructor(e){this.customStyles=e}static of(t){const n=Object.entries(t).map((([e,t])=>new Ke(e,t)));return new e(n)}static fromTheme(t){const n=t.customStyles||{},{colors:o}=Ge(t.palette,t.paletteConfig),{textColor:i,bgColor:r}=o||{};if(o?.textColor||o?.bgColor){const t=Ye.reduce(((e,t)=>({...e,[t]:{color:i,backgroundColor:r}})),{});return e.of(t).overrideWith(e.of(n))}return e.of(n)}toCustomStyles(){return this.customStyles.reduce(((e,t)=>{const n=t.toCustomStyle();return{...e,...i.isEmpty(n)?{}:{[t.getKey()]:n}}}),{})}byKey(t){return this.customStyles.find((e=>e.getKey()===t))||e.empty}overrideWith(t){const n=Ye.reduce(((e,t)=>({...e,[t]:{}})),{}),o=e.of(n).customStyles.map((e=>e.overrideWith(this.byKey(e.getKey())).overrideWith(t.byKey(e.getKey()))));return new e(o)}};qe.empty=new class extends Ke{constructor(){super("empty",{})}overrideWith(e){return e}};let Ze=qe;const Je={headerOne:"h1",headerTwo:"h2",headerThree:"h3",headerFour:"h4",headerFive:"h5",headerSix:"h6",paragraph:"p",blockquote:"quote",codeBlock:"codeBlock"},Xe=(0,V.Fs)(Je,Object.entries,P.Tj(he.wg),_e.P),Qe=(0,V.L3)((e=>N(e)),P.Tj(he.wu((e=>({...Se.of(e?.decorations).toCustomStyle(),...Ie.of(e?.nodeStyle).toCustomStyle(),...Ve.of({lineHeight:e?.lineHeight}).toCustomStyle()})),(e=>Je[e]))),(e=>(0,_e.P)(e))),et=(0,V.L3)(Object.entries,P.Tj(he.wu((e=>({decorations:Se.fromCustomStyle(e).toDecorationArray(),nodeStyle:Ie.fromCustomStyle(e).getNodeStyle(),lineHeight:Ve.fromCustomStyle(e).getTextStyle().lineHeight})),(e=>Xe[e]))),_e.P,JSON.stringify,JSON.parse);class tt{constructor(e){this.theme=e}static fromTheme(e){return new tt(e||{})}static fromDocumentStyle(e){const t=Qe(e);return new tt({customStyles:t})}toTheme(){return this.theme}toDocumentStyle(){return et(Ze.fromTheme(this.theme).toCustomStyles())}}function nt(e){return{...i.mapValues(e,(e=>"object"==typeof e&&null!==e?nt(e):e)),...void 0!==e.fontSize&&void 0===e.lineHeight?{lineHeight:1.5}:{}}}function ot(e,t="",n="-"){const o={};return function e(i,r=""){for(const[a,s]of Object.entries(i)){const i=r?`${r}${n}${a}`:a;"object"==typeof s?e(s,i):o[`${t?t+"-":""}${i}`]=s}}(e),o}var it=({customStyles:e={},settingsStyles:t={},nativeStyle:n={}})=>i.merge({},ot(nt(e),"custom"),ot(t,"settings"),ot(n,"native"));const rt=e=>(e=>Object.entries(e))(e).reduce(((e,t)=>{return{...e,[`font-${t[0].toLowerCase()}`]:(n=t[1],n.value.substr(5,n.value.length-6))};var n}),{});const at=(e,t)=>{const n=tt.fromDocumentStyle(t).toTheme(),o=i.merge({},e,n),{palette:r,paletteConfig:a,typography:s,customStyles:l,nativeStyle:c}=o,{paletteVarsObject:d}=Ge(r,a),u=function(e){if(!e)return{};const{fontFamily:t,wixTypography:n}=e,o=n?rt(n):{};return Object.assign(o,t&&{fontFamily:t})}(s);return(e=>{const t=e=>"--ricos-"+He(e);return Object.entries(e).reduce(((e,[n,o])=>({...e,[t(n)]:o})),{})})({...d,...u,...it({customStyles:l,nativeStyle:c}),...{"breakout-normal-padding-start":e?.breakout?.normalPadding.start??"0","breakout-normal-padding-end":e?.breakout?.normalPadding.end??"0","breakout-full-width-padding-start":e?.breakout?.fullWidthPadding?.start??"0","breakout-full-width-padding-end":e?.breakout?.fullWidthPadding?.end??"0"}})},st=o.createContext({isMobile:!1,experiments:{},languageDir:"ltr"}),lt="no-outline";class ct extends o.Component{constructor(){super(...arguments),this.handleTabKeyUp=e=>{9===e.which&&document.body.classList.contains(lt)&&document.body.classList.remove(lt)},this.handleClick=()=>{document.body.classList.contains(lt)||document.body.classList.add(lt)},this.render=()=>null}componentDidMount(){document.body.classList.add(lt),this.props.isMobile||(document.addEventListener("keyup",this.handleTabKeyUp),document.addEventListener("click",this.handleClick))}componentWillUnmount(){this.props.isMobile||(document.removeEventListener("keyup",this.handleTabKeyUp),document.removeEventListener("click",this.handleClick))}}const dt=["ae","ar","arc","bcc","bqi","ckb","dv","fa","glk","he","ku","mzn","nqo","pnb","ps","sd","ug","ur","yi"],ut=e=>(e=>!!e&&dt.includes(e.split("-|_")[0].toLowerCase()))(e)?"rtl":"ltr";var pt={fontElementMap:"Jj-ce",wrapper:"_51bfW",pluginContainerMobile:"AHsN1",viewer:"Z9NRM",android:"f20Gz",rootedTraversedContent:"XM1-r",atomic:"oHsIv",toolbar:"b-1Sh",block:"JKIfS"},mt={rtl:"utB3m",ltr:"igx42"};const gt=o.memo((({anchorKey:e,type:t})=>o.createElement("div",{key:e,type:t,"data-hook":e}))),ht=(e,t,n)=>{const i=(e=>[d.A.PARAGRAPH,d.A.HEADING,d.A.BLOCKQUOTE,d.A.CODE_BLOCK].includes(e.type)&&!e.nodes?.length)(e)?"empty-line":e.type.toLowerCase(),r=`${n}${t+1}`;return o.createElement(gt,{key:r,type:i,anchorKey:r})};var _t=({addAnchors:e,children:t})=>{if(e){const n=e&&(!0===e?"rcv-block":e),i=`${n}-first`,r=`${n}-last`;return o.createElement(o.Fragment,null,o.createElement(gt,{type:"first",anchorKey:i}),(0,o.cloneElement)(o.Children.only(t),{addAnchorsPrefix:n}),o.createElement(gt,{type:"last",anchorKey:r}))}return t};const Et=o.createContext({locale:"en",localeContent:"en",experiments:{},isMobile:!1,t:e=>e,languageDir:"ltr",theme:{},portal:null,debugMode:[],platform:"macOs",content:null,jsdomMode:!1,environment:"liveSite",tabIndex:0,lastInteraction:null}),ft=({children:e,isMobile:t,locale:n,localeContent:i,experiments:r,languageDir:a,theme:s,t:l,portal:c,debugMode:d=[],jsdomMode:u=!1,platform:p="macOs",content:m=null,environment:g="liveSite",tabIndex:h=0,lastInteraction:_=null})=>o.createElement(Et.Provider,{value:{t:l,locale:n,localeContent:i,isMobile:t,experiments:r??{},languageDir:a,theme:s,portal:c??null,debugMode:d,jsdomMode:u,platform:p,content:m,environment:g,tabIndex:h,lastInteraction:_}},e);const yt=o.createContext(null),bt=({zIndexService:e,children:t})=>o.createElement(yt.Provider,{value:e},t);const vt=o.createContext(null),wt=({nodeViewDecorators:e,renderers:t,SpoilerViewerWrapper:n,children:i})=>o.createElement(vt.Provider,{value:{nodeViewDecorators:e,renderers:t,SpoilerViewerWrapper:n}},i);var At=n(418);const Tt=o.createContext(null),xt=({textAlignment:e,children:t})=>{const n=(0,o.useMemo)((()=>({textAlignment:e})),[e]);return o.createElement(Tt.Provider,{value:n},t)},St=o.createContext(null),Ct=({spoilerManager:e,children:t})=>o.createElement(St.Provider,{value:{spoilerManager:e}},t),It=o.createContext(null),Lt=({visibleIndentation:e,children:t})=>o.createElement(It.Provider,{value:{visibleIndentation:e}},t),Vt=o.createContext(null),Dt=({parentNode:e,parentIndex:t,children:n})=>o.createElement(Vt.Provider,{value:{parentNode:e,parentIndex:t}},n);const Pt=1e5,Ot=5e3,kt={TOOLBAR:100,POPUP:200,DRAWER:300,DIALOG:300,TOOLTIP:400,NOTIFICATION:500};class Bt{constructor(e,t){this.baseZIndex=e?Pt:Ot,this.settingsStyles=t}layerToCssVar(e){if(this.settingsStyles&&this.settingsStyles?.modals)return`--ricos-settings-modals-${e.toLowerCase()}-z-index`}getZIndex(e){const t=this.baseZIndex+kt[e],n=this.layerToCssVar(e);return void 0!==n?`var(${n}, ${t})`:t}}const Mt=[{rule:e=>!!e.textData?.decorations?.some((({type:e})=>e===d.g.BOLD)),decorate:({element:e,node:t})=>{const n=t.textData?.decorations?.find((({type:e})=>e===d.g.BOLD));return((e,t)=>{const{fontWeightValue:n}=e;return o.createElement("strong",{key:`bold-${t?.key}`,style:{fontWeight:n||700}},t)})(n,e)}},{rule:e=>!!e.textData?.decorations?.some((({type:e})=>e===d.g.ITALIC)),decorate:({element:e,node:t})=>{const n=t.textData?.decorations?.find((({type:e})=>e===d.g.ITALIC));return((e,t)=>{const{italicData:n}=t,i=void 0===n||n;return o.createElement("em",{key:`italic-${e.key}`,style:{fontStyle:i?"italic":"normal"}},e)})(e,n)}},{rule:e=>!!e.textData?.decorations?.some((({type:e})=>e===d.g.UNDERLINE)),decorate:({element:e,node:t})=>{const n=t.textData?.decorations?.find((({type:e})=>e===d.g.UNDERLINE));return((e,t)=>{const{underlineData:n}=t,i=void 0===n||n;return o.createElement("u",{key:e.key,style:{textDecoration:i?"underline":"none"}},e)})(e,n)},priority:2},{rule:e=>!!e.textData?.decorations?.some((({type:e})=>e===d.g.FONT_SIZE)),decorate:({element:e,node:t})=>{const n=t.textData?.decorations?.find((({type:e})=>e===d.g.FONT_SIZE));return((e,t)=>{const{unit:n,value:i}=t.fontSizeData||{};return o.createElement("span",{key:`fontsize-${e.key}`,style:{fontSize:`${i}${n?n.toLowerCase():"px"}`}},e)})(e,n)}},{rule:e=>!!e.textData?.decorations?.some((({type:e})=>e===d.g.STRIKETHROUGH)),decorate:({element:e,node:t})=>{const n=t.textData?.decorations?.find((({type:e})=>e===d.g.STRIKETHROUGH));return((e,t)=>{const{strikethroughData:n}=t,i=void 0===n||n;return o.createElement("s",{key:`strikethrough-${e?.key}`,style:{textDecoration:i?"line-through":"none"}},e)})(e,n)}},{rule:e=>!!e.textData?.decorations?.some((({type:e})=>e===d.g.SUBSCRIPT)),decorate:({element:e,node:t})=>{const n=t.textData?.decorations?.find((({type:e})=>e===d.g.SUBSCRIPT));return((e,t)=>{const{subscriptData:n}=t;return void 0===n||n?o.createElement("sub",{key:`subscript-${e?.key}`},e):e})(e,n)}},{rule:e=>!!e.textData?.decorations?.some((({type:e})=>e===d.g.SUPERSCRIPT)),decorate:({element:e,node:t})=>{const n=t.textData?.decorations?.find((({type:e})=>e===d.g.SUPERSCRIPT));return((e,t)=>{const{superscriptData:n}=t;return void 0===n||n?o.createElement("sup",{key:`superscript-${e?.key}`},e):e})(e,n)}}],Rt={rule:()=>!0,decorate:({node:e,element:t,isRoot:n,context:i})=>{if(!n)return o.createElement(o.Fragment,null,t);const r=i.theme.breakout;if(!r)return o.createElement(o.Fragment,null,t);const a=r.rule({node:e});return o.createElement("div",{"data-breakout":a},t)}},Ut=class e{constructor(t,n){this.decorators=[],this.textualDecorators=[],this.decorators=t.filter((e=>!e.isText)).sort(e.sort).reverse(),this.textualDecorators=t.filter((e=>e.isText)),this.context=n}static sort(t,n){const o=t.priority??e.defaultPriority,i=n.priority??e.defaultPriority;return o>i?-1:o<i?1:0}static of(t,n){return new e(t,n)}getDecorators(){return this.decorators}merge(t){return e.of([...this.decorators,...t.getDecorators()],this.context)}apply(e,t,n){return this.decorators.reduce(((e,o)=>o.rule(t)?o.decorate({node:t,context:this.context,element:e,isRoot:n}):e),e)}applyTextual(e){const t=this.textualDecorators.find((t=>t.rule(e)));return t?.decorate({node:e,context:this.context,isRoot:!1})}};Ut.defaultPriority=100;const Nt=[...Mt,Rt];var Ft=Ut;const jt="\u0591-\u07ff\ufb1d-\ufdfd\ufe70-\ufefc",Ht="A-Za-z\xc0-\xd6\xd8-\xf6\xf8-\u02b8\u0300-\u0590\u0800-\u1fff\u200e\u2c00-\ufb1c\ufe00-\ufe6f\ufefd-\uffff",zt=new RegExp("^[^"+Ht+"]*["+jt+"]"),$t=new RegExp("^[^"+jt+"]*["+Ht+"]"),Wt=/\u00a9|\u00ae|[\u2000-\u3300]|\ud83c[\ud000-\udfff]|\ud83d[\ud000-\udfff]|\ud83e[\ud000-\udfff]/g,Gt=e=>{let t="neutral";if(!e)return t;const n=e.replace(Wt,"");return zt.test(n)?t="rtl":$t.test(n)&&(t="ltr"),t};var Kt={indentZero:"_3Xmqv",indentOne:"bX5FV",indentTwo:"aB4j8",indentThree:"HiABr",indentFour:"_4UbEO",indentFive:"_0muYU",indentSix:"FYdyD"};var Yt=({nodeId:e,children:t})=>o.cloneElement(o.Children.only(t),{id:`viewer-${e}`});const qt=({className:e="paywall",index:t=3},n)=>n<t&&e,Zt=e=>"object"==typeof e&&"object"==typeof e?.paywall;var Jt=(e=>(e.AUTO="AUTO",e.LEFT="LEFT",e.RIGHT="RIGHT",e.CENTER="CENTER",e.JUSTIFY="JUSTIFY",e.UNRECOGNIZED="UNRECOGNIZED",e))(Jt||{});const Xt={0:"indentZero",1:"indentOne",2:"indentTwo",3:"indentThree",4:"indentFour",5:"indentFive",6:"indentSix"},Qt={[Jt.LEFT]:"left",[Jt.CENTER]:"center",[Jt.RIGHT]:"right",[Jt.JUSTIFY]:"flex-start"};var en={fontElementMap:"BFlyk",quote:"zQEih"};const tn=({node:e,nodeIndex:t,RicosNodesRenderer:n})=>{const{seoMode:i}=(0,o.useContext)(At.u),{parentNode:r}=(0,o.useContext)(Vt),{indentation:a=0}=e.blockquoteData||{},s=e.nodes?.[0],l=`${s?.type?.toLowerCase()}Data`,{textAlignment:c}=s?.[l]?.textStyle||{},d=ue(en.quote,a>0&&Kt[Xt[a]],!r&&Zt(i)&&qt(i?.paywall||{},t)),u=en.child;return o.createElement(Yt,{nodeId:e.id},o.createElement("div",{style:{display:"flex",justifyContent:Qt[c]},dir:Gt(e.nodes?.[0]?.nodes?.[0]?.textData?.text)||"ltr"},o.createElement("blockquote",{className:d,style:e.style},o.createElement("span",{className:u},o.createElement(Dt,{parentNode:e,parentIndex:t},o.createElement(n,{nodes:e.nodes??[]}))))))};var nn=({node:e,...t})=>o.createElement(tn,{key:e.id,node:e,...t});const on=e=>{let t="";return("string"==typeof e&&e.match(/^\d+(\.\d+)?$/)||"number"==typeof e)&&(t="em"),`max(0.8em, ${e}${t})`};var rn={alignLeft:"fGhYO",textWrapNowrap:"PB8VU",alignRight:"HBFZL",alignCenter:"YLicF",sizeFullWidth:"vdo9L",renderedInTable:"_9yuvY",sizeOriginal:"OiAo0",sizeContent:"_27RY-",sizeSmall:"ILN2e",sizeSmallCenter:"_47Tgb",flex:"Db9ng",LEFT:"wB32D",CENTER:"-gOPv",RIGHT:"_4sl0y",AUTO:"baWVz",JUSTIFY:"o-EbM"},an="rfGFl EszDD";const sn=({node:e,nodeIndex:t,RicosNodesRenderer:n})=>{const{seoMode:i}=(0,o.useContext)(At.u),{textAlignment:r}=(0,o.useContext)(Tt),{parentNode:a}=(0,o.useContext)(Vt),{textAlignment:s,lineHeight:l}=e.codeBlockData?.textStyle||{},c=ue(an,rn[s||r||Jt.AUTO],!a&&Zt(i)&&qt(i?.paywall||{},t)),d={...e.style,...l&&{lineHeight:on(l)}};return o.createElement(Yt,{nodeId:e.id},o.createElement("pre",{className:c,style:d,dir:"auto"},o.createElement("span",{className:ue(s===Jt.JUSTIFY&&rn[s])},o.createElement(Dt,{parentNode:e,parentIndex:t},o.createElement(n,{nodes:e.nodes??[]})))))};var ln=({node:e,...t})=>o.createElement(sn,{key:e.id,node:e,...t});const cn=o.createContext(null),dn=({directionHandled:e,children:t})=>{const n=(0,o.useMemo)((()=>({directionHandled:e})),[e]);return o.createElement(cn.Provider,{value:n},t)};var un={fontElementMap:"hzbsW",elementSpacing:"_84yKH",headerOne:"EO0tC _84yKH",headerTwo:"DEpv3 _84yKH",headerThree:"tdem5 _84yKH",headerFour:"xdN9R _84yKH",headerFive:"_9Q0Dg _84yKH",headerSix:"D0uEM _84yKH",child:"_9-L3b"};const pn={1:"headerOne",2:"headerTwo",3:"headerThree",4:"headerFour",5:"headerFive",6:"headerSix"},mn={1:"h1",2:"h2",3:"h3",4:"h4",5:"h5",6:"h6"},gn=({node:e,nodeIndex:t,RicosNodesRenderer:n})=>{const{seoMode:i}=(0,o.useContext)(At.u),{textAlignment:r}=(0,o.useContext)(Tt),{parentNode:a}=(0,o.useContext)(Vt),s=(0,o.useContext)(cn),{level:l=0,textStyle:c,indentation:d=0}=e.headingData||{},{textAlignment:u,lineHeight:p}=c||{},m=ue(un[pn[l]??pn[1]],rn[u||r||Jt.AUTO],Kt[Xt[d]],!a&&Zt(i)&&qt(i?.paywall||{},t)),g=ue(un.child,u===Jt.JUSTIFY&&rn[u]),h=mn[l]??mn[1],_=e.style||{};p&&(_.lineHeight=on(p));const E=!e.nodes?.length,f=!E&&"\n"===e.nodes?.[e.nodes.length-1]?.textData?.text?.slice(-1),y=E?o.createElement("br",{role:"presentation"}):o.createElement(Dt,{parentNode:e,parentIndex:t},o.createElement(n,{key:`children-${e.id}`,nodes:e.nodes??[]}));return o.createElement(Yt,{nodeId:e.id},o.createElement(h,{className:m,style:_,dir:s?"":"auto"},o.createElement("span",{className:g},y),f&&o.createElement("br",{role:"presentation"})))};var hn=({node:e,...t})=>o.createElement(gn,{key:e.id,node:e,...t}),_n="J-DMl",En="mSIVT";const fn=({node:e,nodeIndex:t,RicosNodesRenderer:n})=>o.createElement("div",{className:En},o.createElement(Dt,{parentNode:e,parentIndex:t},o.createElement(n,{key:`children-${e.id}`,nodes:e.nodes??[]})));var yn=(e=>(e.QUICK_ACTIONS="QUICK_ACTIONS",e.SIDE="SIDE",e.MOBILE="MOBILE",e.FOOTER="FOOTER",e.STATIC="STATIC",e.INLINE="INLINE",e.PLUGIN="PLUGIN",e.FORMATTING="FORMATTING",e.INSERT_PLUGIN="INSERT_PLUGIN",e.TEXT="ALL-TEXT-TOOLBARS",e.SHORTCUT="SHORTCUT",e.LINK="LINK",e.EXTERNAL="EXTERNAL",e.PLUGIN_MENU="PLUGIN_MENU",e.EMPTY="EMPTY",e))(yn||{});yn.FOOTER,yn.SIDE,yn.MOBILE;const bn=e=>{const t=[];return e.nodes.forEach((e=>{"LAYOUT_CELL"===e.type&&t.push(e.layoutCellData?.colSpan??0)})),(e=>{const t=e.reduce(((e,t)=>e+t),0),n=e.every((e=>e>=3));if(12===t&&n)return e;let o=12-t;const i=e.filter((e=>0===e)),r=t<=12&&o/i.length>=3,a=Math.floor(12/e.length);return e.map(((t,n)=>{let i=a;return r&&(t?i=t:n===e.length-1?i=o:(i=a,o-=a)),i}))})(t).map(((e,n)=>0===n||n===t.length-1?`minmax(0, calc(((100% + 2 * var(--ricos-layout-horizontal-padding)) * ${e} / 12) - var(--ricos-layout-horizontal-padding)))`:`minmax(0, ${e}fr)`)).join(" ")},vn=({node:e,nodeIndex:t,RicosNodesRenderer:n})=>o.createElement("div",{className:_n,style:{"--ricos-layout-column-template":bn(e)}},o.createElement(Dt,{parentNode:e,parentIndex:t},o.createElement(n,{key:`children-${e.id}`,nodes:e.nodes??[]})));var wn="P49FA",An="_3laU7",Tn="m8IH7";const xn=({node:e,nodeIndex:t,RicosNodesRenderer:n})=>{const{textAlignment:i}=(0,o.useContext)(Tt),{visibleIndentation:r}=(0,o.useContext)(It),a=r?.indentation||0,s=e.bulletedListData?.offset||0,l=(r&&r?.nodeId!==e.id?1:0)+a+s,c=ue(An,rn[i||Jt.AUTO]);return o.createElement("ul",{className:c},o.createElement(Lt,{visibleIndentation:{indentation:l,nodeId:e.id}},o.createElement(Dt,{parentNode:e,parentIndex:t},o.createElement(n,{nodes:e.nodes??[]}))))};var Sn=({node:e,...t})=>o.createElement(xn,{key:e.id,node:e,...t}),Cn=n(88187);function In(e){return e<0?0:e>Cn.ll?Cn.ll:e}const Ln={[re.BULLETED_LIST]:["disc","circle","square"],[re.ORDERED_LIST]:["decimal","lower-latin","lower-roman"]};const Vn=({node:e,nodeIndex:t,RicosNodesRenderer:n})=>{const{seoMode:i}=(0,o.useContext)(At.u),{parentIndex:r}=(0,o.useContext)(Vt),{visibleIndentation:a}=(0,o.useContext)(It),s=e.nodes[0],l=`${s?.type?.toLowerCase()}Data`,{textAlignment:c}=s?.[l]?.textStyle||{},u=c&&c===Jt.CENTER,{parentNode:p}=(0,o.useContext)(Vt),m=a&&a?.nodeId===p?.id?In(a?.indentation):0,g=function(e,t){if(!e)return"1.5em";const n=`${e?.type?.slice(0,-5).toLowerCase()}ListData`,o=e[n]?.offset||0,i=t>Cn.ll?o-(t-Cn.ll):In(o);return e?2*In(i)+1.5+"em":""}(p,a?.indentation||0),h=p?function(e,t){const n=Ln[t];return n?n[e%n.length]:""}(m,p.type):"",_=s?.type===d.A.HEADING&&s.headingData?.level,E=ue(Tn,Zt(i)&&qt(i?.paywall||{},t+r)),f=s?.nodes?.[0]?.textData?.decorations?.find((e=>e.type===d.g.FONT_SIZE))?.fontSizeData?.value;return o.createElement("li",{dir:"auto","aria-level":m+1,className:E,style:{textAlign:c,fontSize:f,marginInlineStart:g,listStyleType:h},..._?{"data-heading-level":pn[_]}:{},...u?{"data-list-style-position":"inside"}:{}},o.createElement(dn,{directionHandled:!0},o.createElement(Dt,{parentNode:e,parentIndex:t},o.createElement(n,{nodes:e.nodes??[],shouldParagraphApplyTextStyle:!0}))))};var Dn=({node:e,...t})=>o.createElement(Vn,{key:e.id,node:e,...t});const Pn=({node:e,nodeIndex:t,RicosNodesRenderer:n})=>{const{textAlignment:i}=(0,o.useContext)(Tt),{visibleIndentation:r}=(0,o.useContext)(It),a=r?.indentation||0,s=e.orderedListData?.offset||0,l=(r&&r?.nodeId!==e.id?1:0)+a+s,c=ue(wn,rn[i||Jt.AUTO]);return o.createElement("ol",{className:c,start:e.orderedListData?.start},o.createElement(Lt,{visibleIndentation:{indentation:l,nodeId:e.id}},o.createElement(Dt,{parentNode:e,parentIndex:t},o.createElement(n,{nodes:e.nodes??[]}))))};var On=({node:e,...t})=>o.createElement(Pn,{key:e.id,node:e,...t}),kn="wHDUb cFdAL",Bn="UR7ou";const Mn=({node:e,nodeIndex:t,RicosNodesRenderer:n,shouldParagraphApplyTextStyle:i})=>{const{seoMode:r}=(0,o.useContext)(At.u),{textAlignment:a}=(0,o.useContext)(Tt),{parentNode:s}=(0,o.useContext)(Vt),l=(0,o.useContext)(cn),{indentation:c=0}=e.paragraphData||{},{textAlignment:d,lineHeight:u}=e.paragraphData?.textStyle||{},p=d!==Jt.AUTO&&d?d:a,m=ue(i&&kn,rn[p||Jt.AUTO],Kt[Xt[c]],!s&&Zt(r)&&qt(r?.paywall||{},t)),g=ue(Bn,d===Jt.JUSTIFY&&rn[d]),h=!e.nodes?.length,_=!h&&"\n"===e.nodes?.[e.nodes?.length-1]?.textData?.text?.slice(-1),E=h||!i?"div":"p",f={...e.style,...u&&{lineHeight:on(u)}},y=h?o.createElement("br",{role:"presentation"}):o.createElement(Dt,{parentNode:e,parentIndex:t},o.createElement(n,{key:`children-${e.id}`,nodes:e.nodes??[]}));return o.createElement(Yt,{nodeId:e.id},o.createElement(E,{className:m,style:f,dir:l?"":"auto"},o.createElement("span",{className:g},y),_&&o.createElement("br",{role:"presentation"})))};var Rn=({node:e,...t})=>o.createElement(Mn,{key:e.id,node:e,...t});const Un=({node:e})=>{const{text:t}=e.textData||{},{nodeViewDecorators:n}=(0,o.useContext)(vt);return t&&""!==t?o.createElement("span",null,n.applyTextual(e)||t):o.createElement("br",{role:"presentation"})};var Nn,Fn=(Nn=e=>{const{parentId:t,nodeIndex:n}=e,i=`${t}-text-node-${n}`;return o.createElement(Un,{key:i,...e})},function(e){return o.createElement(Vt.Consumer,{key:Math.random().toString().slice(2,9)},(({parentNode:t,parentIndex:n})=>o.createElement(Nn,{...e,parentNode:t,parentIndex:n})))});var jn={[d.A.PARAGRAPH]:Rn,[d.A.TEXT]:Fn,[d.A.BLOCKQUOTE]:nn,[d.A.CODE_BLOCK]:ln,[d.A.HEADING]:hn,[d.A.BULLETED_LIST]:Sn,[d.A.ORDERED_LIST]:On,[d.A.LIST_ITEM]:Dn,[d.A.LAYOUT]:({node:e,...t})=>o.createElement(vn,{key:e.id,node:e,...t}),[d.A.LAYOUT_CELL]:({node:e,...t})=>o.createElement(fn,{key:e.id,node:e,...t})},Hn=n(97821),zn=(e=>(e.NULL_VALUE="NULL_VALUE",e.UNRECOGNIZED="UNRECOGNIZED",e))(zn||{});const $n=e=>"boolean"==typeof e.boolValue,Wn=e=>"string"==typeof e.stringValue,Gn=e=>"number"==typeof e.numberValue,Kn=e=>i.isObject(e.listValue)&&Array.isArray(e.listValue?.values),Yn=e=>i.isObject(e.structValue)&&i.isObject(e.structValue?.fields),qn=e=>e.nullValue===zn.NULL_VALUE,Zn=J((0,B.$1)()),Jn=([e,t])=>({[e]:t}),Xn=e=>(0,V.Fs)(e.fields,Object.entries,P.Tj(he.wu(Qn,V.D_)),P.Tj(Jn),(0,O.KO)(Zn)),Qn=e=>(0,Hn.jJ)(e,null,[[$n,({boolValue:e})=>e],[Gn,({numberValue:e})=>e],[Wn,({stringValue:e})=>e],[qn,()=>null],[Kn,e=>eo(e.listValue||{values:[]})],[Yn,e=>Xn(e.structValue||{fields:{}})]]),eo=e=>(0,V.Fs)(e.values,P.Tj(Qn));var to={alignLeft:"_3wl4x",textWrapNowrap:"uEAeL",alignRight:"c42YG",alignCenter:"MjaZV",sizeFullWidth:"wF6Wp",renderedInTable:"PosPL",sizeOriginal:"eLKFM",sizeContent:"d77zP",sizeSmall:"d1Z5b",sizeSmallCenter:"H5zHO",flex:"fUZlZ",atomic:"_5PBMS",embed:"t2USV",horizontalScrollbar:"Fndl8",pluginContainerMobile:"aUGlZ"};const no=e=>{const t=`${i.camelCase(e.type)}Data`;return e?.[t]?.containerData},oo=e=>{const t=e?.alignment;if(!t)return"";const n=`align${i.upperFirst(t.toLowerCase())}`;return to[n]},io=e=>{const t=e?.width?.size;if(!t||"FULL_WIDTH"===t)return"";const n=`size${i.upperFirst(t.toLowerCase())}`;return to[n]},ro=e=>e?.textWrap?"":to.textWrapNowrap;class ao extends o.Component{constructor(e){super(e),this.state={hasError:!1}}static getDerivedStateFromError(e){return{hasError:!0}}render(){return this.state.hasError?null:this.props.children}}const so={[d.A.IMAGE]:(e,t)=>e?.image?.width&&t&&("number"==typeof e?.containerData?.width?.custom?e.containerData.width.custom:parseFloat(e?.containerData?.width?.custom||"0"))>150},lo=e=>e?.width?.size&&("CONTENT"===e.width.size||"FULL_WIDTH"===e.width?.size),co={[d.A.IMAGE]:e=>lo(e.containerData)?"100%":`${e?.image?.width}px`,[d.A.GIF]:e=>lo(e.containerData)?"100%":`${e.width}px`},uo=({nodeType:e,componentData:t,nodeIndex:n,className:i,children:r})=>{const{containerData:a}=t,{seoMode:s}=(0,o.useContext)(At.u),{isMobile:l}=(0,o.useContext)(Et),{parentNode:c}=(0,o.useContext)(Vt),d=so[e]?.(t,l),u=a?.width?.custom&&!d?`${isNaN(Number(a.width.custom))?a.width.custom:parseInt(a.width.custom)}px`:void 0,p=("ORIGINAL"===a?.width?.size||d)&&co[e]?.(t),m=u||p,g=t&&m?{width:`min(100%, ${m})`}:void 0;return o.createElement("div",{className:ue(i,!c&&Zt(s)&&qt(s?.paywall||{},n)),style:g},r)};var po=n(91936),mo=n(93856),go="_-1JRa",ho="yP-Id";const _o=({children:e,nodeType:t,link:n})=>{const{anchorTarget:i,baseUrl:r,customAnchorScroll:a}=(0,o.useContext)(At.u),{tabIndex:s}=(0,o.useContext)(Et);return n&&t!==d.A.BUTTON?o.createElement("a",{className:ue(ho,"has-custom-focus",{[go]:n}),tabIndex:s,href:n?(0,po.L)(n,r):void 0,target:n.target?`_${n.target.toLowerCase()}`:i,rel:n.rel&&D(n.rel),onClick:n?.anchor?e=>{if(a)return a(e,n?.anchor);const t=`viewer-${n?.anchor}`,o=document.getElementById(t);o&&((0,mo.J)(t),(0,mo.P)(o))}:void 0},e):o.createElement(o.Fragment,null,e)},Eo=e=>void 0!==e,fo=(e={})=>({config:{spoiler:{enabled:!!e.spoiler?.enabled,description:e.spoiler?.description,buttonContent:e.spoiler?.buttonText}}}),yo=({node:e,nodeIndex:t,customClassNames:n,withHorizontalScroll:r,children:a})=>{const{isMobile:s}=(0,o.useContext)(Et),{SpoilerViewerWrapper:l}=(0,o.useContext)(vt),c=`${i.camelCase(e.type)}Data`,d=e[c]||{},{containerData:u,link:p}=d,{alignment:m,size:g,textWrap:h}=n||{},_=ue({[to.pluginContainerMobile]:s,[to.horizontalScrollbar]:r},Eo(m)?m:(e=>oo(no(e)))(e),Eo(g)?g:(e=>io(no(e)))(e),Eo(h)?h:(e=>ro(no(e)))(e)),E=(0,o.useCallback)((n=>o.createElement(uo,{className:_,nodeType:e.type,componentData:d,nodeIndex:t},n)),[_,e.type,u?.width?.custom,u?.width?.size,u?.alignment,t]),f=(0,o.useCallback)((t=>l?o.createElement(l,{className:_,width:u?.width?.custom?parseInt(u.width.custom):void 0,type:e.type,componentData:fo(u)},t):t),[_,e.type,u?.width?.custom,u?.width?.size,u?.alignment,u?.spoiler?.enabled,u?.spoiler?.buttonText,u?.spoiler?.description]),y=(0,o.useCallback)((t=>o.createElement(_o,{link:p,nodeType:e.type},t)),[e.type,p?.url,p?.target,p?.anchor,p?.rel?.nofollow,p?.rel?.noreferrer,p?.rel?.sponsored]);return"function"==typeof a?o.createElement(bo,{node:e},a((0,o.useMemo)((()=>({ContainerElement:e=>E(e.children),SpoilerWrapper:e=>f(e.children),LinkWrapper:e=>y(e.children)})),[E,f,y]))):o.createElement(bo,{node:e},y(f(E(a))))},bo=({children:e,node:t})=>o.createElement(ao,null,o.createElement(Yt,{nodeId:t.id},o.createElement("div",{className:to.atomic},e))),vo=({type:e,context:t,node:n,nodeIndex:i,children:r,pluginComponent:{component:a}})=>{const s=t.config?.[e]||{},l=n.externalData&&Xn(n.externalData)||{},c=(((u=(d=l).config||{}).width||u.alignment||u.textWrap)&&(["content","original","fullWidth","small"].includes(u.size)||["center","left","right"].includes(u.alignment))?(e=>({..."string"==typeof e.size?{width:{size:e.size.toUpperCase()}}:{},..."string"==typeof e.alignment?{alignment:e.alignment.toUpperCase()}:{},...void 0!==e.textWrap?{textWrap:"nowrap"!==e.textWrap}:{}}))(d.config):d.containerData)||{};var d,u;const p={type:e,componentData:l,settings:s,children:r,...t,blockKey:n.id},m={alignment:oo(c),size:io(c),textWrap:ro(c)};return o.createElement(yo,{node:n,nodeIndex:i,customClassNames:m},o.createElement(a,{...p}))},wo=(e,t,n,i)=>{const r=Object.entries(t).reduce(((t,[n,r])=>{return n===d.A.EXTERNAL?{...t,...(a={pluginComponent:r,type:n,context:e,SpoilerViewerWrapper:i},{[d.A.EXTERNAL]:({node:e,nodeIndex:t})=>o.createElement(vo,{key:e.id,node:e,nodeIndex:t,...a})})}:{...t};var a}),{});return{...r,...n}};class Ao{constructor(){this.spoilers={},this.registerSpoiler=(e,t)=>{this.spoilers[e]||(this.spoilers[e]={isActive:!0,notifiers:[]}),this.spoilers[e].notifiers.push(t)},this.toggleSpoiler=e=>{this.spoilers[e].isActive=!this.spoilers[e].isActive,this.spoilers[e].notifiers.forEach((e=>e()))},this.isActive=e=>this.spoilers[e]?.isActive}}const To=({node:e})=>null,xo=({node:e,index:t,isRoot:n,shouldParagraphApplyTextStyle:i})=>{const{renderers:r,nodeViewDecorators:a}=(0,o.useContext)(vt),s=r[e.type];return s?a.apply(o.createElement(s,{node:e,key:e.id||t.toString(),RicosNodesRenderer:So,isRoot:n,nodeIndex:t,NodeViewContainer:yo,shouldParagraphApplyTextStyle:i}),e,!!n):(console.error(`RicosNodeDedcorated Error: no renderer passed for node type - ${e.type}`),o.createElement(To,{node:e}))},So=({nodes:e,isRoot:t,shouldParagraphApplyTextStyle:n,addAnchorsPrefix:i})=>o.createElement(o.Fragment,null,e.flatMap(((e,r)=>[o.createElement(xo,{isRoot:t,node:e,index:r,key:e.id||r.toString(),shouldParagraphApplyTextStyle:n}),...i?[ht(e,r,i)]:[]])));var Co=So;var Io=({content:e,context:t,typeMappers:n,nodeViewRenderers:i,nodeViewDecorators:r,SpoilerViewerWrapper:a,addAnchorsPrefix:s})=>{const{nodes:l}=e,c=new Ao,d=(0,o.useMemo)((()=>({...jn,...wo(t,n,i,a)})),[]),u=(0,o.useMemo)((()=>Ft.of([...Nt,...r],t)),[]),p=(0,o.useMemo)((()=>{const{t:e,locale:n,isMobile:o,theme:i,experiments:r,languageDir:a,jsdomMode:s}=t;return{t:e,locale:n,localeContent:n,isMobile:o,theme:i,experiments:r,languageDir:a,jsdomMode:s,tabIndex:0}}),[t.t,t.locale,t.isMobile,t.theme,t.experiments,t.languageDir,t.jsdomMode]),m=(0,o.useMemo)((()=>new Bt(t.isMobile,t.theme.settingsStyles)),[]);return o.createElement(o.Fragment,null,o.createElement(ft,{...p},o.createElement(bt,{zIndexService:m},o.createElement(wt,{nodeViewDecorators:u,renderers:d,SpoilerViewerWrapper:a},o.createElement(At.T,{contextualData:t},o.createElement(xt,{textAlignment:t.textAlignment},o.createElement(Ct,{spoilerManager:c},o.createElement(Lt,{visibleIndentation:null},o.createElement(Dt,{parentNode:null,parentIndex:0},o.createElement("div",{className:pt.rootedTraversedContent},o.createElement(Co,{nodes:l??[],isRoot:!0,addAnchorsPrefix:s,shouldParagraphApplyTextStyle:!0})))))))))))};const Lo={...pt,...mt},Vo=({t:e,theme:t,isMobile:n=!1,anchorTarget:o,relValue:i,customAnchorScroll:r,config:a,locale:s,disabled:l,seoMode:c,iframeSandboxDomain:d,textAlignment:u,experiments:p,textDirection:m,direction:g,helpers:h,onNodeClick:_,jsdomMode:E,baseUrl:f})=>({t:e,theme:t||{},isMobile:n,anchorTarget:o,relValue:i,customAnchorScroll:r,config:a,helpers:h||{},locale:s||"en",disabled:l,seoMode:c,iframeSandboxDomain:d,disableRightClick:a?.uiSettings?.disableRightClick,textAlignment:u,experiments:p,textDirection:m,languageDir:g||ut(s),onNodeClick:_,jsdomMode:E,baseUrl:f});var Do=e=>{const{config:t={},onHover:n,isMobile:r=!1,addAnchors:a,onMouseOver:s=()=>{},content:l,setRef:c,typeMappers:d,nodeViewRenderers:u,nodeViewDecorators:p,experiments:m={},t:g}=e,_=t[h]?.SpoilerViewerWrapper,E=Vo(e),f=ue(Lo.viewer,{[mt.rtl]:"rtl"===E.textDirection}),y=i.merge({},r?ge.mobile:ge.desktop,E.theme),b=at(y,l?.documentStyle??{});return o.createElement(st.Provider,{key:"RicosViewer",value:{experiments:m,isMobile:r,t:g}},o.createElement("div",{className:ue(Lo.wrapper),dir:E.languageDir,onMouseEnter:e=>n&&n(e),onMouseOver:s,ref:c,style:b,"data-id":"content-viewer"},e.devTools,o.createElement("div",{className:f},o.createElement(_t,{addAnchors:a},o.createElement(Io,{content:l,typeMappers:d,context:E,nodeViewRenderers:u,nodeViewDecorators:p,SpoilerViewerWrapper:_}))),o.createElement(ct,{isMobile:r})))};const Po=function(e){const t=e.en;return Object.keys(e).reduce(((n,o)=>(n[o]={...t,...e[o]},n)),{})}({ar:{VerticalEmbed_Products_Button:"\u0634\u0631\u0627\u0621 \u0627\u0644\u0622\u0646",VerticalEmbed_Events_Button:"\u062a\u0633\u062c\u064a\u0644 \u0627\u0644\u0622\u0646",VerticalEmbed_Bookings_Button:"\u062d\u062c\u0632 \u0627\u0644\u0622\u0646",UploadFile_Viewer_Download:"\u062a\u0646\u0632\u064a\u0644 {{fileType}}",VerticalEmbed_Units_Hour:"{{hours}} \u0633",VerticalEmbed_Units_Minute:"{{minutes}} \u062f"},bg:{VerticalEmbed_Products_Button:"\u041a\u0443\u043f\u0435\u0442\u0435 \u0441\u0435\u0433\u0430",VerticalEmbed_Events_Button:"\u0420\u0435\u0433\u0438\u0441\u0442\u0440\u0438\u0440\u0430\u0439\u0442\u0435 \u0441\u0435 \u0441\u0435\u0433\u0430",VerticalEmbed_Bookings_Button:"\u0420\u0435\u0437\u0435\u0440\u0432\u0438\u0440\u0430\u0439\u0442\u0435 \u0441\u0435\u0433\u0430",UploadFile_Viewer_Download:"\u0418\u0437\u0442\u0435\u0433\u043b\u044f\u043d\u0435 \u043d\u0430 {{fileType}}",VerticalEmbed_Units_Hour:"{{hours}}\u0447.",VerticalEmbed_Units_Minute:"{{minutes}}\u043c\u0438\u043d."},ca:{VerticalEmbed_Products_Button:"Compra ara",VerticalEmbed_Events_Button:"Registra't ara",VerticalEmbed_Bookings_Button:"Reserva ara",UploadFile_Viewer_Download:"Baixa {{fileType}}",VerticalEmbed_Units_Hour:"{{hours}} h",VerticalEmbed_Units_Minute:"{{minutes}} min"},cs:{VerticalEmbed_Products_Button:"Koupit",VerticalEmbed_Events_Button:"Registrovat",VerticalEmbed_Bookings_Button:"Rezervovat",UploadFile_Viewer_Download:"St\xe1hnout {{fileType}}",VerticalEmbed_Units_Hour:"{{hours}}\xa0h",VerticalEmbed_Units_Minute:"{{minutes}}\xa0min"},da:{VerticalEmbed_Products_Button:"K\xf8b nu",VerticalEmbed_Events_Button:"Tilmeld dig",VerticalEmbed_Bookings_Button:"Book nu",UploadFile_Viewer_Download:"Download {{fileType}}",VerticalEmbed_Units_Hour:"{{hours}} t",VerticalEmbed_Units_Minute:"{{minutes}} min"},de:{VerticalEmbed_Products_Button:"Jetzt kaufen",VerticalEmbed_Events_Button:"Jetzt anmelden",VerticalEmbed_Bookings_Button:"Jetzt buchen",UploadFile_Viewer_Download:"{{fileType}} herunterladen",VerticalEmbed_Units_Hour:"{{hours}} Std.",VerticalEmbed_Units_Minute:"{{minutes}} Min."},el:{VerticalEmbed_Products_Button:"\u0391\u03b3\u03bf\u03c1\u03ac \u03c4\u03ce\u03c1\u03b1",VerticalEmbed_Events_Button:"\u0395\u03b3\u03b3\u03c1\u03b1\u03c6\u03b5\u03af\u03c4\u03b5 \u03c4\u03ce\u03c1\u03b1",VerticalEmbed_Bookings_Button:"\u039a\u03ac\u03bd\u03c4\u03b5 \u03ba\u03c1\u03ac\u03c4\u03b7\u03c3\u03b7 \u03c4\u03ce\u03c1\u03b1",UploadFile_Viewer_Download:"\u039b\u03ae\u03c8\u03b7 {{fileType}}",VerticalEmbed_Units_Hour:"{{hours}}\u03c9",VerticalEmbed_Units_Minute:"{{minutes}} \u03bb\u03b5\u03c0\u03c4\u03ac"},en:{Poll_Viewer_VoteCount_Modal_Voters_Header:"{{count}} person voted for this answer",Poll_Viewer_VoteCount_Modal_Voters_Header_plural:"{{count}} people voted for this answer",Poll_Viewer_VoteCount_Modal_Voters_Anonymous:"+{{anonNumber}} anonymous",Poll_Viewer_VoteCount_Modal_Voters_Private_Voter:"Private User",Poll_Viewer_VoteCount_Modal_Voters_CTA_Follow:"Follow",Poll_Viewer_ShowAllOptions_CTA:"See All Options",Poll_Viewer_ShowAllResults_CTA:"See All Results",Poll_Viewer_ShowLess_CTA:"Show Less",Poll_Viewer_Visibility_Owner_Footer:"Only you can see the results.",Poll_Viewer_Multiselect_Footer:"You can vote for more than one answer.",Poll_Viewer_Toast_Error_Connection:"No internet. Check your connection.",Poll_Viewer_Toast_Error_ServerDown:"Couldn't connect to server. Try again later.",Poll_Viewer_VoteCount:"{{number}} votes",Poll_Viewer_Option_AriaLabel_Checked:"checked",Poll_Viewer_Option_AriaLabel_Unchecked:"unchecked",Audio_Plugin_Cover_Alt:"Audio cover",Audio_Plugin_Volume_Control_Mute_AriaLabel:"Mute",Audio_Plugin_Volume_Control_Unmute_AriaLabel:"Unmute",Audio_Plugin_Volume_Control_Slider_AriaLabel:"Volume",Audio_Plugin_More_Actions_Button_AriaLabel:"Show more audio controls",Audio_Plugin_Elapsed_Time_Slider_AriaLabel:"Seek slider",Polls_Plugin_User_Avatar_Alt:"User avatar",UploadFile_Viewer_Download:"Download {{fileType}}",UploadFile_Viewer_Loader:"Loading {{fileType}}",Viewer_Toolbar_Twitter_Tooltip:"Tweet this",Preview_ReadMore_Label:"Read more",Preview_SeeFullPost_Label:"See Full Post",Spoiler_Insert_Tooltip:"Spoiler",Spoiler_Reveal_Tooltip:"Click to reveal",Spoiler_Reveal_Image_Placeholder:"This image contains a spoiler",Spoiler_Reveal_Image_CTA:"Reveal Image",Spoiler_Reveal_Gallery_Placeholder:"This gallery contains a spoiler",Spoiler_Reveal_Gallery_CTA:"Reveal Gallery",Spoiler_Reveal_Video_Placeholder:"This video contains a spoiler",Spoiler_Reveal_Video_CTA:"Reveal Video",CollapsibleListPlugin_ExpandButton_AriaLabel:"Item is expanded",CollapsibleListPlugin_CollapseButton_AriaLabel:"Item is collapsed",VerticalEmbed_Bookings_Button:"Book Now",VerticalEmbed_Events_Button:"Register Now",VerticalEmbed_Products_Button:"Buy Now",VerticalEmbed_Disabled_Button_Tooltip:"Product page will be available once you publish your site.",VerticalEmbed_Units_Hour:"{{hours}}h",VerticalEmbed_Units_Minute:"{{minutes}}min",Image_Expand_Button_AriaLabel:"Expand image",Fullscreen_Close_Button_AriaLabel:"Close",Fullscreen_Expand_Button_AriaLabel:"Expand",Fullscreen_Shrink_Button_AriaLabel:"Shrink"},es:{VerticalEmbed_Products_Button:"Comprar ahora",VerticalEmbed_Events_Button:"Registrarse",VerticalEmbed_Bookings_Button:"Reservar ahora",UploadFile_Viewer_Download:"Descargar {{fileType}}",VerticalEmbed_Units_Hour:"{{hours}}h",VerticalEmbed_Units_Minute:"{{minutes}}min"},fi:{VerticalEmbed_Products_Button:"Osta nyt",VerticalEmbed_Events_Button:"Rekister\xf6idy nyt",VerticalEmbed_Bookings_Button:"Varaa nyt",UploadFile_Viewer_Download:"Lataa {{fileType}}",VerticalEmbed_Units_Hour:"{{hours}} t",VerticalEmbed_Units_Minute:"{{minutes}} min"},fr:{VerticalEmbed_Products_Button:"Acheter",VerticalEmbed_Events_Button:"S'inscrire",VerticalEmbed_Bookings_Button:"R\xe9server",UploadFile_Viewer_Download:"T\xe9l\xe9charger {{fileType}}",VerticalEmbed_Units_Hour:"{{hours}} h",VerticalEmbed_Units_Minute:"{{minutes}} min"},he:{VerticalEmbed_Products_Button:"\u05dc\u05e7\u05e0\u05d9\u05d9\u05d4",VerticalEmbed_Events_Button:"\u05dc\u05d4\u05e8\u05e9\u05de\u05d4",VerticalEmbed_Bookings_Button:"\u05d4\u05d6\u05de\u05d9\u05e0\u05d5 \u05e2\u05db\u05e9\u05d9\u05d5",UploadFile_Viewer_Download:"\u05d4\u05d5\u05e8\u05d9\u05d3\u05d5 \u05d0\u05ea {{fileType}}",VerticalEmbed_Units_Hour:"{{hours}} \u05e9\u05e2\u05d5\u05ea",VerticalEmbed_Units_Minute:"{{minutes}} \u05d3\u05e7\u05d5\u05ea"},hi:{VerticalEmbed_Products_Button:"\u0905\u092d\u0940 \u0916\u0930\u0940\u0926\u0947\u0902",VerticalEmbed_Events_Button:"\u0905\u092d\u0940 \u092a\u0902\u091c\u0940\u0915\u0930\u0923 \u0915\u0930\u0947\u0902",VerticalEmbed_Bookings_Button:"\u0905\u092d\u0940 \u092c\u0941\u0915 \u0915\u0930\u0947\u0902",UploadFile_Viewer_Download:"{{fileType}} \u0921\u093e\u0909\u0928\u0932\u094b\u0921 \u0915\u0930\u0947\u0902",VerticalEmbed_Units_Hour:"{{hours}}\u0918\u0902\u091f\u0947",VerticalEmbed_Units_Minute:"{{minutes}}\u092e\u093f\u0928\u091f"},hr:{VerticalEmbed_Products_Button:"Kupi odmah",VerticalEmbed_Events_Button:"Prijavi se odmah",VerticalEmbed_Bookings_Button:"Rezerviraj odmah",UploadFile_Viewer_Download:"Preuzmi {{fileType}}",VerticalEmbed_Units_Hour:"{{hours}}h",VerticalEmbed_Units_Minute:"{{minutes}}min"},hu:{VerticalEmbed_Products_Button:"V\xe1s\xe1rl\xe1s most",VerticalEmbed_Events_Button:"Regisztr\xe1ci\xf3 most",VerticalEmbed_Bookings_Button:"Foglal\xe1s most",UploadFile_Viewer_Download:"{{fileType}} let\xf6lt\xe9se",VerticalEmbed_Units_Hour:"{{hours}} \xf3ra",VerticalEmbed_Units_Minute:"{{minutes}} perc"},id:{VerticalEmbed_Products_Button:"Beli Sekarang",VerticalEmbed_Events_Button:"Daftar Sekarang",VerticalEmbed_Bookings_Button:"Pesan Sekarang",UploadFile_Viewer_Download:"Unduh {{fileType}}",VerticalEmbed_Units_Hour:"{{hours}}j",VerticalEmbed_Units_Minute:"{{minutes}}mnt"},it:{VerticalEmbed_Products_Button:"Acquista",VerticalEmbed_Events_Button:"Registrati",VerticalEmbed_Bookings_Button:"Prenota",UploadFile_Viewer_Download:"Scarica {{fileType}}",VerticalEmbed_Units_Hour:"{{hours}} h",VerticalEmbed_Units_Minute:"{{minutes}} min"},ja:{VerticalEmbed_Products_Button:"\u8cfc\u5165\u3059\u308b",VerticalEmbed_Events_Button:"\u4eca\u3059\u3050\u767b\u9332",VerticalEmbed_Bookings_Button:"\u4eca\u3059\u3050\u4e88\u7d04",UploadFile_Viewer_Download:"\u30c0\u30a6\u30f3\u30ed\u30fc\u30c9\uff1a{{fileType}}",VerticalEmbed_Units_Hour:"{{hours}}\u6642\u9593",VerticalEmbed_Units_Minute:"{{minutes}}\u5206"},ko:{VerticalEmbed_Products_Button:"\uad6c\ub9e4\ud558\uae30",VerticalEmbed_Events_Button:"\ub4f1\ub85d\ud558\uae30",VerticalEmbed_Bookings_Button:"\uc608\uc57d\ud558\uae30",UploadFile_Viewer_Download:"{{fileType}} \ub2e4\uc6b4\ub85c\ub4dc",VerticalEmbed_Units_Hour:"{{hours}}\uc2dc\uac04",VerticalEmbed_Units_Minute:"{{minutes}}\ubd84"},lt:{VerticalEmbed_Products_Button:"Pirkti",VerticalEmbed_Events_Button:"Registruotis",VerticalEmbed_Bookings_Button:"U\u017esakyti",UploadFile_Viewer_Download:"Atsisi\u0173sti {{fileType}}",VerticalEmbed_Units_Hour:"{{hours}}\xa0val.",VerticalEmbed_Units_Minute:"{{minutes}}\xa0min."},lv:{VerticalEmbed_Products_Button:"P\u0113rciet t\u016bl\u012bt",VerticalEmbed_Events_Button:"Re\u0123istr\u0113jieties t\u016bl\u012bt",VerticalEmbed_Bookings_Button:"Rezerv\u0113jiet t\u016bl\u012bt",UploadFile_Viewer_Download:"Lejupiel\u0101d\u0113t {{fileType}}",VerticalEmbed_Units_Hour:"{{hours}}\xa0h",VerticalEmbed_Units_Minute:"{{minutes}}\xa0min"},ms:{VerticalEmbed_Products_Button:"Beli Sekarang",VerticalEmbed_Events_Button:"Daftar Sekarang",VerticalEmbed_Bookings_Button:"Tempah Sekarang",UploadFile_Viewer_Download:"Muat turun {{fileType}}",VerticalEmbed_Units_Hour:"{{jam}}j",VerticalEmbed_Units_Minute:"{{minit}}min"},nl:{VerticalEmbed_Products_Button:"Nu kopen",VerticalEmbed_Events_Button:"Nu registreren",VerticalEmbed_Bookings_Button:"Nu boeken",UploadFile_Viewer_Download:"Download {{fileType}}",VerticalEmbed_Units_Hour:"{{uur}}u",VerticalEmbed_Units_Minute:"{{minuten}}min"},no:{VerticalEmbed_Products_Button:"Kj\xf8p n\xe5",VerticalEmbed_Events_Button:"Registrer deg n\xe5",VerticalEmbed_Bookings_Button:"Book n\xe5",UploadFile_Viewer_Download:"Last ned {{fileType}}",VerticalEmbed_Units_Hour:"{{hours}}t",VerticalEmbed_Units_Minute:"{{minutes}}min"},pl:{VerticalEmbed_Products_Button:"Kup teraz",VerticalEmbed_Events_Button:"Zarejestruj si\u0119 teraz",VerticalEmbed_Bookings_Button:"Zarezerwuj teraz",UploadFile_Viewer_Download:"Pobierz {{fileType}}",VerticalEmbed_Units_Hour:"{{hours}}h",VerticalEmbed_Units_Minute:"{{minutes}}min"},pt:{VerticalEmbed_Products_Button:"Comprar",VerticalEmbed_Events_Button:"Registrar-se",VerticalEmbed_Bookings_Button:"Agendar",UploadFile_Viewer_Download:"Fazer download de {{fileType}}",VerticalEmbed_Units_Hour:"{{hours}}h",VerticalEmbed_Units_Minute:"{{minutes}}min"},ro:{VerticalEmbed_Products_Button:"Cump\u0103r\u0103 acum",VerticalEmbed_Events_Button:"\xcenregistreaz\u0103-te acum",VerticalEmbed_Bookings_Button:"Rezerv\u0103 acum",UploadFile_Viewer_Download:"Descarc\u0103 {{fileType}}",VerticalEmbed_Units_Hour:"{{hours}} h",VerticalEmbed_Units_Minute:"{{minutes}} min"},ru:{VerticalEmbed_Products_Button:"\u041a\u0443\u043f\u0438\u0442\u044c",VerticalEmbed_Events_Button:"\u0417\u0430\u0440\u0435\u0433\u0438\u0441\u0442\u0440\u0438\u0440\u043e\u0432\u0430\u0442\u044c\u0441\u044f",VerticalEmbed_Bookings_Button:"\u0417\u0430\u0431\u0440\u043e\u043d\u0438\u0440\u043e\u0432\u0430\u0442\u044c",UploadFile_Viewer_Download:"\u0421\u043a\u0430\u0447\u0430\u0442\u044c {{fileType}}",VerticalEmbed_Units_Hour:"{{hours}} \u0447.",VerticalEmbed_Units_Minute:"{{minutes}} \u043c\u0438\u043d."},sk:{VerticalEmbed_Products_Button:"K\xfapi\u0165 teraz",VerticalEmbed_Events_Button:"Zaregistrujte sa teraz",VerticalEmbed_Bookings_Button:"Rezervova\u0165 teraz",UploadFile_Viewer_Download:"Stiahnu\u0165 {{fileType}}",VerticalEmbed_Units_Hour:"{{hours}}h",VerticalEmbed_Units_Minute:"{{minutes}}min"},sl:{VerticalEmbed_Products_Button:"Kupi zdaj",VerticalEmbed_Events_Button:"Registrirajte se zdaj",VerticalEmbed_Bookings_Button:"Rezerviraj zdaj",UploadFile_Viewer_Download:"Prenos {{fileType}}",VerticalEmbed_Units_Hour:"{{hours}}h",VerticalEmbed_Units_Minute:"{{minutes}} min"},sv:{VerticalEmbed_Products_Button:"K\xf6p nu",VerticalEmbed_Events_Button:"Registrera dig nu",VerticalEmbed_Bookings_Button:"Boka nu",UploadFile_Viewer_Download:"Ladda ner {{fileType}}",VerticalEmbed_Units_Hour:"{{hours}} t",VerticalEmbed_Units_Minute:"{{minutes}} min"},th:{VerticalEmbed_Products_Button:"\u0e0b\u0e37\u0e49\u0e2d\u0e40\u0e25\u0e22",VerticalEmbed_Events_Button:"\u0e25\u0e07\u0e17\u0e30\u0e40\u0e1a\u0e35\u0e22\u0e19\u0e15\u0e2d\u0e19\u0e19\u0e35\u0e49",VerticalEmbed_Bookings_Button:"\u0e08\u0e2d\u0e07\u0e40\u0e25\u0e22",UploadFile_Viewer_Download:"\u0e14\u0e32\u0e27\u0e19\u0e4c\u0e42\u0e2b\u0e25\u0e14 {{fileType}}",VerticalEmbed_Units_Hour:"{{hours}} \u0e0a\u0e21.",VerticalEmbed_Units_Minute:"{{minutes}} \u0e19\u0e32\u0e17\u0e35"},tl:{VerticalEmbed_Products_Button:"Bumili Na",VerticalEmbed_Events_Button:"Magparehistro Na",VerticalEmbed_Bookings_Button:"Mag-book Na",UploadFile_Viewer_Download:"I-download ang {{fileType}}",VerticalEmbed_Units_Hour:"{{hours}}h",VerticalEmbed_Units_Minute:"{{minutes}}min"},tr:{VerticalEmbed_Products_Button:"Sat\u0131n Al",VerticalEmbed_Events_Button:"Hemen Kaydol",VerticalEmbed_Bookings_Button:"Yer Ay\u0131rt",UploadFile_Viewer_Download:"{{fileType}} dosyas\u0131n\u0131 indir",VerticalEmbed_Units_Hour:"{{hours}} sa.",VerticalEmbed_Units_Minute:"{{minutes}} dk."},uk:{VerticalEmbed_Products_Button:"\u041a\u0443\u043f\u0438\u0442\u0438 \u0437\u0430\u0440\u0430\u0437",VerticalEmbed_Events_Button:"\u0417\u0430\u0440\u0435\u0454\u0441\u0442\u0440\u0443\u0432\u0430\u0442\u0438\u0441\u044f",VerticalEmbed_Bookings_Button:"\u0417\u0430\u043f\u0438\u0441\u0430\u0442\u0438\u0441\u044f",UploadFile_Viewer_Download:"\u0417\u0430\u0432\u0430\u043d\u0442\u0430\u0436\u0438\u0442\u0438 {{fileType}}",VerticalEmbed_Units_Hour:"{{hours}} \u0433\u043e\u0434.",VerticalEmbed_Units_Minute:"{{minutes}} \u0445\u0432."},vi:{VerticalEmbed_Products_Button:"Mua ngay",VerticalEmbed_Events_Button:"\u0110\u0103ng k\xfd ngay",VerticalEmbed_Bookings_Button:"\u0110\u0103\u0323t ngay",UploadFile_Viewer_Download:"T\u1ea3i v\u1ec1 {{fileType}}",VerticalEmbed_Units_Hour:"{{hours}} gi\u1edd",VerticalEmbed_Units_Minute:"{{minutes}} ph\xfat"},zh:{VerticalEmbed_Products_Button:"\u7acb\u5373\u8cfc\u8cb7",VerticalEmbed_Events_Button:"\u7acb\u5373\u5831\u540d",VerticalEmbed_Bookings_Button:"\u7acb\u5373\u9810\u8a02",UploadFile_Viewer_Download:"\u4e0b\u8f09 {{fileType}}",VerticalEmbed_Units_Hour:"{{hours}} \u5c0f\u6642",VerticalEmbed_Units_Minute:"{{minutes}} \u5206\u9418"}}),Oo=e=>{const t=e.locale,[i,r]=o.useState((()=>({status:"READY",loadedLocales:["en"],i18nextInstance:ko({activeLocale:t,resources:Po})}))),{i18nextInstance:a,loadedLocales:s}=i;t!==a.language&&a.changeLanguage(t),o.useEffect((()=>{s.includes(t)||"READY"!==i.status||(r((e=>({...e,status:"LOADING"}))),n(66726)(`./messages_${t}.json`).then((e=>e.default)).then((e=>{a.addResourceBundle(t,"translation",e,!0,!0),r((e=>({...e,status:"READY",loadedLocales:[...e.loadedLocales,t]})))})).catch((e=>{console.error(`can't find ${t} locale`,e),r((e=>({...e,status:"ERROR"})))})))}),[t,s]);const l=o.useMemo((()=>a.t.bind(a)),[a]);return function(e){const{[g]:t,[m]:n}=e||{},o=!t?.onExpand||!n?.onExpand;return!((!t||t.disableExpand)&&(!n||n?.disableExpand))&&o}(e.config)?o.createElement(le,{key:"RicosFullscreenProvider",fullscreenProps:e?.fullscreenProps,isMobile:!!e.isMobile,content:e.content,experiments:e.experiments,config:e.config,jsdomMode:e.jsdomMode,t:l},(({config:t,onHover:n})=>o.createElement(Do,{...e,config:t,t:l,onHover:n}))):o.createElement(Do,{...e,t:l})};function ko({activeLocale:e,resources:t}){const n=Object.keys(t).reduce(((e,n)=>(e[n]={translation:t[n]},e)),{}),o=ne.A.createInstance({lng:e,keySeparator:"$",interpolation:{escapeValue:!1},resources:n});return o.init(),o}class Bo extends o.Component{constructor(e){super(e),this.loadTextSelection=()=>{const{textSelectionToolbar:e,isMobile:t}=this.props;if(e&&!t&&!this.state.TextSelectionToolbar){const e=o.lazy((()=>n.e(2524).then(n.bind(n,82524))));this.setState({TextSelectionToolbar:e})}},this.loadLinkPreviewPopover=()=>{const{linkPreviewPopoverFetchData:e,isMobile:t}=this.props;if(e&&!t&&!this.state.LinkPreviewPopover){const e=o.lazy((()=>n.e(6626).then(n.bind(n,56626))));this.setState({LinkPreviewPopover:e})}},this.onMouseOver=()=>{this.loadTextSelection(),this.loadLinkPreviewPopover()},this.getBiCallback=e=>{const{children:t,_rcProps:n}=this.props;return t?.props.helpers?.[e]||n?.helpers?.[e]},this.viewerRef=(0,o.createRef)(),this.state={}}static getDerivedStateFromError(e){return{error:e}}componentDidCatch(e,t){console.error({error:e,errorInfo:t})}componentDidMount(){const e=this.getBiCallback("onViewerLoaded"),t=this.props.content||this.props._rcProps?.content,{pluginsCount:n}=t&&function(e){const{nodes:t}=e,n=(0,A.o)(t??[]).filter((e=>![d.A.PARAGRAPH,d.A.TEXT,d.A.UNRECOGNIZED,d.A.COLLAPSIBLE_ITEM,d.A.COLLAPSIBLE_ITEM_BODY,d.A.COLLAPSIBLE_ITEM_TITLE,d.A.TABLE_CELL,d.A.TABLE_ROW].includes(e.type))).map((e=>{const{type:t}=e,n=T(e),o={},i=S[t]||t===d.A.HEADING&&C[n?.level];return i&&(o.type=i,n&&(o.data=n)),o})).filter((e=>!!e.type)).get(),o=(0,A.o)(t??[]).filter((e=>!!e.textData?.decorations?.some((e=>[d.g.ANCHOR,d.g.LINK,d.g.MENTION].includes(e.type))))).map((e=>{const t=e.textData?.decorations?.find((e=>I[e.type])),n={},o=t?.type&&I[t.type];return o&&(n.type=o,t&&(n.data=T(e))),n})).filter((e=>!!e.type)).get(),i=(0,A.o)(t??[]).map((({textData:e})=>e?.text||"")).get().reduce(((e,t)=>e+(t.match(L)||[]).length),0);return{pluginsCount:{...x(n),...x(o),[u]:i},pluginsDetails:[...n,...o]}}(t)||{};n&&e?.({isPreview:!1,version:a,pluginsCount:n,url:document.URL,contentId:t?.metadata?.id}),((e,t)=>{let n=null;const o=o=>{o.metaKey&&o.shiftKey&&o.altKey&&!n&&(n=e=>{2===e.detail&&t()},e?.addEventListener("click",n,{capture:!0}))},i=t=>{t.metaKey&&t.shiftKey&&t.altKey||n&&(e?.removeEventListener("click",n),n=null)};document.addEventListener("keydown",o),document.addEventListener("keyup",i)})(this.viewerRef.current,(()=>this.setState({debugInvokedWithMouse:!0})))}render(){const{seoSettings:e,linkPreviewPopoverFetchData:t,mediaSettings:n,content:i,debugMode:r}=this.props,{TextSelectionToolbar:s,LinkPreviewPopover:d}=this.state,u=te({...this.props,viewerProps:{seoMode:e,onMouseOver:this.onMouseOver,locale:this.props.locale}});try{return this.state.error?(this.props.onError?.(this.state.error),null):[o.createElement(Oo,{...u,content:this.state.content??u.content,config:u.config,onHover:()=>{},key:"viewer",setRef:this.viewerRef,fullscreenProps:n?.fullscreenProps,devTools:this.state.debugInvokedWithMouse||l(r?.includes("all"))?o.createElement(o.Suspense,null,o.createElement(c,{schema:{getContent:()=>this.state.content??u.content,setContent:e=>this.setState({content:e})},version:a,mode:"viewer",contentState:i,ricosProps:this.props,autoOpen:this.state.debugInvokedWithMouse??!1})):null}),s?o.createElement(o.Suspense,{key:"TextSelectionToolbar",fallback:o.createElement("div",null)},o.createElement(s,{onButtonClick:this.getBiCallback("onViewerAction"),container:this.viewerRef.current})):null,d?o.createElement(o.Suspense,{key:"LinkPreviewPopover",fallback:o.createElement("div",null)},o.createElement(d,{fetchUrlPreviewData:t,container:this.viewerRef.current})):null]}catch(e){return this.props.onError?.(e),null}}}Bo.displayName="RicosViewerNext",Bo.defaultProps={onError:e=>{throw e}};var Mo=Bo;function Ro(e){const t={ricosFeatures:{},wixRicosFeatures:{}};if(!e)return t;const n=function(e){return Object.entries(e).reduce(((e,[t,n])=>{const[,o,i]=t.split("."),r=n.toString().toLowerCase();return e[i]={enabled:""!==r&&"false"!==r,value:n,namespace:o},e}),{})}(i.isFunction(e?.all)?e.all():e);return Object.entries(n).reduce(((e,[t,n])=>("wixRicos"===n?.namespace?e.wixRicosFeatures[t]=n:"ricos"===n?.namespace&&(e.ricosFeatures[t]=n),e)),t)}function Uo(e){return{evid:47,src:116,endpoint:"rich-editor",params:e}}function No(e){return{evid:51,src:116,endpoint:"rich-editor",params:e}}function Fo(e){return{evid:25,src:116,endpoint:"rich-editor",params:e}}function jo(e){return{evid:2,src:116,endpoint:"rich-editor",params:e}}function Ho(e){return{evid:50,src:116,endpoint:"rich-editor",params:e}}function zo(e){return{evid:9,src:116,endpoint:"rich-editor",params:e}}function $o(e){return{evid:13,src:116,endpoint:"rich-editor",params:e}}function Wo(e){return{evid:3,src:116,endpoint:"rich-editor",params:e}}function Go(e){return{evid:46,src:116,endpoint:"rich-editor",params:e}}function Ko(e){return{evid:5,src:116,endpoint:"rich-editor",params:e}}function Yo(e){return{evid:16,src:116,endpoint:"rich-editor",params:e}}function qo(e){return{evid:4,src:116,endpoint:"rich-editor",params:e}}function Zo(e){return{evid:33,src:116,endpoint:"rich-editor",params:e}}function Jo(e){return{evid:8,src:116,endpoint:"rich-editor",params:e}}function Xo(e){return{evid:15,src:116,endpoint:"rich-editor",params:e}}function Qo(e){return{evid:55,src:116,endpoint:"rich-editor",params:e}}function ei(e){return{evid:6,src:116,endpoint:"rich-editor",params:e}}function ti(e){return{evid:14,src:116,endpoint:"rich-editor",params:e}}function ni(e){return{evid:11,src:116,endpoint:"rich-editor",params:e}}function oi(e){return{evid:17,src:116,endpoint:"rich-editor",params:e}}const ii={addPluginLink:function(e){return{evid:26,src:116,endpoint:"rich-editor",params:e}},searchForPlugin:function(e){return{evid:35,src:116,endpoint:"rich-editor",params:e}},searchInsideThePlugin:function(e){return{evid:34,src:116,endpoint:"rich-editor",params:e}},pluginTableColumnRowAction:function(e){return{evid:32,src:116,endpoint:"rich-editor",params:e}},tablePluginAddColumnRow:function(e){return{evid:20,src:116,endpoint:"rich-editor",params:e}},tablePluginDeleteColumnRow:function(e){return{evid:21,src:116,endpoint:"rich-editor",params:e}},tablePluginClickOnOptionMenu:function(e){return{evid:22,src:116,endpoint:"rich-editor",params:e}},tablePluginClickActionFromOptionMenu:function(e){return{evid:23,src:116,endpoint:"rich-editor",params:e}},settingsModalOpenedForPlugin:function(e){return{evid:36,src:116,endpoint:"rich-editor",params:e}},settingsModalClosedForPlugin:function(e){return{evid:37,src:116,endpoint:"rich-editor",params:e}}};function ri(e){return{evid:59,src:116,endpoint:"",params:e}}const ai=e=>({evid:1113,src:76,endpoint:"",params:e}),si=(e,t)=>{if(t){const n={element_id:e||"",url:"undefined"!=typeof window?window.location.href:"",elementGroup:"Ricos",actionName:""};switch(t.type){case d.A.BUTTON:return{...n,elementTitle:t.buttonData?.text||"",details:t.buttonData?.link?.rel?JSON.stringify(t.buttonData.link.rel):"",elementType:d.A.BUTTON,value:t.buttonData?.link?.url||""};case d.A.LINK_PREVIEW:return{...n,elementTitle:t.linkPreviewData?.title||"",details:t.linkPreviewData?.link?.rel?JSON.stringify(t.linkPreviewData.link.rel):"",elementType:d.A.LINK_PREVIEW,value:t.linkPreviewData?.link?.url||""};case d.A.APP_EMBED:return{...n,elementTitle:t.appEmbedData?.name||"",details:t.appEmbedData?.type||"",elementType:d.A.APP_EMBED,value:t.appEmbedData?.url||""};case d.A.TEXT:{const e=t.textData?.decorations?.find((({type:e})=>e===d.g.LINK));if(e)return{...n,elementTitle:t.textData?.text||"",details:e?.linkData?.link?.rel?JSON.stringify(e.linkData.link.rel):"",elementType:d.g.LINK,value:e?.linkData?.link?.url||""};break}default:return}}};function li(e){const{bsi:t,metaSiteId:n,visitorId:o,appId:r,instanceId:a,siteOwnerId:s}=e;return i.pickBy({bsi:t,msid:n,_visitorId:o,_appId:r,_instanceId:a,_siteOwnerId:s},i.identity)}class ci{constructor({logger:e,webBiLogger:t,genericParams:o,essentials:r={}}){if(e)this.newLoggerConfig={logger:e,genericParams:i.omit(o,"msid")};else if(!s()){const e=Promise.resolve(t||n.e(7212).then(n.bind(n,84998)).then((e=>e.webBiLogger)));this.oldLogger=e.then((e=>(e.default?.default?e.default:e).factory().updateDefaults({...o,...li(r)}).logger()))}}log(e,t){if(this.newLoggerConfig){const n=e(t),{params:o,...i}=n;this.newLoggerConfig.logger.report?.({params:{...this.newLoggerConfig.genericParams,...o},...i})}else this.oldLogger?this.oldLogger.then((n=>n.report(e(t)))):console.error("[RicosBILogger.ts:log] RicosBILogger was not initialized")}logV2(e){const t={evid:101,src:116,endpoint:"rich-editor",params:e};this.newLoggerConfig?this.newLoggerConfig.logger.report?.(t):this.oldLogger?this.oldLogger.then((e=>e.report(t))):console.error("[RicosBILogger.ts:log] RicosBILogger was not initialized")}}const di=(()=>{let e=(new Date).getTime();return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(t=>{const n=(e+16*Math.random())%16|0;return e=Math.floor(e/16),("x"===t?n:3&n|8).toString(16)}))})();function ui(e,t,n,o,i,r){const{biCallbacks:a,postId:s,contentId:l,...c}=i,d=s||l||"",u=new ci({logger:c.logger,webBiLogger:c.webBiLogger,genericParams:{container:c.consumer||"default",container_id:e,container_platform:c.platform,container_usage:c.usage,post_id:d,rce_session_id:di,msid:t,wixRicosVersion:n,version:o},essentials:c.defaultParams||{}}),p=((e,{postId:t})=>({biLogger:e,onModalAction:({pluginId:t,modalId:n,modalSessionId:o,actionName:i,value:r,actionNumber:a,initialSelection:s,style:l})=>{e.log(ri,{action:i,modal_session_id:o,numActions:a-1,actionNumber:a,modal_id:n,plugin_id:t,content_id:"",uuid:"",value:r,initial_selection:s,style:l})},onPluginAdd:(t,n,o,i)=>e.log(jo,{plugin_id:t,search_term:void 0,entry_point:n,version:o,content_id:i,entry_type:void 0,plugins_details:void 0,pluginUniqueId:void 0,button_name:void 0,plugin_specification:void 0,editor_engine:void 0}),onPluginAddStep:({entryPoint:t,entryType:n,pluginDetails:o,pluginId:i,step:r,version:a,contentId:s})=>e.log(ni,{entry_point:t,entry_type:n,params:void 0,plugin_id:i,pluginUniqueId:o,plugins_details:void 0,step:r,version:a,content_id:s,plugin_specification:void 0}),onPluginAddSuccess:(t,n,o,i,r)=>e.log(ei,{plugin_id:t,entry_point:n,entry_type:void 0,params:JSON.stringify(o),plugins_details:void 0,pluginUniqueId:void 0,version:i,content_id:r,plugin_specification:void 0,editor_engine:void 0,is_tiptap:void 0,mediaOrigin:void 0}),onPluginDelete:({pluginId:t,pluginDetails:n,version:o,contentId:i})=>e.log(qo,{plugin_id:t,plugins_details:JSON.stringify(n||{}),pluginUniqueId:void 0,version:o,content_id:i,editor_engine:void 0}),onPluginChange:(t,n,o,i)=>e.log(Wo,{plugin_id:t,plugins_details:JSON.stringify(n),settingSessionId:void 0,button_name:void 0,category:void 0,origin:void 0,type:void 0,value:void 0,pluginUniqueId:void 0,version:o,content_id:i,editor_engine:void 0}),onPublish:(n,o,i,r,a)=>e.log(Ko,{post_id:n||t,plugins_count:o?JSON.stringify(o):void 0,plugins_details:"",params:void 0,version:r,content_id:a,editor_engine:void 0,is_tiptap:void 0}),onMenuLoad:({menu:t,version:n,contentId:o})=>e.log(oi,{menu:t,version:n,content_id:o}),onPluginModalOpened:({entryPoint:t,entryType:n,pluginDetails:o,pluginId:i,version:r,contentId:a,dropdownValue:s,modalSessionId:l})=>e.log(Zo,{entry_point:t,entry_type:n,plugin_id:i,plugins_details:JSON.stringify(o),version:r,content_id:a,plugin_specification:void 0,editor_engine:void 0,is_tiptap:void 0,dropdown_value:s,modal_session_id:l}),onViewerAction:(t,n,o,i,r)=>{e.log(Jo,{action_name:n,plugin_id:t,value:o,plugins_details:void 0,pluginUniqueId:void 0,content_id:i,params:void 0,productType:void 0,plugin_specification:void 0});const a=si(i,r);a&&e.log(ai,a)},onViewerLoaded:({isPreview:t,url:n,pluginsCount:o,version:i,contentId:r})=>{const a="boolean"==typeof t?t:"string"==typeof t&&"true"===t;e.log(Xo,{preview:a,postURL:n,version:i,content_id:r,pluginsCount:JSON.stringify(o)})},onOpenEditorSuccess:(t,n,o)=>e.log(zo,{toolbarType:n,version:t,content_id:o,editor_engine:void 0,is_tiptap:void 0}),onContentEdited:({version:t,contentId:n})=>e.log(Yo,{version:t,content_id:n,contact_id:void 0,container_type:void 0,editing_time:void 0,paragraph_count:void 0,word_count:void 0,editor_engine:void 0,is_tiptap:void 0}),onInlineToolbarOpen:({version:t,toolbarType:n,pluginId:o,contentId:i})=>e.log(Go,{version:t,content_id:i,toolbarType:n,plugin_id:o}),onToolbarButtonClick:({buttonName:t,category:n,origin:o,pluginDetails:i,pluginId:r,pluginUniqueId:a,type:s,value:l,version:c,contentId:d})=>e.log(Wo,{button_name:t,category:n,origin:o,pluginUniqueId:a,plugin_id:r,plugins_details:i,type:s,value:l,version:c,content_id:d,settingSessionId:void 0,editor_engine:void 0}),onKeyboardShortcutAction:({buttonName:t,pluginId:n,value:o,version:i,contentId:r})=>e.log(Uo,{button_name:t,category:void 0,origin:void 0,pluginUniqueId:void 0,plugin_id:n,plugins_details:void 0,type:void 0,value:o,version:i,content_id:r,settingSessionId:void 0}),onMediaUploadStart:(t,n,o,i,r,a)=>{e.log($o,{plugin_id:n,correlationId:t,fileSize:o,mediaType:i,type:void 0,version:r,content_id:a})},onMediaUploadEnd:(t,n,o,i,r,a,s,l,c)=>{e.log(ti,{plugin_id:n,correlationId:t,duration:o,fileSize:i,mediaType:r,isSuccess:a,errorReason:s,type:void 0,version:l,content_id:c})},onPluginAction:(t,n)=>{if(n.params){const e=JSON.stringify(n.params);n.params=e}const{contentId:o,...i}=n||{};e.log(ii[t],{content_id:o,version:void 0,plugins_details:void 0,...i})},onPluginsPopOverTabSwitch:({pluginId:t,buttonName:n,version:o,contentId:i})=>{e.log(No,{plugin_id:t,button_name:n,content_id:i,version:o,plugins_details:void 0,pluginUniqueId:void 0,settingSessionId:void 0,category:void 0,origin:void 0,value:void 0,type:void 0})},onPluginsPopOverClick:({pluginId:t,buttonName:n,version:o,contentId:i})=>{e.log(Ho,{plugin_id:t,button_name:n,content_id:i,version:o,plugins_details:void 0,pluginUniqueId:void 0,settingSessionId:void 0,category:void 0,origin:void 0,value:void 0,type:void 0})},onChangePluginSettings:({pluginId:t,actionName:n,value:o,version:i,contentId:r})=>{e.log(Fo,{plugin_id:t,actionName:n,content_id:r,value:o,version:i,container:void 0,container_id:void 0,container_platform:void 0,entry_point:void 0,entry_type:void 0,layout:void 0,params:o,pluginUniqueId:void 0,plugins_details:void 0})},mediaPluginsDetails:({pluginId:t,creator:n,title:o,track_duration:i,url:r,type:a,version:s,contentId:l})=>{e.log(Qo,{plugin_id:t,creator:n,title:o,track_duration:i?.toString(),url:r,type:a,version:s,content_id:l,pluginUniqueId:void 0,container:void 0,container_id:void 0,container_platform:void 0,params:void 0})}}))(u,{postId:d});return{biLogger:u,helpers:{onModalAction:e=>{p.onModalAction(e),a?.onModalAction?.(e)},onPluginAdd:(e,t,n,o)=>{p.onPluginAdd(e,t,n,o),a?.onPluginAdd?.(e,t,n,o)},onPluginAddStep:e=>{p.onPluginAddStep(e),a?.onPluginAddStep?.(e)},onPluginAddSuccess:(e,t,n,o,i)=>{p.onPluginAddSuccess(e,t,n,o,i),a?.onPluginAddSuccess?.(e,t,n,o,i)},onPluginDelete:e=>{p.onPluginDelete(e),a?.onPluginDelete?.(e)},onPublish:(e,t,n,o,i)=>{const r=e||s||l;p.onPublish(r,t,n,o,i),a?.onPublish?.(r,t,n,o,i)},onMenuLoad:e=>{p.onMenuLoad(e),a?.onMenuLoad?.(e)},onPluginModalOpened:e=>{p.onPluginModalOpened(e),a?.onPluginModalOpened?.(e)},onViewerAction:(e,t,n,o,i)=>{p.onViewerAction(e,t,n,o,i),a?.onViewerAction?.(e,t,n,o,i)},onViewerLoaded:e=>{p.onViewerLoaded(e),a?.onViewerLoaded?.(e)},onOpenEditorSuccess:(e,t,n)=>{p.onOpenEditorSuccess(e,t,n),a?.onOpenEditorSuccess?.(e,t,n)},onContentEdited:e=>{p.onContentEdited(e),a?.onContentEdited?.(e)},onInlineToolbarOpen:e=>{p.onInlineToolbarOpen(e),a?.onInlineToolbarOpen?.(e)},onToolbarButtonClick:e=>{p.onToolbarButtonClick(e),a?.onToolbarButtonClick?.(e)},onKeyboardShortcutAction:e=>{p.onKeyboardShortcutAction(e),a?.onKeyboardShortcutAction?.(e)},onMediaUploadStart:(e,t,n,o,i,r)=>{p.onMediaUploadStart(e,t,n,o,i,r),a?.onMediaUploadStart?.(e,t,n,o,i,r)},onMediaUploadEnd:(e,t,n,o,i,r,s,l,c)=>{p.onMediaUploadEnd(e,t,n,o,i,r,s,l,c),a?.onMediaUploadEnd?.(e,t,n,o,i,r,s,l,c)},onPluginAction:(e,t)=>{p.onPluginAction(e,t),a?.onPluginAction?.(e,t)},onPluginsPopOverTabSwitch:e=>{p.onPluginsPopOverTabSwitch(e),a?.onPluginsPopOverTabSwitch?.(e)},onPluginsPopOverClick:e=>{p.onPluginsPopOverClick(e),a?.onPluginsPopOverClick?.(e)},onChangePluginSettings:e=>{p.onChangePluginSettings(e),a?.onChangePluginSettings?.(e)},mediaPluginsDetails:e=>{p.mediaPluginsDetails(e),a?.mediaPluginsDetails?.(e)}}}}var pi=n(27060),mi=(e=>(e.CREATOR="CREATOR",e.VOTERS="VOTERS",e.EVERYONE="EVERYONE",e.UNRECOGNIZED="UNRECOGNIZED",e))(mi||{}),gi=(e=>(e.SITE_MEMBERS="SITE_MEMBERS",e.ALL="ALL",e.UNRECOGNIZED="UNRECOGNIZED",e))(gi||{});const hi=e=>({id:e.id,title:e.title,createdBy:e.createdBy,count:e.count,anonymousCount:e.anonymousCount,creatorFlag:e.creatorFlag,ownVotes:e.ownVotes,image:{src:{url:e.mediaId}},options:e.options.map(_i),settings:{permissions:{view:Ei(e.settings.resultsVisibility),vote:fi(e.settings.voteRole),allowMultipleVotes:e.settings.multipleVotes},showVoters:e.settings.votersDisplay,showVotesCount:e.settings.votesDisplay}}),_i=e=>({id:e.id,title:e.title,image:{src:{url:e.mediaId}},count:e.count,anonymousCount:e.anonymousCount,rating:e.rating,latestVoters:e.latestVoters}),Ei=e=>{switch(e){case"ALWAYS":return mi.EVERYONE;case"VOTERS_ONLY":return mi.VOTERS;default:return mi.CREATOR}},fi=e=>{switch(e){case"ALL":return gi.ALL;case"SITE_MEMBERS":return gi.SITE_MEMBERS;default:return gi.UNRECOGNIZED}},yi=e=>({title:e.title,mediaId:e.image.src.url,ownVotes:[],options:e.options.map((e=>({title:e.title,mediaId:e.image.src.url}))),settings:{resultsVisibility:vi(e.settings.permissions?.view),multipleVotes:!!e.settings.permissions?.allowMultipleVotes,voteRole:wi(e.settings.permissions?.vote),votersDisplay:!!e.settings.showVoters,votesDisplay:!!e.settings.showVotesCount}}),bi=e=>({id:e.id,title:e.title,mediaId:e.image.src.url,count:e.count,anonymousCount:e.anonymousCount,rating:e.rating,latestVoters:e.latestVoters}),vi=e=>"EVERYONE"===e?"ALWAYS":"VOTERS"===e?"VOTERS_ONLY":"ONLY_ME",wi=e=>e===gi.UNRECOGNIZED||void 0===e?"ALL":e,Ai="/_serverless/polls-client/v1";class Ti{constructor(e){this.getWixClientPromise=e,this.fetchPoll=async e=>{try{const t=await this.getWixClientPromise(),n=await t.fetchWithAuth(`${Ai}/questions/${e}`),o=await n.json();return hi(o.question)}catch(e){if(404===e?.response?.status)throw new Error("POLL_NOT_FOUND");throw e}},this.createPoll=async e=>{const t=await this.getWixClientPromise(),n=await t.fetchWithAuth(`${Ai}/questions`,{method:"POST",body:JSON.stringify({question:yi(e)}),headers:{"Content-Type":"application/json"}}),o=await n.json();return hi(o.question)},this.updatePoll=async e=>{try{const n=await this.getWixClientPromise(),o=await n.fetchWithAuth(`${Ai}/questions/${e.id}`,{method:"PATCH",body:JSON.stringify({question:(t=e,{...yi(t),options:t.options.map(bi),id:t.id,createdBy:t.createdBy,count:t.count,anonymousCount:t.anonymousCount,creatorFlag:t.creatorFlag,ownVotes:t.ownVotes})}),headers:{"Content-Type":"application/json"}}),i=await o.json();return hi(i.question)}catch(e){if(404===e?.response?.status)throw new Error("POLL_NOT_FOUND");throw e}var t},this.vote=async(e,t)=>{const n=await this.getWixClientPromise(),o=await n.fetchWithAuth(`${Ai}/questions/${e}/vote`,{method:"POST",body:JSON.stringify({optionId:t}),headers:{"Content-Type":"application/json"}}),i=await o.json();return hi(i.question)},this.unvote=async(e,t)=>{const n=await this.getWixClientPromise(),o=await n.fetchWithAuth(`${Ai}/questions/${e}/unvote`,{method:"POST",body:JSON.stringify({optionId:t}),headers:{"Content-Type":"application/json"}}),i=await o.json();return hi(i.question)},this.getVoters=async(e,t,n)=>{const o=`${Ai}/questions/${e}/options/${t}/voters`,i=new URLSearchParams;Object.entries(n).forEach((([e,t])=>{void 0!==t&&i.set(e,t.toString())}));const r=await this.getWixClientPromise();return await r.fetchWithAuth(`${o}?${i.toString()}`,{method:"GET",headers:{"Content-Type":"application/json"}}).then((e=>e.json()))},this.requestOptions={baseURL:Ai}}}const xi=new Map;const Si=e=>e?.map?.((e=>({siteMemberId:e._id??void 0,imageUrl:e.profile?.photo?.url??void 0,name:{nick:e.profile?.nickname??void 0}})));function Ci(e,t){if(!e)return{};const o="undefined"==typeof window?void 0:(()=>{const e=(o=async e=>{const o=await((e,t)=>Promise.all([t(),n.e(2924).then(n.bind(n,53053)).then((e=>e.members))]).then((([t,n])=>t.use(n).queryMembers().in("_id",e).find().then((e=>e.items)).catch((e=>(console.error("[pollsStrategy] fetchMembers failed. error:",e),[]))).then(Si))))(e,t);return e.map((e=>o.find((t=>t.siteMemberId===e))||{siteMemberId:e}))},new pi((e=>o(e).then((t=>e.map((e=>t.find((t=>t.siteMemberId===e))||new Error("Member not found")))))),{cacheMap:xi}));var o;return e.loadMany.bind(e)})();return{config:{[b]:{pollServiceApi:new Ti(t),siteToken:e,getSiteMembers:o}}}}const Ii=(e,t)=>!!t?.some((t=>t.type===e)),Li={instanceId:"",appDefId:"",metaSiteId:"",uid:"",siteOwnerId:""};function Vi(e){if(!e)return Li;const{data:t}=function(e){console.assert(e,"instance is mandatory");const t=e.split(".");console.assert(t.length>=2,"instance must contain data and signature separated by .");const[n,o]=t.slice(-2);return{data:o,signature:n}}(e),n=s()?global.Buffer.from(t,"base64").toString("utf8"):atob(t),o=JSON.parse(n);return{...Li,...o}}const Di={AUDIO:"music",VIDEO:"video",IMAGE:"picture",SECURE_IMAGE:"secure_picture",DOCUMENT:"document",STATIC:"static_file"};function Pi(e,t){return({id:n,type:o,privacy:i})=>{if("private"!==i&&function(e){return e.includes("/")}(n)){const t=((e,t)=>({picture:"static.wixstatic.com",video:"video.wixstatic.com",music:"music.wixstatic.com",document:`${t}.usrfiles.com`,archive:`${t}.usrfiles.com`}[e]))((e=>{const t=e.split(".").pop()?.toLowerCase()||"";return["jpg","png","gif","jpeg","jpe","jfif","bmp","heic","heif","tiff","tif","webp"].includes(t)?Di.IMAGE:["avi","mpeg","mpg","mpe","mp4","mkv","webm","mov","ogv","vob","m4v","3gp","divx","xvid","mxf","wmv","m1v","flv","m2ts"].includes(t)?Di.VIDEO:["mp3","wav","flac","m4a","wma","aac","aif","aiff"].includes(t)?Di.AUDIO:["zip","rar","tar","gz","gzip","jar","7z","fgz","webarchive"].includes(t)?"archive":Di.DOCUMENT})(o),e);return Promise.resolve(`https://${t}/${n}`)}return function(e){return async t=>{const n=new URLSearchParams({fileId:t}),o=await fetch(`/_serverless/ricos-media-services/download-url?${n.toString()}`,{headers:{Authorization:e}});return await o.json()}}(t)(n).then((e=>Array.isArray(e)&&e[0]?.path))}}const Oi=e=>`https://video.wixstatic.com/${e.pathname}`,ki=e=>e.pathname.startsWith("media/")?`https://static.wixstatic.com/${e.pathname}`:`https://static.wixstatic.com/media/${e.pathname}`,Bi=e=>`https://static.wixstatic.com/${e.id}`;const Mi=({experiments:e={},plugins:t,instance:n,wixRicosVersion:o,ricosVersion:r,biSettings:a,getWixClientPromise:s,msid:l="",viewer:c=!1,content:d})=>{const u=e=>Ii(e,t),{metaSiteId:m,appDefId:g}=Vi(n),h=m||l,{ricosFeatures:_,wixRicosFeatures:E}=e,w=u(b)&&(!c||c&&(e=>{if(!e)return!1;const t=e.nodes;return t?.some((e=>[b,v].includes(e.type)))})(d));return i.merge({experiments:_},ui(g,h,o,r,a),function(e,t,n){return n?{config:{...e(f)&&{[f]:{getVideoUrl:Oi,getThumbnailUrl:ki}},...e(y)&&{[y]:{getAudioUrl:Bi}},...e(p)&&n&&{[p]:{resolveFileUrl:Pi(n,t)}}},helpers:{getImageUrl:({file_name:e})=>`https://static.wixstatic.com/media/${e}`}}:{config:{}}}(u,n,h),w&&Ci(n,s),function(e){const t={};if(e){const n="filesusr";t.iframeSandboxDomain=`https://${e}.${n}.com`}return t}(h),E)},Ri={palette:{},paletteConfig:{settingsActionColor:"#000000"},settingsStyles:{buttons:{borderRadius:"0px",textColor:"#FFFFFF"},dividers:{color:"#ededed",height:"12px"},icons:{color:"#000000"},inputs:{borderColor:"#000000",borderRadius:"0px",placeholderColor:"#646464"},text:{color:"#000000",fontFamily:"HelveticaNeue,Helvetica,Arial"},whitebox:{borderRadius:"0px",boxShadow:"0 8px 8px 0 rgba(22, 45, 61, 0.12), 0 3px 24px 0 rgba(22, 45, 61, 0.18)"},colorPaletteSelectedButton:{borderRadius:"0px"},smallButtons:{borderRadius:"0px"},bgColor:{backgroundColor:"rgba(0,0,0,.2)"},disabled:{backgroundColor:"#969696"},hover:{color:"#4D4D4D"}}},Ui={nativeStyle:{text:{fontFamily:"Madefor",color:"#20303C"},primaryText:{fontWeight:700},bgColor:{backgroundColor:"#F8F9FA"},secondary:{backgroundColor:"#E8ECF0"},whitebox:{borderRadius:"8px"},coverImage:{height:"70px",width:"70px",margin:"8px",border:"1px solid rgba(110, 120, 129, 0.1)"}}},Ni="#116DFF",Fi={palette:{actionColor:Ni,bgColor:"#FFFFFF",textColor:"#000000"},paletteConfig:{focusActionColor:Ni,settingsActionColor:Ni},settingsStyles:{buttons:{borderRadius:"18px",textColor:"#FFFFFF"},dividers:{color:"#DFE5EB",height:"12px"},icons:{color:"#333853"},inputs:{borderColor:"#A8CAFF",borderRadius:"6px",placeholderColor:"#868AA5"},text:{color:"#333853",fontFamily:"Madefor"},whitebox:{borderRadius:"8px",boxShadow:"0 8px 8px 0 rgba(22, 45, 61, 0.12), 0 3px 24px 0 rgba(22, 45, 61, 0.18)"},colorPaletteSelectedButton:{borderRadius:"6px"},smallButtons:{borderRadius:"4px"},bgColor:{backgroundColor:"#D6E6FE"},disabled:{backgroundColor:"#0006244d"},hover:{color:"#33385380"}}},ji={customStyles:{editor:{addPluginButton:{plusIconColor:Ni}}}},Hi=({useWixStyles:e,useNativeStyles:t,...n}={},o=!1)=>{return t?(r=n,i.merge({},Ui,r||{})):e??o?(e=>i.merge({},Fi,e||{},ji))(n):(e=>i.merge({},e||{},Ri))(n);var r},zi=e=>{const t=new URL("https://www.wixapis.com/ricos/v1/oembeds");return t.searchParams.append("url",(e=>e.substring(0,5).includes("http")?e:"http://"+e)(e)),t.searchParams.append("options.maxWidth","980"),t},$i=["wix","facebook"],Wi=e=>async t=>{try{return(async(e,t)=>(await fetch(zi(e),{method:"GET",headers:{"Content-Type":"application/json",Authorization:t}})).json())(t,e).then((e=>{const{html:t,...n}=e,{provider_name:o}=n;return $i.includes((o||"").toLowerCase())?{...n}:{...n,html:t}}))}catch{return new Promise((e=>{setTimeout((()=>e({})),1)}))}},Gi="?d=",Ki=/^https:\/\/www.wix.app/,Yi=(e,t)=>{const n=e.match(Ki),o=n?(e=>e.split(Gi)[1])(e):e,i=(e=>{const t=Math.max(e.indexOf("/product-page/"),e.indexOf("/stores/"),e.indexOf("/bookings-checkout/"),e.indexOf("/service-page/"),e.indexOf("/event-details/"),e.indexOf("/events/"),e.indexOf("/event-details-registration/"));return t>=0?e.slice(t):void 0})(o),r=void 0!==i?(e=>e.replace(/\/$/,""))(t)+i:o;return n?((e,t)=>`${e.split(Gi)[0]}${Gi}${t}`)(e,r):r};const qi=(e,t,n)=>t&&Ii(E,e)?function(e,t=!1){return{config:{[E]:{changeBaseUrl:t=>Yi(t,e),disabled:t}}}}(t,n):{config:{}};var Zi;const Ji=(Zi=Mo,function(e){const{children:t,biSettings:a,instance:s,plugins:l,theme:c,wixExperiments:d,allowLinkPreviewPopover:u,msid:p,baseUrl:m,previewMode:g,content:_,...E}=e,f=Ro(d),{getWixClientPromise:y}=((e,t)=>{const i=o.useMemo((()=>{let t;return()=>t||(t=n.e(7623).then(n.bind(n,15834)).then((t=>t.createClient({auth:{getAuthHeaders:()=>({headers:{Authorization:e}})}}))),t)}),[e]);return t?{getWixClientPromise:t}:{getWixClientPromise:i}})(s),b=Mi({plugins:l,instance:s,biSettings:a,experiments:f,wixRicosVersion:r,ricosVersion:r,getWixClientPromise:y,msid:p,viewer:!0,content:_}),v=i.merge(b,qi(l,m,g),((e,t)=>{let n={};return e?.forEach((e=>{const{type:o,config:i}=e;i&&0!==Object.keys(i).length&&(n={...n,[o]:i}),o===h&&t&&(n[o].onViewerAction=t.onViewerAction)})),{config:n}})(l,b.helpers));return o.createElement(Zi,{_rcProps:v,baseUrl:m,content:_,experiments:b.experiments,plugins:l,theme:Hi(c),...u&&{linkPreviewPopoverFetchData:Wi(s)},...E},t)})},27060:(e,t,n)=>{"use strict";var o,i=n(23184),r=function(){function e(e,t){if("function"!=typeof e)throw new TypeError("DataLoader must be constructed with a function which accepts Array<key> and returns Promise<Array<value>>, but got: "+e+".");this._batchLoadFn=e,this._maxBatchSize=function(e){var t=!e||!1!==e.batch;if(!t)return 1;var n=e&&e.maxBatchSize;if(void 0===n)return 1/0;if("number"!=typeof n||n<1)throw new TypeError("maxBatchSize must be a positive number: "+n);return n}(t),this._batchScheduleFn=function(e){var t=e&&e.batchScheduleFn;if(void 0===t)return a;if("function"!=typeof t)throw new TypeError("batchScheduleFn must be a function: "+t);return t}(t),this._cacheKeyFn=function(e){var t=e&&e.cacheKeyFn;if(void 0===t)return function(e){return e};if("function"!=typeof t)throw new TypeError("cacheKeyFn must be a function: "+t);return t}(t),this._cacheMap=function(e){var t=!e||!1!==e.cache;if(!t)return null;var n=e&&e.cacheMap;if(void 0===n)return new Map;if(null!==n){var o=["get","set","delete","clear"].filter((function(e){return n&&"function"!=typeof n[e]}));if(0!==o.length)throw new TypeError("Custom cacheMap missing methods: "+o.join(", "))}return n}(t),this._batch=null,this.name=function(e){if(e&&e.name)return e.name;return null}(t)}var t=e.prototype;return t.load=function(e){if(null==e)throw new TypeError("The loader.load() function must be called with a value, but got: "+String(e)+".");var t=function(e){var t=e._batch;if(null!==t&&!t.hasDispatched&&t.keys.length<e._maxBatchSize)return t;var n={hasDispatched:!1,keys:[],callbacks:[]};return e._batch=n,e._batchScheduleFn((function(){!function(e,t){if(t.hasDispatched=!0,0===t.keys.length)return void l(t);var n;try{n=e._batchLoadFn(t.keys)}catch(n){return s(e,t,new TypeError("DataLoader must be constructed with a function which accepts Array<key> and returns Promise<Array<value>>, but the function errored synchronously: "+String(n)+"."))}if(!n||"function"!=typeof n.then)return s(e,t,new TypeError("DataLoader must be constructed with a function which accepts Array<key> and returns Promise<Array<value>>, but the function did not return a Promise: "+String(n)+"."));n.then((function(e){if(!c(e))throw new TypeError("DataLoader must be constructed with a function which accepts Array<key> and returns Promise<Array<value>>, but the function did not return a Promise of an Array: "+String(e)+".");if(e.length!==t.keys.length)throw new TypeError("DataLoader must be constructed with a function which accepts Array<key> and returns Promise<Array<value>>, but the function did not return a Promise of an Array of the same length as the Array of keys.\n\nKeys:\n"+String(t.keys)+"\n\nValues:\n"+String(e));l(t);for(var n=0;n<t.callbacks.length;n++){var o=e[n];o instanceof Error?t.callbacks[n].reject(o):t.callbacks[n].resolve(o)}})).catch((function(n){s(e,t,n)}))}(e,n)})),n}(this),n=this._cacheMap,o=this._cacheKeyFn(e);if(n){var i=n.get(o);if(i){var r=t.cacheHits||(t.cacheHits=[]);return new Promise((function(e){r.push((function(){e(i)}))}))}}t.keys.push(e);var a=new Promise((function(e,n){t.callbacks.push({resolve:e,reject:n})}));return n&&n.set(o,a),a},t.loadMany=function(e){if(!c(e))throw new TypeError("The loader.loadMany() function must be called with Array<key> but got: "+e+".");for(var t=[],n=0;n<e.length;n++)t.push(this.load(e[n]).catch((function(e){return e})));return Promise.all(t)},t.clear=function(e){var t=this._cacheMap;if(t){var n=this._cacheKeyFn(e);t.delete(n)}return this},t.clearAll=function(){var e=this._cacheMap;return e&&e.clear(),this},t.prime=function(e,t){var n=this._cacheMap;if(n){var o,i=this._cacheKeyFn(e);if(void 0===n.get(i))t instanceof Error?(o=Promise.reject(t)).catch((function(){})):o=Promise.resolve(t),n.set(i,o)}return this},e}(),a="object"==typeof i&&"function"==typeof i.nextTick?function(e){o||(o=Promise.resolve()),o.then((function(){i.nextTick(e)}))}:"function"==typeof setImmediate?function(e){setImmediate(e)}:function(e){setTimeout(e)};function s(e,t,n){l(t);for(var o=0;o<t.keys.length;o++)e.clear(t.keys[o]),t.callbacks[o].reject(n)}function l(e){if(e.cacheHits)for(var t=0;t<e.cacheHits.length;t++)e.cacheHits[t]()}function c(e){return"object"==typeof e&&null!==e&&"number"==typeof e.length&&(0===e.length||e.length>0&&Object.prototype.hasOwnProperty.call(e,e.length-1))}e.exports=r},88187:(e,t,n)=>{"use strict";n.d(t,{VQ:()=>o,ll:()=>i});const o="linkViewer",i=4}}]);
//# sourceMappingURL=form-app-wix-ricos-viewer.chunk.min.js.map