"use strict";(self.webpackJsonp__wix_thunderbolt_app=self.webpackJsonp__wix_thunderbolt_app||[]).push([[569,5445],{33615:(e,t,a)=>{a.r(t),a.d(t,{ProtectedPagesApiSymbol:()=>S,page:()=>U,site:()=>y});var s,o=a(77748),r=a(20590),n=a(54563);!function(e){e.SM="SM",e.Pass="PASS",e.NONE="NONE"}(s||(s={}));const i=(0,o.Og)([(0,o.KT)(r._K,n.U),(0,o.KT)(r.YG,n.U),(0,o.KT)(r.wk,n.U)],((e,t,a)=>{const o=a=>{const o=e.pagesSecurity[a];return o&&o.requireLogin?s.SM:t.passwordProtected[a]||o&&o.passwordDigest?s.Pass:s.NONE};return{async handle(t){if(t.pageJsonFileName)return t;const r=t.pageId,{pagesMap:n,loginAndNavigate:i,authenticateUsingSitePassword:p,completedSitePasswordAuth:g}=a.get();if(n[r])return{...t,pageJsonFileName:n[r]};if(o("masterPage")===s.Pass&&!g)return p(t,s.Pass),null;const c=o(r);return c===s.NONE&&((e,t)=>{throw new Error(`we do not have authentication info for protected page ${e} page security: ${t}`)})(r,e.pagesSecurity[r]),i(t,c),null}}}));var p=a(71085),g=a(66397),c=a(19889),u=a(91994),d=a(12457),P=a(75396),l=a(32166),w=a(10553);const h=(0,o.Og)([(0,o.KT)(r.wk,n.U),(0,o.KT)(r.YG,n.U),(0,o.KT)(r._K,n.U),p.Ix,P.t7,(0,o.lq)(c.Np),(0,o.lq)(u.De),d.Tf,l.Ht,w.n],((e,{publicPageIds:t,pageUriSeoToRouterPrefix:a},{customNoPermissionsPageUriSeo:o,customNoPermissionsPageId:r},n,i,p,c,u,d,P)=>{const l=async(e,t)=>e===s.SM?(async()=>{let e;if(p)if(P["specs.thunderbolt.newAuthorizedPagesFlow"]){if(e=await p.requestAuthorizedPages(),e.success)return{authorizedPagesMap:e.pages,onProtectedPageNavigationComplete:async()=>{window.document.title=await u.getPageTitle()}}}else if(e=await p.requestAuthentication({}),e.success)return{authorizedPagesMap:await p.authorizeMemberPagesByToken(e.token)};return{authorizedPagesMap:{},authenticationResult:e}})():(async e=>c?c.promptPagePasswordDialog(e):{authorizedPagesMap:{}})(t),w={[s.Pass]:"password-protected",[s.SM]:"protected",[s.NONE]:void 0},h=async(e,t)=>n.navigate(e,{biData:{pageType:w[t]}}),M=async(e,t,s=!1)=>{if(o&&s){const s=`${a[o]?`./${a[o]}/`:"./"}${o}`,r=encodeURIComponent(JSON.stringify({restrictedPageId:e.pageId,restrictedPagePath:(e.relativeEncodedUrl??"").replace("./","/")}));return h(`${s}?appSectionParams=${r}`,t)}if(p&&"SM"===t){const e=()=>{i.isLandingOnProtectedPage()&&h("./",t)};return p.showNoPermissionsToPageDialog(e),!1}return!!i.isLandingOnProtectedPage()&&h("./",t)};async function m(t,a){if(!c)return;const{authorizedPagesMap:s,onComplete:o}=await c.promptSitePasswordDialog();e.update((e=>({...e,pagesMap:Object.assign(e.pagesMap,s),completedSitePasswordAuth:!0}))),await h(t.parsedUrl.href,a),o?.()}const f=async(a,s)=>{const o=a.pageId;try{const{authorizedPagesMap:n,authenticationResult:p,onProtectedPageNavigationComplete:c}=await l(s,o);if(e.update((e=>({...e,pagesMap:Object.assign(e.pagesMap,n)}))),p?.reason===g.qd.CANCELED)return!!i.isLandingOnProtectedPage()&&h("./",s);if(n[o]){const e=await h(a.parsedUrl.href,s);return c?.(),e}{const o=t.includes(r),n=Boolean(e.get().pagesMap[r]);return M(a,s,o||n)}}catch(e){return d.captureError(e,{tags:{feature:"protectedPage"}}),M(a,s)}};return e.update((()=>({loginAndNavigate:f,authenticateUsingSitePassword:m,completedSitePasswordAuth:!1,pagesMap:{}}))),{appWillMount:async()=>{}}}));var M=a(16537),m=a(60950);const f=(0,o.Og)([(0,o.KT)(r.wk,n.U),c.Np],((e,t)=>({getTpaHandlers:()=>({authorizeMemberPages:async()=>{const a=await t.authorizeMemberPagesByCookie();e.update((e=>({...e,pagesMap:{...e.pagesMap,...a}})))}})})));const N=(0,o.Og)([(0,o.KT)(r.wk,n.U)],(function(e){return{getPageJsonFileName:t=>e.get()?.pagesMap[t]??null,getProtectedPages:()=>({...e.get()?.pagesMap??{}})}})),S=Symbol("ProtectedPagesApiSymbol"),y=e=>{e(p.po.Protected).to(i),e(M.$.AppWillMountHandler).to(h),e(S).to(N)},U=e=>{e(m.dQ).to(f)}}}]);
//# sourceMappingURL=https://static.parastorage.com/services/wix-thunderbolt/dist/assetsLoader.1e70f802.chunk.min.js.map