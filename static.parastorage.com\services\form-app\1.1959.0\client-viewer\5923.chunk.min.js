(("undefined"!=typeof self?self:this).webpackJsonp__wix_form_app=("undefined"!=typeof self?self:this).webpackJsonp__wix_form_app||[]).push([[5923],{17678:(e,t,o)=>{"use strict";o.r(t),o.d(t,{classes:()=>n,cssStates:()=>a,keyframes:()=>i,layers:()=>r,namespace:()=>s,st:()=>c,stVars:()=>l,style:()=>p,vars:()=>d});var s="orBkdIS",n={root:"ser9Ttm",dropdownContent:"sQcjGOE"},i={},r={},l={},d={},a=o.stc.bind(null,s),p=o.sts.bind(null,s),c=p},1128:(e,t,o)=>{"use strict";o.r(t),o.d(t,{classes:()=>n,cssStates:()=>a,keyframes:()=>i,layers:()=>r,namespace:()=>s,st:()=>c,stVars:()=>l,style:()=>p,vars:()=>d});var s="oNI8Fl0",n={root:"secP3Xb",optionsContainer:"s_wPDea",dropdownOption:"s__10YMfw",noResultsText:"smLlFs8"},i={},r={},l={},d={},a=o.stc.bind(null,s),p=o.sts.bind(null,s),c=p},72400:(e,t,o)=>{"use strict";o.r(t),o.d(t,{classes:()=>n,cssStates:()=>a,keyframes:()=>i,layers:()=>r,namespace:()=>s,st:()=>c,stVars:()=>l,style:()=>p,vars:()=>d});var s="oNlCzso",n={root:"sgq1Yed",highlight:"sjHKRbA",title:"sjPTX_N"},i={},r={},l={hoverBackgroundColor:"lightgrey",selectedBackgroundColor:"grey",selectedHoverBackgroundColor:"dimgrey",disabledBackgroundColor:"white"},d={"wix-forms-formInputValueColor":"--wix-forms-formInputValueColor","wix-forms-formInputValueFont":"--wix-forms-formInputValueFont","wix-color-5":"--CoreDropdownOption1142800117-wix-color-5"},a=o.stc.bind(null,s),p=o.sts.bind(null,s),c=p},25896:(e,t,o)=>{"use strict";o.r(t),o.d(t,{classes:()=>n,cssStates:()=>a,keyframes:()=>i,layers:()=>r,namespace:()=>s,st:()=>c,stVars:()=>l,style:()=>p,vars:()=>d});var s="oH3f72p",n={root:"sDJ8dAx",popoverAnimation:"sBE9I0n","popoverAnimation-enter":"s__3Dka1A","popoverAnimation-enter-active":"stnuIdB","popoverAnimation-exit":"sJMXY_3","popoverAnimation-exit-active":"srjwQqq",popoverContent:"s_proZI",arrow:"scqSYgR",popover:"s__2L1PDX",withArrow:"sn9Osut",popoverElement:"slgHxg_"},i={},r={},l={contentBackgroundColor:"white",contentBorderWidth:"1px",contentBorderStyle:"solid",contentBorderColor:"black",contentBorderRadius:"initial",contentPadding:"initial",contentArrowSize:"5px"},d={},a=o.stc.bind(null,s),p=o.sts.bind(null,s),c=p},85063:(e,t,o)=>{"use strict";o.d(t,{B:()=>r});var s=o(60751),n=o.n(s),i=o(88929);const r="function"==typeof n().useId?n().useId:()=>(0,s.useRef)((0,i.l)()).current},11642:(e,t,o)=>{"use strict";o.d(t,{v:()=>P,_:()=>B});var s=o(77940),n=o(36111),i=o(60751),r=o(17678),l=o(40884),d=o(22784),a=o(22770),p=o(9728),c=o(84190),h=o(25896),u=o(55530),m=o.n(u),v=o(56340),f=o.n(v);class C extends i.PureComponent{constructor(e){super(e),(0,n.A)(this,"_boundEvents",void 0),(0,n.A)(this,"_isInsideClick",(e=>{const{rootRef:t,excludeClass:o}=this.props;let s=e.target;for(;s;){if(t.current===s)return!0;if(s.classList){if("string"==typeof o&&s.classList.contains(o))return!0;if("object"==typeof o&&s.classList.toString().split(" ").some((e=>o.includes(e))))return!0}s=s.parentElement}})),(0,n.A)(this,"_onClickOutside",(e=>{const{onClickOutside:t}=this.props;"function"!=typeof t||this._isInsideClick(e)||t(e)})),this._boundEvents=[]}_registerEvents(){const{options:e}=this.props;["mouseup","touchend"].forEach((t=>{document.addEventListener(t,this._onClickOutside,e),this._boundEvents.push(t)}))}_unregisterEvents(){const{options:e}=this.props;for(;this._boundEvents.length>0;){const t=this._boundEvents.pop();document.removeEventListener(t,this._onClickOutside,e)}}componentDidMount(){this.props.onClickOutside&&this._registerEvents()}componentDidUpdate(e){this.props.onClickOutside!==e.onClickOutside&&(this.props.onClickOutside?this._registerEvents():this._unregisterEvents())}componentWillUnmount(){this._unregisterEvents()}render(){return this.props.children}}const w=i.createContext({excludeClickOutsideClasses:[]}),y=e=>{let{moveBy:t,placement:o=""}=e;return o.includes("right")||o.includes("left")?[t?t.y:0,t?t.x:0]:[t?t.x:0,t?t.y:0]},O=e=>{let{width:t,minWidth:o,dynamicWidth:s,referenceWidth:n}=e;return{minWidth:s?`${n}px`:(i=o,"string"==typeof i?i:`${i}px`),width:t||"auto"};var i};var b=o(68266);function x(e){return"HTML"===e.nodeName?e:e.parentNode||e.host}function k(e){if(!e)return document.body;switch(e.nodeName){case"HTML":case"BODY":return e.ownerDocument.body;case"#document":return e.body}const{overflow:t,overflowX:o,overflowY:s}=function(e){return 1!==e.nodeType?[]:e.ownerDocument.defaultView.getComputedStyle(e,null)}(e);return/(auto|scroll|overlay)/.test(t+s+o)?e:k(x(e))}var E=o(82664),g=o.n(E);function A(e,t){if(!e)return null;if("string"==typeof e)switch(e){case"window":case"viewport":return document.body;case"scrollParent":return k(t);default:return}return"function"==typeof e?function(e,t){if(!t)return;const o=_(e,t);if(o)return o;return}(e,t):g()(e)?e:void 0}function _(e,t){if(t)return e(t)?t:_(e,x(t))}const I=e=>{const t=e=>{let{children:t}=e;return"string"==typeof t?i.createElement("div",{},t):t};return t.displayName=e,t};let T;const D=!1;const N=(e,t)=>e&&e.classList.remove(...t.split(" ")),S=e=>{let{timeout:t}=e;if("object"==typeof t){const{enter:e,exit:o}=t;return void 0!==e&&void 0!==o&&(e>0||o>0)}return!!t};class M extends i.Component{constructor(e){super(e),(0,n.A)(this,"targetRef",null),(0,n.A)(this,"portalNode",null),(0,n.A)(this,"portalClasses",void 0),(0,n.A)(this,"appendToNode",null),(0,n.A)(this,"clickOutsideRef",null),(0,n.A)(this,"popoverContentRef",void 0),(0,n.A)(this,"clickOutsideClass",void 0),(0,n.A)(this,"contentHook",void 0),(0,n.A)(this,"popperScheduleUpdate",null),(0,n.A)(this,"_hideTimeout",null),(0,n.A)(this,"_showTimeout",null),(0,n.A)(this,"_handleClickOutside",(e=>{const{onClickOutside:t,shown:o,disableClickOutsideWhenClosed:s}=this.props;!t||s&&!o||t(e)})),(0,n.A)(this,"_onKeyDown",(e=>{const{onEscPress:t}=this.props;t&&"Escape"===e.key&&t(e)})),(0,n.A)(this,"_onDocumentKeyUp",(e=>{const{onTabOut:t}=this.props;"undefined"!=typeof document&&this.popoverContentRef.current&&!this.popoverContentRef.current.contains(document.activeElement)&&t(e)})),this.state={isMounted:!1,shown:e.shown||!1},this.clickOutsideRef=i.createRef(),this.popoverContentRef=i.createRef(),this.clickOutsideClass=f()("clickOutside"),this.contentHook=`popover-content-${e["data-hook"]||""}-${T}`}focus(){this.popoverContentRef.current&&this.popoverContentRef.current.focus()}getPopperContentStructure(e){const{shown:t}=this.state,{moveBy:o,appendTo:s,placement:n,showArrow:r,moveArrowTo:d,flip:a,fixed:p,customArrow:c,role:u,id:v,zIndex:f,minWidth:C,maxWidth:b,width:x,dynamicWidth:k,excludeClass:E=this.clickOutsideClass,contentClassName:g,onEscPress:A,tabIndex:_,eventsEnabled:I=!0,"aria-label":T,"aria-labelledby":N,"aria-describedby":M}=this.props,R=(e=>{let{width:t,moveBy:o,appendTo:s,shouldAnimate:n,flip:i,fixed:r,placement:l,isTestEnv:d,minWidth:a,dynamicWidth:p}=e;const c=!r,h=[{name:"offset",options:{offset:y({moveBy:o,placement:l})}},{name:"computeStyles",enabled:!d,options:{gpuAcceleration:!n}},{name:"flip",enabled:void 0!==i?i:!o},{name:"preventOverflow",enabled:c,options:{boundariesElement:s||void 0}},{name:"hide",enabled:c}];return(p||a||t)&&h.push({name:"setPopperWidth",enabled:!0,order:840,fn:e=>{const{width:o}=e.offsets.reference;return e.styles={...e.styles,...O({width:t,referenceWidth:o,minWidth:a,dynamicWidth:p})},e}}),h})({minWidth:C,width:x,dynamicWidth:k,moveBy:o,appendTo:s,shouldAnimate:S(this.props),flip:a,placement:n,fixed:p,isTestEnv:D}),H=i.createElement(l.Ay,{modifiers:R,placement:n,eventsEnabled:I},(o=>{let{ref:s,style:l,placement:a,arrowProps:p,scheduleUpdate:C,disableEventListeners:y}=o;return this.popperScheduleUpdate=I?C:void 0,i.createElement(w.Consumer,null,(o=>{let{excludeClickOutsideClasses:C}=o;return i.createElement("div",{ref:s,"data-hook":"popover-content","data-content-element":this.contentHook,style:{...l,zIndex:f,maxWidth:b},"data-placement":a||n,className:m()(h.classes.popover,this.clickOutsideClass,g,{[h.classes.withArrow]:r,[h.classes.popoverContent]:!r},...C)},r&&this.renderArrow(p,d,a||n,c),i.createElement("div",{key:"popover-content",id:v,role:u,tabIndex:_,ref:this.popoverContentRef,className:r?h.classes.popoverContent:"",onKeyDown:t&&A?this._onKeyDown:void 0,"aria-label":T,"aria-labelledby":N,"aria-describedby":M},i.createElement(w.Provider,{value:{excludeClickOutsideClasses:[E,...C]}},e.Content)))}))}));return this.wrapWithAnimations(H)}applyStylesToPortaledNode(){const{shown:e}=this.state;S(this.props)||e?((e,t)=>{e&&e.classList.add(...t.split(" "))})(this.portalNode,this.portalClasses):N(this.portalNode,this.portalClasses)}wrapWithAnimations(e){const{timeout:t}=this.props,{shown:o}=this.state;return S(this.props)?i.createElement(p.A,{in:o,timeout:t,unmountOnExit:!0,classNames:{enter:h.classes["popoverAnimation-enter"],enterActive:h.classes["popoverAnimation-enter-active"],exit:h.classes["popoverAnimation-exit"],exitActive:h.classes["popoverAnimation-exit-active"]},onExited:()=>N(this.portalNode,this.portalClasses)},e):e}renderPopperContent(e){const t=this.getPopperContentStructure(e);return this.portalNode?i.createElement(c.A,{node:this.portalNode},t):t}renderArrow(e,t,o,n){const r={ref:e.ref,key:"popover-arrow","data-hook":"popover-arrow",style:{...e.style,...(l=t,d=o,l?d.startsWith("top")||d.startsWith("bottom")?{left:`${l}px`}:d.startsWith("left")||d.startsWith("right")?{top:`${l}px`}:{}:{})}};var l,d;return n?n(o,r):i.createElement("div",(0,s.A)({},r,{className:h.classes.arrow}))}componentDidMount(){const{shown:e,onTabOut:t}=this.props;this.initAppendToNode(),t&&e&&this._setBlurByKeyboardListener(),this.setState({isMounted:!0})}_setBlurByKeyboardListener(){"undefined"!=typeof document&&document.addEventListener("keyup",this._onDocumentKeyUp,!0)}_removeBlurListener(){"undefined"!=typeof document&&document.removeEventListener("keyup",this._onDocumentKeyUp,!0)}initAppendToNode(){const{appendTo:e}=this.props;this.appendToNode=A(e,this.targetRef),this.appendToNode&&(this.portalNode=document.createElement("div"),this.portalNode.setAttribute("data-hook","popover-portal"),Object.assign(this.portalNode.style,{position:"static",top:0,left:0,width:0,height:0}),this.appendToNode.appendChild(this.portalNode))}hidePopover(){const{isMounted:e}=this.state,{hideDelay:t,onTabOut:o}=this.props;e&&!this._hideTimeout&&(this._showTimeout&&(clearTimeout(this._showTimeout),this._showTimeout=null),o&&this._removeBlurListener(),t?this._hideTimeout=setTimeout((()=>{this.setState({shown:!1})}),t):this.setState({shown:!1}))}showPopover(){const{isMounted:e}=this.state,{showDelay:t,onTabOut:o}=this.props;e&&!this._showTimeout&&(this._hideTimeout&&(clearTimeout(this._hideTimeout),this._hideTimeout=null),o&&this._setBlurByKeyboardListener(),t?this._showTimeout=setTimeout((()=>{this.setState({shown:!0})}),t):this.setState({shown:!0}))}componentWillUnmount(){this.portalNode&&this.appendToNode.children.length&&this.appendToNode.removeChild(this.portalNode),this.portalNode=null,this._hideTimeout&&(clearTimeout(this._hideTimeout),this._hideTimeout=null),this._showTimeout&&(clearTimeout(this._showTimeout),this._showTimeout=null)}updatePosition(){this.popperScheduleUpdate&&this.popperScheduleUpdate()}componentDidUpdate(e){const{shown:t}=this.props;this.portalNode&&(this.portalClasses=(0,h.st)(h.classes.root,this.props.className),this.applyStylesToPortaledNode()),e.shown!==t?t?this.showPopover():this.hidePopover():this.updatePosition()}render(){const{onMouseEnter:e,onMouseLeave:t,onKeyDown:o,onClick:n,children:r,style:l,id:p,excludeClass:c,fluid:u}=this.props,{isMounted:m,shown:v}=this.state,f=((e,t)=>i.Children.toArray(e).reduce(((e,t)=>i.isValidElement(t)&&t.type&&t.type.displayName?(e[t.type.displayName.split(".").pop()]=t,e):e),t||{}))(r,{Element:null,Content:null}),w=S(this.props),y=m&&(w||v);return i.createElement(d.Ay,null,i.createElement(C,{rootRef:this.clickOutsideRef,onClickOutside:v?this._handleClickOutside:void 0,excludeClass:[this.clickOutsideClass,c]},i.createElement("div",(0,s.A)({ref:this.clickOutsideRef,style:l,"data-content-hook":this.contentHook,className:(0,h.st)(h.classes.root,{fluid:u},this.props.className),onMouseEnter:e,onMouseLeave:t,id:p},(0,b.r)(this.props)),i.createElement(a.A,{innerRef:e=>this.targetRef=e},(e=>{let{ref:t}=e;return i.createElement("div",{ref:t,className:h.classes.popoverElement,"data-hook":"popover-element",onClick:n,onKeyDown:o},f.Element)})),y&&this.renderPopperContent(f))))}}(0,n.A)(M,"displayName","CorePopover"),(0,n.A)(M,"defaultProps",{flip:!0,fixed:!1,zIndex:1e3}),(0,n.A)(M,"Element",I("Popover.Element")),(0,n.A)(M,"Content",I("Popover.Content"));var R=o(50332);const H="hover";class B extends i.PureComponent{constructor(e){super(e),(0,n.A)(this,"dropdownContentRef",null),(0,n.A)(this,"state",{isOpen:!1,selectedIds:[]}),this.close=this.close.bind(this),this.handleClickOutside=this.handleClickOutside.bind(this),this.onPopoverClick=this.onPopoverClick.bind(this),this.onKeyDown=this.onKeyDown.bind(this),this.onOptionHover=this.onOptionHover.bind(this),this.onOptionClick=this.onOptionClick.bind(this),this._onContentMouseDown=this._onContentMouseDown.bind(this)}componentDidMount(){this.initializeSelectedOptions()}componentDidUpdate(e){B.areSelectedIdsEqual(this.props.initialSelectedIds,e.initialSelectedIds)||this.initializeSelectedOptions()}initializeSelectedOptions(){const{initialSelectedIds:e,options:t,onInitialSelectedOptionsSet:o}=this.props,s=(e||[]).map((e=>t.find((t=>e===t.id)))).filter((e=>e&&!e.isDisabled&&e.isSelectable)),n=s.map((e=>e&&e.id));this.setState({selectedIds:n}),o&&o(s)}handleClickOutside(){this.close()}_onExpandedChange(){this.props.onExpandedChange&&this.props.onExpandedChange(this.state.isOpen)}open(){this.setState({isOpen:!0},(()=>this._onExpandedChange()))}onPopoverClick(){this.state.isOpen?this.close():this.open()}close(){this.state.isOpen&&this.setState({isOpen:!1},this._onExpandedChange)}getSelectedOption(){return this.dropdownContentRef?this.dropdownContentRef.getSelectedOption():null}onKeyboardSelect(){const e=this.getSelectedOption();this.onOptionClick(e)}isClosingKey(e){const{dropdownA11yFixes:t}=this.props;return"Tab"===e||"Escape"===e||!t&&"Enter"===e}onKeyDown(e){const t=e.key,{dropdownA11yFixes:o}=this.props;if(this.state.isOpen){const s=()=>{this.onKeyboardSelect(),!this.props.multi&&this.close(),null!==this.getSelectedOption()&&e.preventDefault()};switch(t){case"Tab":case"Enter":s(),o&&e.preventDefault();break;case"Escape":this.close()}}else{if(this.isClosingKey(t))return;if(o)if("Enter"===t||" "===t||"ArrowDown"===t||"ArrowUp"===t)e.preventDefault();else{if(!/^.$/u.test(e.key))return;this.open()}if("Unidentified"===t)return;this.open()}this.dropdownContentRef&&this.dropdownContentRef.onKeyDown(t,e)}_onContentMouseDown(e){const{onContentMouseDown:t}=this.props;t&&t(e)}onOptionHover(e){this.props.onOptionHover&&this.props.onOptionHover(e)}onOptionClick(e){const{onSelect:t,onDeselect:o,multi:s,allowReselect:n}=this.props,{selectedIds:i}=this.state,r={isOpen:!!s,selectedIds:i};let l=t;if(s)e&&(i.includes(e.id)?(r.selectedIds=i.filter((t=>t!==e.id)),l=o):r.selectedIds=[...i,e.id]);else if(e)if(i.includes(e.id)){if(this.close(),!n)return}else r.selectedIds=[e.id];else r.selectedIds=[];this.setState(r,(()=>l(e)))}render(){const{openTrigger:e,placement:t,options:o,children:n,showArrow:l,fixedFooter:d,fixedHeader:a,disabled:p,readOnly:c,timeout:h,forceContentElementVisibility:u,style:m,id:v,flip:f,fixed:C,moveBy:w,role:y,contentId:O,optionsContainerZIndex:x,dynamicWidth:k,appendTo:E,className:g,contentClassName:A,focusOnOption:_,contentRole:I,eventsEnabled:T,noResultsText:D}=this.props,{isOpen:N,selectedIds:S}=this.state,B=Boolean(o&&o.length||a||d),P=u||N&&!p&&!c&&B;return i.createElement(M,(0,s.A)({className:(0,r.st)(r.classes.root,{"content-visible":P},g),contentClassName:A,placement:t,shown:P,showArrow:l,timeout:h,onClickOutside:this.handleClickOutside,onClick:p||c||"click"!==e?void 0:()=>this.onPopoverClick(),onMouseEnter:p||c||e!==H?void 0:()=>this.open(),onKeyDown:p||c?void 0:this.onKeyDown,onMouseLeave:p||c||e!==H?void 0:this.close,style:m,id:v,flip:f,fixed:C,moveBy:w,role:y,zIndex:x,dynamicWidth:k,appendTo:E,eventsEnabled:T},(0,b.r)(this.props)),i.createElement(M.Element,null,n),i.createElement(M.Content,null,i.createElement(R.W,{className:r.classes.dropdownContent,role:I,"data-hook":"dropdown-content",ref:e=>this.dropdownContentRef=e,options:o,fixedFooter:d,fixedHeader:a,selectedIds:S,onOptionClick:this.onOptionClick,onOptionHover:this.onOptionHover,onMouseDown:this._onContentMouseDown,optionsContainerId:O,focusOnOption:_,noResultsText:D})))}}(0,n.A)(B,"displayName","CoreDropdown"),(0,n.A)(B,"areSelectedIdsEqual",((e,t)=>void 0===e&&void 0===t||null===e&&null===t||Array.isArray(e)&&Array.isArray(t)&&e.length===t.length&&e.every(((e,o)=>e===t[o]))));const P=B},50332:(e,t,o)=>{"use strict";o.d(t,{W:()=>p});var s=o(36111),n=o(60751),i=o(1128),r=o(77940),l=o(72400);const d=e=>{const{id:t,option:o,isSelected:s,isHovered:i,role:d,onClickHandler:a,onMouseEnterHandler:p,onMouseLeaveHandler:c,dataHook:h}=e,u=o.isDisabled,m=o.isSelectable,v=!u&&i,f=!u&&s,C=m?{role:d,"aria-selected":f,"aria-disabled":u}:{};return n.createElement("div",(0,r.A)({id:t,tabIndex:-1,className:(0,l.st)(l.classes.root,{disabled:u,selectable:m,hovered:v,selected:f},e.className),"data-hook":h},C,{onClick:a,title:o.value,onMouseEnter:p,onMouseLeave:c}),o.render(o.value))};d.displayName="CoreDropdownOption";var a=o(63355);class p extends n.PureComponent{constructor(e){super(e),(0,s.A)(this,"optionsContainerRef",null),(0,s.A)(this,"mouseCoords",{screenX:-1,screenY:-1}),(0,s.A)(this,"state",{hoveredIndex:-1}),this.onMouseMove=this.onMouseMove.bind(this),this.onMouseDown=this.onMouseDown.bind(this),this.onOptionHover=this.onOptionHover.bind(this)}componentDidMount(){const{options:e,selectedIds:t,focusOnOption:o}=this.props;if(t.length){const o=e.findIndex((e=>e.id===t[0]));if(o>-1){const e=this.optionsContainerRef.childNodes[o],t=this.optionsContainerRef.getBoundingClientRect();return e.getBoundingClientRect().bottom>t.bottom&&(this.optionsContainerRef.scrollTop=Math.min(e.offsetTop+e.clientHeight/2-this.optionsContainerRef.offsetHeight/2,this.optionsContainerRef.scrollHeight)),void this.setHoveredIndex(o)}}o&&this.hoverItemById(o)}componentDidUpdate(e){const{focusOnOption:t}=this.props;t!==e.focusOnOption&&this.hoverItemById(t)}setHoveredIndex(e){this.setState({hoveredIndex:e},this.onOptionHover)}onOptionHover(){this.props.onOptionHover&&this.props.onOptionHover(this.getSelectedOption())}isValidOptionForSelection(e){return e.isSelectable&&!e.isDisabled}hoverNextItem(e){const{options:t}=this.props;if(!t.find(this.isValidOptionForSelection))return;let{hoveredIndex:o}=this.state;for(;o+=e,o===t.length?o=0:o<0&&(o=t.length-1),!this.isValidOptionForSelection(t[o]););this.hoverItemAtIndex(o)}hoverItemById(e){const{options:t}=this.props,o=t.findIndex((t=>t.id===e));-1!==o&&this.hoverItemAtIndex(o)}hoverItemAtIndex(e){const{options:t}=this.props;if(t.find(this.isValidOptionForSelection)){if(this.optionsContainerRef){const t=this.optionsContainerRef.childNodes[e],o=t.offsetHeight,s=t.offsetTop-1,{scrollTop:n,clientHeight:i}=this.optionsContainerRef;n<=s&&n+i>s+o||(this.optionsContainerRef.scrollTop<s?this.optionsContainerRef.scrollTop=o+s-i:this.optionsContainerRef.scrollTop=s)}this.setHoveredIndex(e)}}getOptionDOMid(e){const t=this.props.optionsContainerId;return t?`${t}_option-${e.id}`:null}getSelectedOption(){const{options:e}=this.props,{hoveredIndex:t}=this.state;return t>=0&&t<e.length?{...e[t],_DOMid:this.getOptionDOMid(e[t])}:null}onKeyDown(e,t){switch(e){case"ArrowUp":return t.preventDefault(),this.hoverNextItem(-1);case"ArrowDown":return t.preventDefault(),this.hoverNextItem(1);case"ArrowLeft":case"ArrowRight":return}}onMouseMove(e){this.mouseCoords.screenX=e.screenX,this.mouseCoords.screenY=e.screenY}onMouseDown(e){const{onMouseDown:t}=this.props;t&&t(e)}onMouseEnter(e,t){this.mouseCoords.screenX===e.screenX&&this.mouseCoords.screenY===e.screenY||this.setHoveredIndex(t)}render(){const{fixedHeader:e,fixedFooter:t,options:o,selectedIds:s,onOptionClick:r,optionsContainerId:l,className:p,role:c="menu",noResultsText:h}=this.props,{hoveredIndex:u}=this.state;return n.createElement("div",{className:(0,i.st)(i.classes.root,p),"data-hook":this.props["data-hook"],onMouseMove:this.onMouseMove,onMouseDown:this.onMouseDown},e,n.createElement("div",{role:c,className:i.classes.optionsContainer,id:l,ref:e=>this.optionsContainerRef=e},!(null!=o&&o.length)&&!!h&&n.createElement(a.E,{role:"alert",priority:"secondary",className:i.classes.noResultsText},h),(o||[]).map(((e,t)=>n.createElement(d,{className:i.classes.dropdownOption,dataHook:"option",key:e.id,id:this.getOptionDOMid(e),option:e,isHovered:u===t,isSelected:(s||[]).includes(e.id),role:"listbox"===c?"option":"menuitem",onClickHandler:this.isValidOptionForSelection(e)?()=>r(e):void 0,onMouseEnterHandler:this.isValidOptionForSelection(e)?e=>this.onMouseEnter(e,t):void 0,onMouseLeaveHandler:()=>{this.setHoveredIndex(-1)}})))),t)}}(0,s.A)(p,"displayName","CoreDropdownContent")},35216:(e,t,o)=>{"use strict";o.d(t,{s:()=>u});var s=o(60751),n=o(72400),i=o(56340),r=o.n(i),l=o(62869),d=o.n(l);const a=function(e){return void 0===e&&(e=null),{id:e&&(e.id||0===e.id)?e.id:r()("Option"),isDisabled:!1,isSelectable:!0,value:null,render:e=>s.createElement("div",{className:n.classes.title,role:"presentation"},e),...e}},p=e=>e%2==0,c=e=>e%2==1,h=(e,t)=>{const o=(e=>new RegExp(`(${e.replace(/ /g,"|")})`,"gi"))(t.trim().replace(/[-/\\^$*+?.()|[\]{}]/g,"\\$&"));const i=e.value.split(o),r=""===i[0]?p:c,l=d()(i).map(((e,t)=>r(t)?s.createElement("mark",{className:n.classes.highlight,key:t},e):s.createElement("span",{className:n.classes.nonHighlight,key:t},e)));return a({id:e.id,isDisabled:e.isDisabled,isSelectable:e.isSelectable,value:e.value,render:()=>e.render(l)})},u={create:a,createHighlighted:(e,t)=>e.value&&t?h(e,t):e}},68266:(e,t,o)=>{"use strict";o.d(t,{r:()=>n});const s="data-",n=e=>{const t={};for(const o in e)e.hasOwnProperty(o)&&o.length>5&&o.startsWith(s)&&(t[o]=e[o]);return t}},62869:(e,t,o)=>{e.exports=o(16252).compact}}]);
//# sourceMappingURL=5923.chunk.min.js.map