"use strict";(self.webpackJsonp__wix_thunderbolt_app=self.webpackJsonp__wix_thunderbolt_app||[]).push([[7882],{39685:(t,e,n)=>{n.r(e),n.d(e,{AppMonitoringSymbol:()=>a.k,site:()=>h});var s=n(77748),o=n(20590),r=n(32166),a=n(16540),i=n(53518),p=n(54676);const c=({appId:t,metaSiteId:e,siteUrl:n})=>({appId:t,platform:"SITE_VIEWER",tenantType:"SITE",tenantId:e,siteUrl:n}),l=(0,s.Og)([(0,s.KT)(o.YG,a.U),r.RV],((t,e)=>{const n=e=>t.appsWithMonitoring.find((t=>t.appId===e));return{featureName:a.U,addRerouteDataToSentryEvent:p.g,getMonitoringClientFunction:({appId:s,metaSiteId:o,siteUrl:r})=>{const a=n(s);return(0,i.w)({monitoringConfig:a?.monitoringComponent?.monitoring,hostContext:c({appId:s,metaSiteId:o,siteUrl:r}),sentrySDK:e.Sentry,sentryTransport:t.sentryTransport})},getMonitoringConfig:n,isSentryEventFromNonWixTpa:p.k}})),h=t=>{t(a.k).to(l)}},53518:(t,e,n)=>{n.d(e,{w:()=>D});var s=t=>null!=t&&""!==t,o=t=>r(t,s);function r(t,e=(t=>!!t)){if(!t)return{};const n={};for(const s in t)e(t[s],s)&&(n[s]=t[s]);return n}var a=class{constructor(t){this.message=t,this.captureException=()=>{console.error(this.message)},this.captureMessage=()=>{console.error(this.message)},this.startSpan=(t,e)=>(console.error(this.message),e(void 0)),this.startSpanManual=()=>(console.error(this.message),{end:()=>{},fail:()=>{}}),this.endSpanManual=()=>{console.error(this.message)},this.addBreadcrumb=()=>{console.error(this.message)}}},i=t=>new a(t),p=t=>"function"==typeof t?.then,c=(t,e)=>e?p(t)?t.then((t=>(e(t,!0),t))):(e(t,!1),t):t,l=()=>Date.now()+Math.random(),h=class{constructor(t){this.options=t,this.spanMap=new Map}createSpan(t,e){let n,s,o;const r=Date.now();let a;const i=c(e,(({sentrySDK:e,scope:i},p)=>{p&&(a=Date.now()-r);const c=e.startSpan({...t,scope:i,forceTransaction:this.options.forceTransaction},(()=>new Promise(((t,e)=>{n=t,s=e}))));c?.catch((t=>{if(t?.message!==o?.message)throw t}))})),p=e=>{this.spanMap.delete(t.name),c(i,(()=>{((t,e)=>{t&&(e?setTimeout(t,e):t())})(e,a)}))},l={end:()=>{p((()=>n?.()))},fail:t=>{o=o??t,p((()=>s?.(t)))}};return this.spanMap.set(t.name,l),l}getSpan(t){return this.spanMap.get(t.name)}},u=(t,e)=>{if(!e)return;const{tags:n,contexts:s}={tags:o({"app.id":(a=e).appId,"app.version":a.appVersion,"app.instanceId":a.appInstanceId,"extension.id":a.extensionId,"extension.name":a.extensionName,"extension.type":a.extensionType,platform:a.platform}),contexts:r({"extension.data":o({...a.extensionData}),site:o({url:a.siteUrl,id:"SITE"===a.tenantType?a.tenantId:void 0}),account:o({id:"ACCOUNT"===a.tenantType?a.tenantId:void 0})},(t=>!!t&&Object.keys(t).length>0))};var a;((t,{tags:e,contexts:n})=>{e&&t.setTags(e),n&&Object.entries(n).forEach((([e,n])=>void 0!==n&&t.setContext(e,n)))})(t,{tags:n,contexts:s})},d=["onLoad","forceLoad"],S=new Map,m=class{constructor(t){this.options=t,this.resolvedState=null,this.validateOptions=t=>{const e=d.filter((e=>"function"!=typeof t.sentrySDK?.[e]));if(e.length>0)throw new Error(`Missing the following options.sentrySDK methods: ${e.join(", ")}`)},this.validateOptions(t);let e=S.get(t.dsn);e||(e=(t=>new h(t))({forceTransaction:!0}),S.set(t.dsn,e)),this.manualSpanRegistry=e}isFullSDK(){return"function"==typeof this.options.sentrySDK.BrowserClient}init(){if(this.resolvedState)return this.resolvedState;const{sentrySDK:t,transport:e,hostContext:n,dsn:s,release:o,environment:r,tracesSampleRate:a=1,debug:i=!1}=this.options,p=new t.Scope,c=new t.BrowserClient({dsn:s,transport:e??t.makeFetchTransport,integrations:t.getDefaultIntegrations({}),stackParser:t.defaultStackParser,tracesSampleRate:a||1,debug:i,environment:r,release:o});return u(p,n),p.setClient(c),c.init(),this.resolvedState={sentrySDK:t,scope:p,sentryClient:c},this.resolvedState}loadFullSDK(t){this.isFullSDK()?t?.(this.init()):this.promise?this.promise.then(t):(this.promise=new Promise((e=>{this.options.sentrySDK.onLoad((()=>{if(this.resolvedState)return;const n=this.init();e(n),t?.(n)}))})),this.options.sentrySDK.forceLoad())}},g=class extends m{constructor(){super(...arguments),this.addBreadcrumb=t=>{const e={type:t.type,category:t.category,message:t.message,level:t.level,data:t.data};this.loadFullSDK((({scope:t})=>{t.addBreadcrumb(e)}))},this.captureException=(t,e)=>{this.loadFullSDK((({sentryClient:n,scope:s})=>{n.captureException(t,{captureContext:{level:e?.level??"error",tags:e?.tags,contexts:e?.contexts}},s)}))},this.captureMessage=(t,e)=>{this.loadFullSDK((({sentryClient:n,scope:s})=>{n.captureMessage(t,e?.level??"error",{captureContext:{level:e?.level??"error",tags:e?.tags,contexts:e?.contexts}},s)}))},this.startSpan=(t,e)=>{if(this.isFullSDK()){const{sentrySDK:n,scope:s}=this.init();return n.startSpan({...t,scope:s},e)}this.loadFullSDK();const n=(e,n)=>{const s=Date.now()-n,o=()=>new Promise((t=>setTimeout((()=>t(e)),s)));this.loadFullSDK((async({sentrySDK:e,scope:n})=>e.startSpan({...t,scope:n},o)))},s=async(e,n)=>{const s=Date.now()-n,o=()=>new Promise(((t,n)=>setTimeout((()=>n(e)),s)));this.loadFullSDK((async({sentrySDK:n,scope:s})=>{try{await n.startSpan({...t,scope:s},o)}catch(t){if(t?.message!==e?.message)throw t}}))},o=Date.now();let r,a;try{r=e()}catch(t){a=t}if(a)throw s(a,o),a;return p(r)?r.then((t=>n(t,o))).catch((t=>s(t,o))):n(r,o),r},this.startSpanManual=t=>{const e=this.isFullSDK()?this.init():new Promise((t=>this.loadFullSDK(t)));return this.manualSpanRegistry.createSpan(t,e)},this.endSpanManual=t=>{const e=this.manualSpanRegistry.getSpan(t);e?.end()}}},y=({sentrySDK:t,dsn:e,...n})=>{if(!t)return i("Unsupported environment - Sentry SDK is not loaded into the environment. Please make sure to use monitoring SDK only in supported environments.");if(!e)return i("Missing Sentry DSN in the app's monitoring configuration. Please make sure to set it.");try{const s=t.SDK_VERSION&&(t=>{try{return parseInt(t.split(".")[0],10)}catch{return void console.warn("Failed to parse Sentry SDK version")}})(t.SDK_VERSION);return s&&s<7?i(`Sentry SDK version "${t.SDK_VERSION}" is not supported. Please make sure to use monitoring SDK only in supported environments.`):new g({...n,dsn:e,sentrySDK:t})}catch(t){return i(`Failed to initialize monitoring client: ${t.message}`)}},f=class{constructor(t){this.options=t,this.captureException=(t,e)=>{const{panoramaClient:n}=this.options;t instanceof Error||(t=new Error(t)),n.errorMonitor().reportError(t,this.contextToData(e))},this.captureMessage=(t,e)=>{const{panoramaClient:n}=this.options,s=n.logger();let o;switch(e?.level){case"error":o=s.error;break;case"warning":o=s.warn;break;default:o=s.info}o(t,this.contextToData(e))},this.startSpan=(t,e)=>{const{panoramaClient:n}=this.options,{name:s}=t;let o;const r=n.transaction(s,{id:l()}),a=this.contextToData(t);r.start(a);try{o=e(),p(o)?o=o.then((t=>(r.finish(a),t))).catch((e=>{throw this.reportErrorInContextOfSpan(e,t),e})):r.finish(a)}catch(e){throw this.reportErrorInContextOfSpan(e,t),e}return o},this.addBreadcrumb=t=>{const{panoramaClient:e}=this.options;e.errorMonitor().addBreadcrumb(t)}}contextToData(t){return t&&{...t?.tags&&{tags:t.tags},...t?.contexts&&{context:t.contexts},...t?.level&&{severity:t.level}}}reportErrorInContextOfSpan(t,e){const{panoramaClient:n}=this.options,{name:s}=e,o=this.contextToData({...e,contexts:{transaction:{name:s}}});n.errorMonitor().reportError(t,o)}startSpanManual(t){const{panoramaClient:e}=this.options,{name:n}=t,s=e.transaction(n,{id:l()}),o=this.contextToData(t);s.start(o);let r=!1;const a=t=>{r||(r=!0,t())};return{end:()=>{a((()=>s.finish(o)))},fail:e=>{a((()=>this.reportErrorInContextOfSpan(e,t)))}}}endSpanManual(t){const{panoramaClient:e}=this.options,{name:n}=t;e.transaction(n).finish()}},D=({sentrySDK:t,sentryTransport:e,panoramaClient:n,monitoringConfig:s,hostContext:o})=>{if((t=>"SENTRY"===t?.type)(s)){const{sentryOptions:n}=s;return()=>y({dsn:n?.dsn,hostContext:o,sentrySDK:t,transport:e})}return(t=>"PANORAMA"===t?.type)(s)?()=>(t=>{const{panoramaClient:e}=t;return e?new f(t):i("Missing Panorama client")})({panoramaClient:n}):()=>i("Invalid monitoring configuration. Please check the monitoring setup of your application.")}}}]);
//# sourceMappingURL=https://static.parastorage.com/services/wix-thunderbolt/dist/appMonitoring.39e75b67.chunk.min.js.map