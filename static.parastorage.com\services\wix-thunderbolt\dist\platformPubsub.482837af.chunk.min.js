"use strict";(self.webpackJsonp__wix_thunderbolt_app=self.webpackJsonp__wix_thunderbolt_app||[]).push([[1171],{64537:(e,t,s)=>{s.d(t,{Q0:()=>l,iZ:()=>c,ow:()=>o});var n=s(77748),r=s(32166),i=s(39218);const a={},c="TPA_PUB_SUB_";function o(e){const t=new RegExp(`^${c}`);return e.replace(t,"")}const l=(0,n.Og)([r.RV,i.DR],((e,t)=>{function s(e,t){const s={persistentData:[],listeners:{}};return a[e]?a[e][t]||(a[e][t]=s):a[e]={[t]:s},a[e][t]}function n(e){Object.keys(a).forEach((t=>{Object.keys(a[t]).forEach((n=>{const r=s(t,n);Object.entries(r.listeners).forEach((([t,s])=>{Object.keys(s).forEach((s=>{e(s,t)&&delete r.listeners[t][s]}))}))}))}))}return{publish(e,t,n){const r=o(n.eventKey),i=n.isPersistent,a=s(e,r),c=a.listeners;if(Object.entries(c).forEach((([e,s])=>Object.keys(s).forEach((s=>{c[e][s].forEach((e=>e({data:n.eventData,name:r,origin:t})))})))),i){const e=n.eventData;a.persistentData.push({name:r,data:e})}},subscribe(e,n,r,i){const a=o(r.eventKey),c=s(e,a);c.listeners[t]||(c.listeners[t]={}),c.listeners[t][n]?"worker"===n&&c.listeners[t][n].push(i):c.listeners[t][n]=[i],r.isPersistent&&c.persistentData.length&&i({data:c.persistentData[0].data,name:a,origin:n},!0)},unsubscribe(e,t,n){const r=s(e,o(n));Object.keys(r.listeners).forEach((e=>{delete r.listeners[e][t]}))},handleIframeSubscribe(t,s,{eventKey:n,isPersistent:r,callId:i}){this.subscribe(t,s,{eventKey:n,isPersistent:r},((t,r=!1)=>{!function(e,t,s){const n=s.document.getElementById(e);if(!n)return;const r=n.getElementsByTagName("iframe")[0];r&&r.contentWindow&&r.contentWindow.postMessage(JSON.stringify(t),"*")}(s,r?{intent:"TPA_RESPONSE",callId:i,type:"registerEventListener",res:{drain:!0,data:[t]},status:!0,compId:s}:{eventType:n,intent:"addEventListener",params:t},e)}))},clearListenersBesideStubs(){n(((e,s)=>!e.includes("tpapopup")&&!e.includes("tpaWorker")&&s===t&&"masterPage"!==s))},clearListenersByCompId(e){n((t=>t===e))}}}))}}]);
//# sourceMappingURL=https://static.parastorage.com/services/wix-thunderbolt/dist/platformPubsub.482837af.chunk.min.js.map