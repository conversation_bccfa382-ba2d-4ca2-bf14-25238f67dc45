"use strict";(("undefined"!=typeof self?self:this).webpackJsonp__wix_form_app=("undefined"!=typeof self?self:this).webpackJsonp__wix_form_app||[]).push([[7017],{18675:(e,r,t)=>{t.r(r),t.d(r,{classes:()=>o,cssStates:()=>c,keyframes:()=>i,layers:()=>s,namespace:()=>a,st:()=>u,stVars:()=>l,style:()=>d,vars:()=>n});var a="oLSesXh",o={root:"sKc61_s",focusRing:"s__2pZzQY",textAreaWrapper:"sgm_SfQ",textArea:"sPmnXEf",errorIconWrapper:"s__6BxKaD",iconTooltip:"s__54Afa3",errorIcon:"sJYzWud",label:"snJBLHz",charCount:"sfA7Ch0",focused:"smHFIGt"},i={},s={},l={},n={"wix-color-1":"--wix-color-1","wix-color-5":"--wix-color-5","wix-color-4":"--wix-color-4","wix-color-29":"--wix-color-29","wut-error-color":"--wut-error-color","wix-ui-tpa-text-area-main-border-radius":"--wix-ui-tpa-text-area-main-border-radius","wix-ui-tpa-text-area-main-border-width":"--wix-ui-tpa-text-area-main-border-width","wix-ui-tpa-text-area-container-width":"--wix-ui-tpa-text-area-container-width","wix-ui-tpa-text-area-container-height":"--wix-ui-tpa-text-area-container-height","wix-ui-tpa-text-area-border-color":"--wix-ui-tpa-text-area-border-color","wix-ui-tpa-text-area-background-color":"--wix-ui-tpa-text-area-background-color","wix-ui-tpa-text-area-text-color":"--wix-ui-tpa-text-area-text-color","wix-ui-tpa-text-area-disabled-text-color":"--wix-ui-tpa-text-area-disabled-text-color","wix-ui-tpa-text-area-placeholder-color":"--wix-ui-tpa-text-area-placeholder-color","wix-ui-tpa-text-area-text-font":"--wix-ui-tpa-text-area-text-font","wix-ui-tpa-text-area-error-border-color":"--wix-ui-tpa-text-area-error-border-color","wix-ui-tpa-text-area-success-border-color":"--wix-ui-tpa-text-area-success-border-color","wix-ui-tpa-text-area-disabled-border-color":"--wix-ui-tpa-text-area-disabled-border-color","wix-ui-tpa-text-area-main-label-font":"--wix-ui-tpa-text-area-main-label-font","wix-ui-tpa-text-area-main-label-text-color":"--wix-ui-tpa-text-area-main-label-text-color","wix-ui-tpa-text-area-disabled-label-color":"--wix-ui-tpa-text-area-disabled-label-color","wix-ui-tpa-text-area-char-count-font":"--wix-ui-tpa-text-area-char-count-font","wix-ui-tpa-text-area-char-count-color":"--wix-ui-tpa-text-area-char-count-color","wix-ui-tpa-text-area-error-color":"--wix-ui-tpa-text-area-error-color","wix-ui-tpa-text-area-error-message-min-height":"--wix-ui-tpa-text-area-error-message-min-height","wix-ui-tpa-text-area-main-border-opacity":"--wix-ui-tpa-text-area-main-border-opacity","wix-ui-tpa-text-area-hover-border-color":"--wix-ui-tpa-text-area-hover-border-color","wix-ui-tpa-text-area-hover-border-width":"--wix-ui-tpa-text-area-hover-border-width","wix-ui-tpa-text-area-hover-border-radius":"--wix-ui-tpa-text-area-hover-border-radius","wix-ui-tpa-text-area-hover-background-color":"--wix-ui-tpa-text-area-hover-background-color","wix-ui-tpa-text-area-hover-background-opacity":"--wix-ui-tpa-text-area-hover-background-opacity","wix-ui-tpa-text-area-hover-text-color":"--wix-ui-tpa-text-area-hover-text-color","wix-ui-tpa-text-area-horizontal-padding":"--wix-ui-tpa-text-area-horizontal-padding","wix-ui-tpa-text-area-vertical-padding":"--wix-ui-tpa-text-area-vertical-padding","wix-ui-tpa-text-area-padding-inline-start":"--wix-ui-tpa-text-area-padding-inline-start","default-main-border-width":"--TextArea3811639133-default-main-border-width","default-background-color":"--TextArea3811639133-default-background-color"},c=t.stc.bind(null,a),d=t.sts.bind(null,a),u=d},2764:(e,r,t)=>{t.r(r),t.d(r,{default:()=>P});var a=t(77940),o=t(60751),i=t.n(o),s=t(8419),l=t(8938),n=t(88718),c=t(32886),d=t(88553),u=t(36783),p=t(80211),h=t(55530),x=t.n(h),w=t(18675),b=t(93885);var m=t(14507);const g="data-disabled",f="data-theme",_="data-error",E="data-success",v="data-focused";var T,I=t(46973),k=t(99528),C=t(88787),A=t(50815);!function(e){e.Box="box",e.Line="line"}(T||(T={}));var B=t(18061),y=t(68403),F=t(96616);class R extends o.Component{constructor(e){super(e),this._rootRef=o.createRef(),this.state={focused:!1,isErrorTooltipShown:!1},this._textAreaRef=o.createRef(),this.focus=e=>{this._textAreaRef.current.focus(e),this.setState({focused:!0})},this.blur=()=>{this._textAreaRef.current.blur(),this.setState({focused:!1})},this._getErrorIconTooltip=()=>{const{isErrorTooltipShown:e}=this.state,{errorDescription:r,errorMessage:t,errorTooltipPlacement:a,errorTooltipMaxWidth:i}=this.props,s=r||t;return o.createElement(C.F,{"data-hook":"error-Icon",className:(0,w.st)(w.classes.iconTooltip),message:s,icon:o.createElement(m.A,{className:w.classes.errorIcon}),tooltipId:this._errorMessageId,tooltipSkin:I.j.Error,showTooltip:e,onClick:this._onErrorButtonClick,onBlur:this._onClickOutsideOrBlur,onClickOutside:this._onClickOutsideOrBlur,tooltipProps:{placement:a,appendTo:"scrollParent",moveBy:{x:5,y:0},maxWidth:i}})},this.showErrorTooltip=()=>{this._toggleTooltip(!0)},this.hideErrorTooltip=()=>{this._toggleTooltip(!1)},this._onFocus=e=>{this.props.onFocus&&this.props.onFocus(e),this.setState({focused:!0}),this._rootRef.current&&this._rootRef.current.contains(document.activeElement)&&this._toggleTooltip(!0)},this._toggleTooltip=e=>{this.setState({isErrorTooltipShown:e})},this._onErrorButtonClick=()=>{const{isErrorTooltipShown:e}=this.state;this._toggleTooltip(!e)},this._onClickOutsideOrBlur=()=>{this._rootRef.current&&!this._rootRef.current.contains(document.activeElement)&&this._toggleTooltip(!1)},this._onBlur=e=>{this.props.onBlur&&this.props.onBlur(e),this.setState({focused:!1})},this._getCharCount=()=>{const{value:e,maxLength:r}=this.props;return o.createElement("div",{"data-hook":"char-counter",className:w.classes.charCount},`${e.length}/${r}`)},this.labelForId=(0,y.A)("label-for-id_"),this._errorMessageId=(0,y.A)("error-message"),this.props.errorDescription&&(0,b.T5)("TextArea",'The prop "errorDescription" is deprecated and will be removed in a future version, please use the "errorMessage" prop instead.')}_getDataAttributes(){const{disabled:e,success:r,theme:t,error:a}=this.props,{focused:o}=this.state;return{[g]:e,[f]:t,[_]:a,[E]:r,[v]:o}}render(){const{value:e,theme:r,error:t,success:a,disabled:i,label:s,onChange:l,placeholder:n,errorDescription:c,errorMessage:d,errorAppearance:u,autoFocus:p,className:h,maxLength:b,showCharCount:m,"aria-describedby":g,newErrorMessage:f,withResize:_,required:E,showAsterisk:v,lang:T,rows:I}=this.props,C=this._getDataAttributes(),y=f?A.B:o.Fragment,R=t&&(c||d)&&!f,M=c||d,q=!!M,D={error:t,theme:r,success:a,disabled:i,newErrorMessage:f,hasErrorMessage:q},N=this.state.focused,S=(0,B.g)([g,M?this._errorMessageId:""]),L=E&&v;return o.createElement(y,{...f&&{visible:t&&q,message:M,errorAppearance:u,messageId:this._errorMessageId,className:(0,w.st)(w.classes.root,D,h),suffixText:m&&b&&this._getCharCount(),lang:T}},o.createElement(k.UR,null,(({rtl:c})=>o.createElement("div",{className:(0,w.st)(w.classes.root,{error:t,rtl:c,errorMessage:!!M,theme:r,success:a,disabled:i},h),...C,...f?{}:{lang:T},"data-hook":this.props["data-hook"],ref:this._rootRef},s&&o.createElement("label",{htmlFor:this.props.id||this.labelForId,"data-hook":"text-area-label",className:(0,w.st)(w.classes.label)},s,L&&o.createElement(F.n,{dataHook:"label-asterisk"})),o.createElement("div",{className:(0,w.st)(w.classes.textAreaWrapper,{withResize:_},x()({[w.classes.focused]:N}))},o.createElement("textarea",{value:e,disabled:i,onChange:e=>{i||l(e)},id:this.props.id||this.labelForId,maxLength:b,autoFocus:p,onFocus:this._onFocus,onBlur:this._onBlur,placeholder:n,className:x()(w.classes.textArea,"has-custom-focus"),"data-hook":"text-area","aria-label":this.props.ariaLabel,"aria-describedby":S,"aria-invalid":t,required:E,ref:this._textAreaRef,rows:I}),R&&o.createElement("div",{"data-hook":"text-area-error-icon",className:w.classes.errorIconWrapper},this._getErrorIconTooltip())),b&&!(f&&t&&M)&&m&&this._getCharCount()))))}}R.contextType=k.rs,R.displayName="TextArea",R.defaultProps={error:!1,success:!1,disabled:!1,placeholder:"",errorMessage:"",errorDescription:"",theme:T.Box,showCharCount:!1,errorTooltipPlacement:"top-end",withResize:!1,required:!1};var M="sETfmIu",q="sZ5EaP4",D="s__6MNZOg",N="s__7mEBKn";const S=e=>{let{label:r,placeholder:t,required:a,value:s,onChange:l,onFocus:n,onBlur:c,disabled:d=!1,inputId:u,hasError:h,ariaDescribedBy:w}=e;const b=(0,p.EG)().includes(p.bD.INPUT_FIELDS_HOVER),m=(0,o.useRef)(null),g=(0,o.useCallback)((e=>{const r=e.target.value;l(r)}),[l]);return i().createElement(R,{id:u,error:h,newErrorMessage:!0,className:x()(M,N,{[q]:h,[D]:b}),ariaLabel:r??"",value:s?String(s):"",onChange:g,onFocus:n,onBlur:c,disabled:d,placeholder:t,theme:T.Box,"aria-describedby":w,"aria-invalid":h,required:a,ref:m})},L=e=>{let{id:r,target:t,label:a,hideLabel:o,placeholder:p,required:h,value:x,onChange:w,onFocus:b,onBlur:m,error:g,customErrors:f,disabled:_=!1,description:E,fieldType:v=s.bh.TEXT_AREA,FieldLayout:T,requiredIndicatorType:I,requiredIndicatorPlacement:k}=e;const{labelId:C,inputId:A,errorId:B,descriptionId:y,ariaDescribedBy:F}=(0,n.zF)(r,g,E),{hasError:R,errorMessage:M}=(0,l.q)(g,f,v);return i().createElement(T,{fieldId:r,renderLabel:()=>i().createElement(u.f,{labelId:C,htmlFor:A,required:h,label:a,hideLabel:o,requiredIndicatorType:I,requiredIndicatorPlacement:k}),renderInput:()=>i().createElement(S,{label:a,placeholder:p,required:h,value:x,onChange:w,onFocus:b,onBlur:m,disabled:_,inputId:A,hasError:R,ariaDescribedBy:F,fieldId:r}),renderDescription:()=>i().createElement(i().Fragment,null,i().createElement(d.f,{target:t,hasError:R,errorMessage:M,id:B}),i().createElement(c.D,{description:E,id:y}))})},P=e=>{const r=s.bh.TEXT_AREA;return i().createElement(L,(0,a.A)({},e,{fieldType:r}))}},96616:(e,r,t)=>{t.d(r,{n:()=>s});var a=t(60751),o=t.n(a),i="sqq2p13";const s=({dataHook:e,...r})=>o().createElement("span",{className:i,"aria-hidden":"true","data-hook":e,...r},"*")}}]);
//# sourceMappingURL=form-app-text-area.chunk.min.js.map