"use strict";(self.webpackJsonp__wix_thunderbolt_app=self.webpackJsonp__wix_thunderbolt_app||[]).push([[1141],{41017:function(e,t,i){var n=this&&this.__createBinding||(Object.create?function(e,t,i,n){void 0===n&&(n=i);var r=Object.getOwnPropertyDescriptor(t,i);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[i]}}),Object.defineProperty(e,n,r)}:function(e,t,i,n){void 0===n&&(n=i),e[n]=t[i]}),r=this&&this.__exportStar||function(e,t){for(var i in e)"default"===i||Object.prototype.hasOwnProperty.call(t,i)||n(t,e,i)};Object.defineProperty(t,"__esModule",{value:!0}),r(i(12960),t)},12960:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.implementStaticService=t.defineStaticService=void 0;const n=i(24150);t.defineStaticService=n.defineService,t.implementStaticService=n.implementService},63035:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ConsentPolicyDefinition=void 0;const n=i(41017);t.ConsentPolicyDefinition=(0,n.defineStaticService)("viewer-core/viewer-service-consent-policy")},24150:(e,t)=>{function i(e,t){return t}Object.defineProperty(t,"__esModule",{value:!0}),t.defineService=function(e){return e},t.implementService=i,i.withConfig=function(){return function(e,t){return t}}}}]);
//# sourceMappingURL=https://static.parastorage.com/services/wix-thunderbolt/dist/consentPolicy.2eb235e0.chunk.min.js.map