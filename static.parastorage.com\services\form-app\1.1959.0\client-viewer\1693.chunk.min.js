"use strict";(("undefined"!=typeof self?self:this).webpackJsonp__wix_form_app=("undefined"!=typeof self?self:this).webpackJsonp__wix_form_app||[]).push([[1693],{92895:(t,e,r)=>{r.d(e,{o:()=>c});var n=r(70119),o=r(65054),s=r(43373);const c=(0,n.L3)((t=>Array.isArray(t)?{id:"root",type:o.A.UNRECOGNIZED,nodes:t}:t),(0,s.N)((t=>Array.isArray(t.nodes)?t.nodes:[])))},43373:(t,e,r)=>{r.d(e,{N:()=>a});var n=r(16307),o=r(70119),s=r(59202),c=r(65327),u=r(96351);const a=t=>e=>{class r{constructor(t,e){this.traversal=t,this.tree=e}filter(t){return new r(this.traversal.composePrism(u.My.fromPredicate(t)),this.tree)}map(t){return new r(this.traversal.composeLens(new u.J2(t,(()=>t=>t))),this.tree)}chain(t){return(0,o.Fs)(this.get(),n.Tj(t))}get(){return(0,o.Fs)(this.traversal.asFold().getAll(this.tree),n.Tj((0,s.aN)(Boolean)),n.oE)}}const a=c.jh(e,(e=>[e,t(e)]));return new r((0,u.Un)(c.B2)(),a)}},97821:(t,e,r)=>{r.d(e,{Uo:()=>f,jJ:()=>l});var n=r(16307);const o={concat:(t,e)=>t&&e,empty:!0},s={concat:(t,e)=>t||e,empty:!1};var c=r(66005),u=r(70119),a=r(87029),i=(r(59202),r(20681));const l=(t,e,r)=>{const o=c.CA(i.$1()),s=i.KO(o)(c.kb(e));return(0,u.Fs)(s((0,u.Fs)(r,n.Tj((r=>(0,u.Fs)(t,c.aN(r[0],(()=>e)),c.Tj(r[1])))))),c.AU(u.D_,u.D_))},p=t=>e=>r=>(0,u.Fs)(e,n.ap(n.of(r)),(0,a.KO)(t)),f=p(o);p(s)},16199:(t,e,r)=>{function n(t){return[...t].reduce(((t,[e,r])=>(t[e]=r,t)),{})}r.d(e,{P:()=>n})},75089:(t,e,r)=>{r(35323)},35323:(t,e,r)=>{r(22562)},16307:(t,e,r)=>{r.d(e,{ap:()=>m,oE:()=>F,JI:()=>k,Tj:()=>h,of:()=>f,Ej:()=>p,hV:()=>_});r(35323),r(5122);var n=r(70119),o=r(22562),s=(r(60632),r(9384));const c=t=>e=>[...e,t],u=c;o.aO;const a=t=>[t];s.yO,s.ue,s.TS,s.RE,s.XK,s.$v,s.d5,s.cs,s.nZ,o.FR,s.d5,s.HV,s.jk,s.T9,s.$X;s.KO;var i=r(89033);r(33896),r(8920);const l=u;const p=t=>t.length;i.$I,i.d5,i.HV;i.SL;i.CX;i.a0,i.Kl;i.Mu;i.Yk,i.t3;const f=a,h=t=>e=>e.map((e=>t(e))),m=t=>d((e=>(0,n.Fs)(t,h(e)))),d=(0,n.XY)(2,((t,e)=>(0,n.Fs)(t,(t=>e=>{const r=[];for(let n=0;n<e.length;n++){const o=t(n,e[n]);for(let t=0;t<o.length;t++)r.push(o[t])}return r})(((t,r)=>e(r,t)))))),g=t=>e=>{const r=[];for(let n=0;n<e.length;n++){const s=t(n,e[n]);o.Ru(s)&&r.push(s.value)}return r},v=t=>g(((e,r)=>t(r))),F=v(n.D_),y=(i.ue,i.yO,i.TS,i.RE),_=(i.o8,i.$v,t=>{const e=O(t);return t=>e(((e,r)=>t(r)))}),O=t=>e=>y(t.of([]),((r,o,s)=>t.ap(t.map(o,(t=>e=>(0,n.Fs)(t,l(e)))),e(r,s)))),k=(i.cs,()=>({concat:(t,e)=>t.concat(e),empty:[]}));i.nZ,i.OZ,i.xo,i._k,i.Si,i.$X,o.FR},60802:(t,e,r)=>{function n(t){return(e,r)=>t.chain(e,(e=>t.map(r(e),(()=>e))))}r.d(e,{Mi:()=>n})},66005:(t,e,r)=>{r.d(e,{AU:()=>R,aN:()=>O,CA:()=>m,kb:()=>a,Tj:()=>d});r(75089),r(35323);var n=r(60802);var o=r(5122),s=r(70119),c=r(2768),u=r(22562);r(33896),r(8920);const a=u.kb,i=u.pG,l=(0,s.XY)(2,((t,e)=>k(t)?t:e(t.right))),p=(t,e)=>(0,s.Fs)(t,d(e)),f=(t,e)=>(0,s.Fs)(t,F(e)),h="Either",m=t=>({concat:(e,r)=>k(r)?e:k(e)?r:i(t.concat(e.right,r.right))}),d=t=>e=>k(e)?e:i(t(e.right)),g={URI:h,map:p},v=((0,s.XY)(2,(0,c.as)(g)),(0,c.Pg)(g),i),F=t=>e=>k(e)?e:k(t)?t:i(e.right(t.right)),y={URI:h,map:p,ap:f,chain:l},_={URI:h,fromEither:s.D_},O=(0,o.aN)(_),k=u.OC,w=(u.FG,(t,e)=>r=>k(r)?t(r.left):e(r.right)),R=w;n.Mi(y);u.FR,u.rb},13669:(t,e,r)=>{r.d(e,{ee:()=>n,w_:()=>o});r(70119);const n=t=>({equals:(e,r)=>e===r||t(e,r)}),o={equals:(t,e)=>t===e};o.equals},5122:(t,e,r)=>{r.d(e,{aN:()=>c,j_:()=>a});var n=r(60802),o=r(70119),s=r(22562);function c(t){return(e,r)=>n=>t.fromEither(e(n)?s.pG(n):s.kb(r(n)))}function u(t){return e=>(0,o.L3)(e,t.fromEither)}function a(t,e){const r=u(t),o=(0,n.Mi)(e);return(t,e)=>o(t,r(e))}},2768:(t,e,r)=>{function n(t){return(e,r)=>t.map(e,(()=>r))}function o(t){const e=n(t);return t=>e(t,void 0)}r.d(e,{Pg:()=>o,as:()=>n})},87029:(t,e,r)=>{r.d(e,{AU:()=>u,Hw:()=>i,KO:()=>c,s2:()=>a,w3:()=>s});var n=r(22562),o=r(20681);const s=t=>{const e={};for(const r in t)n.zy.call(t,r)&&(e[r]=t[r].empty);return{concat:o.w3(t).concat,empty:e}},c=t=>o.KO(t)(t.empty),u=(o.sF.concat,c),a={concat:o.Tb.concat,empty:!0},i={concat:o.ey.concat,empty:!1};o.Bn.concat,o.pO.concat,o.vH.concat},59202:(t,e,r)=>{r.d(e,{$I:()=>ot,AU:()=>ut,Tj:()=>S,WL:()=>at,aN:()=>m,cy:()=>lt,k$:()=>it,te:()=>ft,uK:()=>pt,zN:()=>h});r(75089),r(35323);var n=r(60802),o=r(5122),s=r(70119),c=r(2768),u=r(22562),a=r(97282),i=r(20681),l=r(33896),p=r(8920);const f=u.dv,h=u.zN;function m(t){return e=>t(e)?h(e):f}const d=t=>"Left"===t._tag?f:h(t.right),g=(t,e)=>(0,s.Fs)(t,S(e)),v=(t,e)=>(0,s.Fs)(t,b(e)),F=(t,e,r)=>(0,s.Fs)(t,U(e,r)),y=t=>{const e=$(t);return(t,r)=>(0,s.Fs)(t,e(r))},_=(t,e,r)=>(0,s.Fs)(t,C(e,r)),O=t=>{const e=Z(t);return(t,r)=>(0,s.Fs)(t,e(r))},k=(t,e)=>(0,s.Fs)(t,A(e)),w=(t,e)=>(0,s.Fs)(t,H(e)),R=(t,e)=>(0,s.Fs)(t,V(e)),I=(t,e)=>(0,s.Fs)(t,E(e)),j=(t,e)=>(0,s.Fs)(t,x(e)),T=(t,e)=>(0,s.Fs)(t,B(e)),z="Option",N=t=>({concat:(e,r)=>ot(e)?r:ot(r)?e:h(t.concat(e.value,r.value)),empty:f}),S=t=>e=>ot(e)?f:h(t(e.value)),G={URI:z,map:g},L=((0,s.XY)(2,(0,c.as)(G)),(0,c.Pg)(G),h),b=t=>e=>ot(e)||ot(t)?f:h(e.value(t.value)),P=(0,s.XY)(2,((t,e)=>ot(t)?f:e(t.value))),M={URI:z,map:g,ap:v,chain:P},U=(t,e)=>r=>ot(r)?t:e(t,r.value),$=t=>e=>r=>ot(r)?t.empty:e(r.value),C=(t,e)=>r=>ot(r)?t:e(r.value,t),K=(0,s.XY)(2,((t,e)=>ot(t)?e():t)),A=K,q=()=>f,E=t=>e=>ot(e)?f:h(t(e)),X=P(s.D_),D=(0,l.Ci)(f,f),Y=t=>ot(t)?D:(0,l.Ci)((t=>"Right"===t._tag?f:h(t.left))(t.value),d(t.value)),J={URI:z,compact:X,separate:Y},H=t=>e=>ot(e)?f:t(e.value)?e:f,V=t=>e=>ot(e)?f:t(e.value),x=t=>e=>(0,l.Ci)(w(e,(0,a.AU)(t)),w(e,t)),B=t=>(0,s.L3)(S(t),Y),Z=t=>e=>r=>ot(r)?t.of(f):t.map(e(r.value),h),W=t=>e=>ot(e)?t.of(f):t.map(e.value,h),Q={URI:z,map:g,reduce:F,foldMap:y,reduceRight:_,traverse:O,sequence:W},tt=(0,p.nG)(Q,J),et=(0,p.ez)(Q,J),rt=()=>f,nt={URI:z,fromEither:d},ot=(u.Ru,t=>"None"===t._tag),st=(t,e)=>r=>ot(r)?t():e(r.value),ct=st,ut=ct,at=t=>e=>ot(e)?t():e.value,it=(n.Mi(M),(0,o.j_)(nt,M),t=>null==t?f:h(t));u.FR,u.rb;const lt=P;const pt={URI:z,map:g,of:L,ap:v,chain:P,reduce:F,foldMap:y,reduceRight:_,traverse:O,sequence:W,zero:q,alt:k,extend:I,compact:X,separate:Y,filter:w,filterMap:R,partition:j,partitionMap:T,wither:tt,wilt:et,throwError:rt},ft=()=>N((0,i.$1)())},60632:(t,e,r)=>{r.d(e,{I3:()=>c,T9:()=>a,jk:()=>u});var n=r(13669),o=r(70119);const s=t=>(e,r)=>e===r||0===t(e,r),c=t=>({equals:s(t),compare:(e,r)=>e===r?0:t(e,r)}),u=(o.ue,t=>(e,r)=>e===r||t.compare(e,r)<1?e:r),a=t=>(e,r)=>e===r||t.compare(e,r)>-1?e:r;n.w_.equals},97282:(t,e,r)=>{r.d(e,{AU:()=>n});r(70119);const n=t=>e=>!t(e)},89033:(t,e,r)=>{r.d(e,{t3:()=>k,Yk:()=>O,_k:()=>U,xo:()=>M,Mu:()=>_,Si:()=>C,CX:()=>v,SL:()=>g,Kl:()=>y,a0:()=>F,ue:()=>z,yO:()=>j,nZ:()=>b,OZ:()=>P,cs:()=>L,d5:()=>m,$X:()=>K,HV:()=>d,$I:()=>h,TS:()=>T,o8:()=>S,$v:()=>G,RE:()=>N,VN:()=>$});r(35323);var n=r(13669),o=(r(5122),r(70119)),s=r(22562);const c={equals:(t,e)=>t===e},u={equals:c.equals,compare:(t,e)=>t<e?-1:t>e?1:0};var a=r(60632),i=r(9384);r(33896),r(8920);const l=i.J_;i.Hs,i.Cs,i.BC,i.Ak;const p=(t,e)=>r=>l(r)?e(r):t(),f=i.kI;function h(t,e){return void 0===e?e=>h(t,e):f(t,e)?s.dv:s.zN(e[t])}const m=t=>l(t)?s.zN(i.d5(t)):s.dv,d=t=>l(t)?s.zN(i.HV(t)):s.dv;const g=t=>e=>{for(let r=0;r<e.length;r++)if(t(e[r]))return s.zN(r);return s.dv};const v=t=>e=>{for(let r=0;r<e.length;r++){const n=t(e[r]);if(s.Ru(n))return n}return s.dv};const F=t=>e=>{for(let r=e.length-1;r>=0;r--){const n=t(e[r]);if(s.Ru(n))return n}return s.dv},y=t=>e=>{for(let r=e.length-1;r>=0;r--)if(t(e[r]))return s.zN(r);return s.dv};function _(t){return(e,r)=>{if(void 0===r){const r=_(t);return t=>r(e,t)}let n=0;for(;n<r.length;n++)if(o=r[n],t.equals(o,e))return!0;var o;return!1}}const O=(t,e)=>(0,o.Fs)(t,M(e)),k=(t,e)=>(0,o.Fs)(t,U(e)),w=i.of,R=t=>e=>{const r=[];for(let n=0;n<e.length;n++){const o=t(n,e[n]);s.Ru(o)&&r.push(o.value)}return r},I=t=>R(((e,r)=>t(r))),j=(o.D_,t=>e=>r=>r.reduce(((r,n,o)=>t.concat(r,e(o,n))),t.empty)),T=(t,e)=>N(t,((t,r,n)=>e(r,n))),z=t=>{const e=j(t);return t=>e(((e,r)=>t(r)))},N=(t,e)=>r=>{const n=r.length;let o=t;for(let t=0;t<n;t++)o=e(t,o,r[t]);return o},S=(t,e)=>G(t,((t,r,n)=>e(r,n))),G=(t,e)=>r=>r.reduceRight(((t,r,n)=>e(n,r,t)),t),L=t=>({show:e=>`[${e.map(t.show).join(", ")}]`}),b=t=>(0,n.ee)(((e,r)=>e.length===r.length&&e.every(((e,n)=>t.equals(e,r[n]))))),P=t=>(0,a.I3)(((e,r)=>{const n=e.length,o=r.length,s=Math.min(n,o);for(let n=0;n<s;n++){const o=t.compare(e[n],r[n]);if(0!==o)return o}return u.compare(n,o)})),M=t=>e=>{const r=[...t(e)],n=[];for(;r.length>0;){const e=r.shift();s.OC(e)?r.unshift(...t(e.left)):n.push(e.right)}return n},U=t=>e=>{const r=t(e),n=[],o=[];function c(e){s.OC(e)?t(e.left).forEach((t=>n.push(t))):o.push(e.right)}for(const t of r)c(t);for(;n.length>0;)c(n.shift());return o},$=(i.jK,(t,e,r)=>l(r)?i.VN(t,e,r):r);i.Ie;function C(t){return e=>e.every(t)}const K=t=>{const e=i.$X(t);return r=>p((()=>t.empty),e(r))};s.FR,i.y1,i.qu,i.Kv},9384:(t,e,r)=>{r.d(e,{$X:()=>U,$v:()=>j,Ak:()=>f,BC:()=>h,Cs:()=>l,HV:()=>L,Hs:()=>p,Ie:()=>u,J_:()=>a,KO:()=>M,Kv:()=>C,RE:()=>R,T9:()=>P,TS:()=>O,VN:()=>d,XK:()=>w,cs:()=>z,d5:()=>S,jK:()=>m,jk:()=>b,kI:()=>i,nZ:()=>N,of:()=>_,qu:()=>$,ue:()=>k,y1:()=>v,yO:()=>I});r(35323);var n=r(13669),o=r(70119),s=r(22562),c=(r(60632),r(20681));const u=s.rb,a=s.J_,i=(t,e)=>t<0||t>=e.length,l=t=>e=>[t,...e],p=l,f=t=>e=>[...e,t],h=f,m=(t,e,r)=>{if(a(r)){const n=s.aO(r);return n.splice(t,0,e),n}return[e]},d=(t,e,r)=>{if(r[t]===e)return r;{const n=s.aO(r);return n[t]=e,n}},g=t=>e=>{const r=Math.max(0,Math.floor(e)),n=[t(0)];for(let e=1;e<r;e++)n.push(t(e));return n},v=(t,e)=>t<=e?g((e=>t+e))(e-t+1):[t];function F(t,e){return e?t.concat(e):e=>e.concat(t)}const y=t=>e=>{const r=[t,e[0]];for(let n=1;n<e.length;n++)r.push(t,e[n]);return r},_=s.Gr,O=(t,e)=>R(t,((t,r,n)=>e(r,n))),k=t=>e=>r=>r.slice(1).reduce(((r,n)=>t.concat(r,e(n))),e(r[0])),w=(t,e)=>j(t,((t,r,n)=>e(r,n))),R=(t,e)=>r=>r.reduce(((t,r,n)=>e(n,t,r)),t),I=t=>e=>r=>r.slice(1).reduce(((r,n,o)=>t.concat(r,e(o+1,n))),e(0,r[0])),j=(t,e)=>r=>r.reduceRight(((t,r,n)=>e(n,r,t)),t),T=s.d5,z=t=>({show:e=>`[${e.map(t.show).join(", ")}]`}),N=t=>(0,n.ee)(((e,r)=>e.length===r.length&&e.every(((e,n)=>t.equals(e,r[n]))))),S=(s.FR,T),G=s.RT,L=t=>t[t.length-1],b=t=>{const e=c.jk(t);return t=>t.reduce(e.concat)},P=t=>{const e=c.T9(t);return t=>t.reduce(e.concat)},M=t=>e=>e.reduce(t.concat),U=t=>{const e=M(t);return t=>(0,o.L3)((t=>e=>{const r=G(e);return a(r)?(0,o.Fs)(r,y(t),p(S(e))):e})(t),e)};function $(t,e){return void 0===e?p(t):(0,o.Fs)(e,p(t))}const C=(t,e)=>(0,o.Fs)(t,F([e]))},29056:(t,e,r)=>{r.d(e,{$I:()=>d,C:()=>b,D:()=>h,Ej:()=>a,Gr:()=>k,HM:()=>p,Im:()=>i,JI:()=>ut,Jc:()=>j,K2:()=>G,K5:()=>$,Kw:()=>D,Mu:()=>S,PT:()=>at,Q:()=>M,S:()=>C,Si:()=>z,Tj:()=>F,UX:()=>H,WF:()=>Y,Ww:()=>m,XI:()=>R,Yz:()=>A,e:()=>L,eb:()=>E,fP:()=>P,ge:()=>K,jB:()=>W,ji:()=>q,kM:()=>X,l7:()=>V,mQ:()=>it,nZ:()=>ct,oE:()=>nt,pb:()=>B,qe:()=>U,rF:()=>w,rO:()=>v,rT:()=>Q,sj:()=>ot,wL:()=>J,x1:()=>Z,zN:()=>N,zy:()=>f});var n=r(13669),o=r(70119),s=r(22562),c=r(33896),u=r(80666);r(8920);const a=t=>Object.keys(t).length,i=t=>{for(const e in t)if(s.zy.call(t,e))return!1;return!0},l=t=>e=>Object.keys(e).sort(t.compare);u._k;const p=(t,e)=>r=>{if(s.zy.call(r,t)&&r[t]===e)return r;const n=Object.assign({},r);return n[t]=e,n},f=(t,e)=>s.zy.call(e,t);function h(t){return e=>{if(!s.zy.call(e,t))return e;const r=Object.assign({},e);return delete r[t],r}}function m(t){return(e,r)=>{if(void 0===r){const r=m(t);return t=>r(t,e)}for(const n in e)if(!s.zy.call(r,n)||!t.equals(e[n],r[n]))return!1;return!0}}function d(t,e){return void 0===e?e=>d(t,e):s.zy.call(e,t)?s.zN(e[t]):s.dv}const g={};function v(t){return e=>{const r={};for(const n in e)s.zy.call(e,n)&&(r[n]=t(n,e[n]));return r}}function F(t){return v(((e,r)=>t(r)))}function y(...t){if(2===t.length)return y(u._k)(...t);const e=l(t[0]);return(t,r)=>n=>{let o=t;const s=e(n),c=s.length;for(let t=0;t<c;t++){const e=s[t];o=r(e,o,n[e])}return o}}function _(t){if("compare"in t){const e=l(t);return t=>r=>n=>{let o=t.empty;const s=e(n),c=s.length;for(let e=0;e<c;e++){const c=s[e];o=t.concat(o,r(c,n[c]))}return o}}return _(u._k)(t)}function O(...t){if(2===t.length)return O(u._k)(...t);const e=l(t[0]);return(t,r)=>n=>{let o=t;const s=e(n);for(let t=s.length-1;t>=0;t--){const e=s[t];o=r(e,n[e],o)}return o}}const k=(t,e)=>({[t]:e});function w(t){return V(u._k)(t)}function R(t){return e=>{const r={},n={};for(const o in e)if(s.zy.call(e,o)){const s=t(o,e[o]);switch(s._tag){case"Left":r[o]=s.left;break;case"Right":n[o]=s.right}}return(0,c.Ci)(r,n)}}function I(t){return e=>{const r={},n={};for(const o in e)if(s.zy.call(e,o)){const s=e[o];t(o,s)?n[o]=s:r[o]=s}return(0,c.Ci)(r,n)}}function j(t){return e=>{const r={};for(const n in e)if(s.zy.call(e,n)){const o=t(n,e[n]);s.Ru(o)&&(r[n]=o.value)}return r}}function T(t){return e=>{const r={};let n=!1;for(const o in e)if(s.zy.call(e,o)){const s=e[o];t(o,s)?r[o]=s:n=!0}return n?r:e}}function z(t){return e=>{for(const r in e)if(!t(e[r]))return!1;return!0}}function N(t){return e=>{for(const r in e)if(t(e[r]))return!0;return!1}}function S(t){return(e,r)=>{if(void 0===r){const r=S(t);return t=>r(e,t)}for(const n in r)if(t.equals(r[n],e))return!0;return!1}}const G=(t,e)=>(0,o.Fs)(t,F(e)),L=(t,e)=>(0,o.Fs)(t,v(e)),b=t=>{const e=tt(t);return(t,r,n)=>(0,o.Fs)(t,e(r,n))},P=t=>e=>{const r=et(t)(e);return(t,e)=>(0,o.Fs)(t,r(e))},M=t=>{const e=rt(t);return(t,r,n)=>(0,o.Fs)(t,e(r,n))},U=(t,e)=>(0,o.Fs)(t,B(e)),$=(t,e)=>(0,o.Fs)(t,Z(e)),C=(t,e)=>(0,o.Fs)(t,W(e)),K=(t,e)=>(0,o.Fs)(t,Q(e)),A=t=>{const e=y(t);return(t,r,n)=>(0,o.Fs)(t,e(r,n))},q=t=>{const e=_(t);return t=>{const r=e(t);return(t,e)=>(0,o.Fs)(t,r(e))}},E=t=>{const e=O(t);return(t,r,n)=>(0,o.Fs)(t,e(r,n))},X=(t,e)=>(0,o.Fs)(t,R(e)),D=(t,e)=>(0,o.Fs)(t,I(e)),Y=(t,e)=>(0,o.Fs)(t,j(e)),J=(t,e)=>(0,o.Fs)(t,T(e)),H=t=>{const e=x(t);return t=>{const r=e(t);return(t,e)=>r(t,(0,o.L3)(o.SK,e))}},V=t=>{const e=H(t);return t=>{const r=e(t);return t=>r(t,o.D_)}},x=t=>e=>{const r=l(t);return(t,n)=>{const o=r(t);if(0===o.length)return e.of(g);let s=e.of({});for(const r of o)s=e.ap(e.map(s,(t=>e=>Object.assign({},t,{[r]:e}))),n(r,t[r]));return s}},B=t=>T(((e,r)=>t(r))),Z=t=>j(((e,r)=>t(r))),W=t=>I(((e,r)=>t(r))),Q=t=>R(((e,r)=>t(r)));function tt(...t){if(1===t.length){const e=y(t[0]);return(t,r)=>e(t,((t,e,n)=>r(e,n)))}return tt(u._k)(...t)}function et(t){if("compare"in t){const e=_(t);return t=>{const r=e(t);return t=>r(((e,r)=>t(r)))}}return et(u._k)(t)}function rt(...t){if(1===t.length){const e=O(t[0]);return(t,r)=>e(t,((t,e,n)=>r(e,n)))}return rt(u._k)(...t)}const nt=t=>{const e={};for(const r in t)if(s.zy.call(t,r)){const n=t[r];s.Ru(n)&&(e[r]=n.value)}return e},ot=t=>{const e={},r={};for(const n in t)if(s.zy.call(t,n)){const o=t[n];s.OC(o)?e[n]=o.left:r[n]=o.right}return(0,c.Ci)(e,r)},st="ReadonlyRecord";function ct(t){const e=m(t);return(0,n.ee)(((t,r)=>e(t)(r)&&e(r)(t)))}function ut(t){return{concat:(e,r)=>{if(i(e))return r;if(i(r))return e;const n=Object.assign({},e);for(const o in r)s.zy.call(r,o)&&(n[o]=s.zy.call(e,o)?t.concat(e[o],r[o]):r[o]);return n},empty:g}}u._k,u._k,u._k,u._k,u._k,u._k,u._k,u._k,u._k,u._k,u._k,u._k,u._k,u._k,u._k,u._k,u._k,u._k,u._k,u._k,u._k,u._k,u._k,u._k,u._k;const at=p;function it(t,e){return s.zy.call(void 0===e?this:e,t)}u._k,u._k,u._k,u._k,u._k,u._k,u._k,u._k},20681:(t,e,r)=>{r.d(e,{KO:()=>p,$1:()=>i,HV:()=>l,T9:()=>u,jk:()=>c,Tb:()=>m,ey:()=>d,vH:()=>F,Bn:()=>g,pO:()=>v,sF:()=>f,w3:()=>a});var n=r(70119),o=r(22562);var s=r(60632);const c=t=>({concat:s.jk(t)}),u=t=>({concat:s.T9(t)}),a=t=>({concat:(e,r)=>{const n={};for(const s in t)o.zy.call(t,s)&&(n[s]=t[s].concat(e[s],r[s]));return n}}),i=()=>({concat:n.D_}),l=()=>({concat:(t,e)=>e}),p=t=>e=>r=>r.reduce(((e,r)=>t.concat(e,r)),e),f=(h=void 0,{concat:()=>h});var h;const m={concat:(t,e)=>t&&e},d={concat:(t,e)=>t||e},g={concat:(t,e)=>t+e},v={concat:(t,e)=>t+e},F={concat:(t,e)=>t*e}},33896:(t,e,r)=>{r.d(e,{Ci:()=>n});r(70119);const n=(t,e)=>({left:t,right:e})},65327:(t,e,r)=>{r.d(e,{AU:()=>a,B2:()=>T,jh:()=>c});r(35323);var n=r(16307),o=(r(13669),r(70119)),s=r(22562);function c(t,e){const[r,n]=e(t);return{value:r,forest:u(n,e)}}function u(t,e){return t.map((t=>c(t,e)))}function a(t){const e=r=>t(r.value,r.forest.map(e));return e}const i=(t,e)=>(0,o.Fs)(t,F(e)),l=(t,e)=>g(t,(t=>(0,o.Fs)(e,F(t)))),p=(t,e,r)=>(0,o.Fs)(t,y(e,r)),f=t=>{const e=_(t);return(t,r)=>(0,o.Fs)(t,e(r))},h=(t,e,r)=>(0,o.Fs)(t,O(e,r)),m=(t,e)=>(0,o.Fs)(t,v(e)),d=t=>{const e=w(t);return(t,r)=>(0,o.Fs)(t,e(r))},g=(0,o.XY)(2,((t,e)=>{const{value:r,forest:o}=e(t.value);return{value:r,forest:(0,n.JI().concat)(o,t.forest.map(g(e)))}})),v=t=>e=>({value:t(e),forest:e.forest.map(v(t))}),F=t=>e=>({value:t(e.value),forest:e.forest.map(F(t))}),y=(t,e)=>r=>{let n=e(t,r.value);const s=r.forest.length;for(let t=0;t<s;t++)n=(0,o.Fs)(r.forest[t],y(n,e));return n},_=t=>e=>y(t.empty,((r,n)=>t.concat(r,e(n)))),O=(t,e)=>r=>{let n=t;for(let t=r.forest.length-1;t>=0;t--)n=(0,o.Fs)(r.forest[t],O(n,e));return e(r.value,n)},k=t=>t.value,w=t=>{const e=n.hV(t),r=n=>s=>t.ap(t.map(n(s.value),(t=>e=>({value:t,forest:e}))),(0,o.Fs)(s.forest,e(r(n))));return r},R=t=>w(t)(o.D_),I=t=>function(t,e=[]){return{value:t,forest:e}}(t),j="Tree";s.FR;const T={URI:j,map:i,of:I,ap:l,chain:g,reduce:p,foldMap:f,reduceRight:h,traverse:d,sequence:R,extract:k,extend:m}},99173:(t,e,r)=>{r.d(e,{wu:()=>a,wg:()=>u});r(70119);function n(t){return t[0]}function o(t){return t[1]}const s=n,c=o,u=t=>[c(t),s(t)];const a=(t,e)=>r=>[e(s(r)),t(c(r))]},8920:(t,e,r)=>{r.d(e,{ez:()=>n,nG:()=>o});r(22562);function n(t,e){return r=>{const n=t.traverse(r);return(t,o)=>r.map(n(t,o),e.separate)}}function o(t,e){return r=>{const n=t.traverse(r);return(t,o)=>r.map(n(t,o),e.compact)}}},70119:(t,e,r)=>{r.d(e,{D_:()=>n,Fs:()=>a,L3:()=>u,Mi:()=>o,SK:()=>i,XY:()=>l,dY:()=>s,ue:()=>c});function n(t){return t}const o=n;function s(t){return()=>t}const c=s(!0);function u(t,e,r,n,o,s,c,u,a){switch(arguments.length){case 1:return t;case 2:return function(){return e(t.apply(this,arguments))};case 3:return function(){return r(e(t.apply(this,arguments)))};case 4:return function(){return n(r(e(t.apply(this,arguments))))};case 5:return function(){return o(n(r(e(t.apply(this,arguments)))))};case 6:return function(){return s(o(n(r(e(t.apply(this,arguments))))))};case 7:return function(){return c(s(o(n(r(e(t.apply(this,arguments)))))))};case 8:return function(){return u(c(s(o(n(r(e(t.apply(this,arguments))))))))};case 9:return function(){return a(u(c(s(o(n(r(e(t.apply(this,arguments)))))))))}}}function a(t,e,r,n,o,s,c,u,a){switch(arguments.length){case 1:return t;case 2:return e(t);case 3:return r(e(t));case 4:return n(r(e(t)));case 5:return o(n(r(e(t))));case 6:return s(o(n(r(e(t)))));case 7:return c(s(o(n(r(e(t))))));case 8:return u(c(s(o(n(r(e(t)))))));case 9:return a(u(c(s(o(n(r(e(t))))))));default:{let t=arguments[0];for(let e=1;e<arguments.length;e++)t=arguments[e](t);return t}}}const i=(t,e)=>e;const l=(t,e)=>{const r="number"==typeof t?e=>e.length>=t:t;return function(){const t=Array.from(arguments);return r(arguments)?e.apply(this,t):r=>e(r,...t)}}},22562:(t,e,r)=>{r.d(e,{FG:()=>u,FR:()=>d,Gr:()=>l,J_:()=>p,OC:()=>c,RT:()=>h,Ru:()=>n,aO:()=>v,d5:()=>f,dv:()=>o,kb:()=>a,pG:()=>i,rb:()=>m,zN:()=>s,zy:()=>g});const n=t=>"Some"===t._tag,o={_tag:"None"},s=t=>({_tag:"Some",value:t}),c=t=>"Left"===t._tag,u=t=>"Right"===t._tag,a=t=>({_tag:"Left",left:t}),i=t=>({_tag:"Right",right:t}),l=t=>[t],p=t=>t.length>0,f=t=>t[0],h=t=>t.slice(1),m=[],d={},g=Object.prototype.hasOwnProperty,v=t=>[t[0],...t.slice(1)]},80666:(t,e,r)=>{r.d(e,{_k:()=>n});r(9384);const n={equals:(t,e)=>t===e,compare:(t,e)=>t<e?-1:t>e?1:0}},96351:(t,e,r)=>{r.d(e,{J2:()=>pt,My:()=>ft,Un:()=>yt});var n=r(16307),o=r(70119);const s=o.Mi;function c(t){const e=function(t){return{URI:i,_E:void 0,map:u,ap:(e,r)=>s(t.concat(e,r))}}(t);return{URI:i,_E:void 0,map:e.map,ap:e.ap,of:()=>s(t.empty)}}const u=(t,e)=>(0,o.Fs)(t,a(e)),a=()=>o.Mi,i="Const";var l=r(87029),p=r(59202);r(35323);const f=o.Fs;r(13669),r(22562),r(33896),r(8920);new Map;r(97282);new Set;r(89033),r(29056),r(66005);const h=t=>v(t.get,(0,o.L3)(t.reverseGet,o.dY)),m=t=>O((0,o.L3)(t.get,p.zN),t.reverseGet),d=t=>z((0,o.L3)(t.get,p.zN),(0,o.L3)(t.reverseGet,o.dY)),g=t=>b((e=>r=>n=>e.map(r(t.get(n)),(e=>t.reverseGet(e))))),v=(t,e)=>({get:t,set:e}),F=t=>z((0,o.L3)(t.get,p.zN),t.set),y=t=>b((e=>r=>n=>e.map(r(t.get(n)),(e=>t.set(e)(n))))),_=t=>e=>v((r=>t.get(e.get(r))),(r=>n=>e.set(t.set(r)(e.get(n)))(n))),O=(t,e)=>({getOption:t,reverseGet:e}),k=t=>z(t.getOption,(e=>j(e)(t))),w=t=>b((e=>r=>n=>f(t.getOption(n),p.AU((()=>e.of(n)),(o=>e.map(r(o),(e=>j(e)(t)(n)))))))),R=t=>e=>r=>f(e.getOption(r),p.Tj((n=>{const o=t(n);return o===n?r:e.reverseGet(o)}))),I=t=>e=>{const r=R(t)(e);return t=>f(r(t),p.WL((()=>t)))},j=t=>I((()=>t)),T=t=>O(p.aN(t),o.D_),z=(t,e)=>({getOption:t,set:e}),N=t=>b((e=>r=>n=>f(t.getOption(n),p.AU((()=>e.of(n)),(o=>e.map(r(o),(e=>t.set(e)(n)))))))),S=t=>e=>r=>f(e.getOption(r),p.Tj((n=>{const o=t(n);return o===n?r:e.set(o)(r)}))),G=t=>e=>{const r=S(t)(e);return t=>f(r(t),p.WL((()=>t)))},L=t=>e=>z((0,o.L3)(e.getOption,p.cy(t.getOption)),(r=>G(t.set(r))(e))),b=t=>({modifyF:t});function P(t){return e=>b((r=>n=>e.modifyF(r)(t.modifyF(r)(n))))}const M={URI:"Identity",map:(t,e)=>e(t),of:o.D_,ap:(t,e)=>t(e)};function U(t){return()=>b((e=>{const r=(t=>"Identity"===t.URI)(e)?t.map:t.traverse(e);return t=>e=>r(e,t)}))}const $=h,C=m,K=d,A=g;const q=()=>v(o.D_,o.dY),E=F,X=y,D=_,Y=t=>e=>L(k(t))(F(e));const J=t=>e=>v((r=>e.get(r)[t]),(r=>n=>{const o=e.get(n);return r===o[t]?n:e.set(Object.assign({},o,{[t]:r}))(n)})),H=(...t)=>e=>v((r=>{const n=e.get(r),o={};for(const e of t)o[e]=n[e];return o}),(r=>n=>{const o=e.get(n);for(const s of t)if(r[s]!==o[s])return e.set(Object.assign({},o,r))(n);return n}));const V=N,x=L,B=S,Z=G;const W=T,Q=k,tt=w,et=t=>e=>O((0,o.L3)(e.getOption,p.cy(t.getOption)),(0,o.L3)(t.reverseGet,e.reverseGet)),rt=t=>e=>L(F(t))(k(e));const nt=U,ot=P,st=t=>e=>e.modifyF(M)(t);const ct=t=>new pt(t.get,t.set),ut=t=>new ft(t.getOption,t.reverseGet),at=t=>new mt(t.getOption,t.set),it=t=>new dt(t.modifyF),lt=(t,e,r)=>r===t[e]?t:Object.assign({},t,{[e]:r});class pt{constructor(t,e){this.get=t,this.set=e,this._tag="Lens"}static fromPath(){const t=pt.fromProp();return e=>{const r=t(e[0]);return e.slice(1).reduce(((e,r)=>e.compose(t(r))),r)}}static fromProp(){return t=>ct(f(q(),J(t)))}static fromProps(){return t=>ct(f(q(),H(...t)))}static fromNullableProp(){return(t,e)=>new pt((r=>{const n=(0,p.k$)(r[t]);return(0,p.$I)(n)?e:n.value}),(e=>r=>lt(r,t,e)))}modify(t){return(t=>e=>r=>{const n=e.get(r),o=t(n);return n===o?r:e.set(o)(r)})(t)(this)}asOptional(){return at(E(this))}asTraversal(){return it(X(this))}asSetter(){return new Ft((t=>this.modify(t)))}asGetter(){return new gt((t=>this.get(t)))}asFold(){return new vt((()=>t=>e=>t(this.get(e))))}compose(t){return ct(D(t)(this))}composeLens(t){return this.compose(t)}composeGetter(t){return this.asGetter().compose(t)}composeFold(t){return this.asFold().compose(t)}composeOptional(t){return at(f(this,E,x(t)))}composeTraversal(t){return it(f(this,X,ot(t)))}composeSetter(t){return this.asSetter().compose(t)}composeIso(t){return ct(f(this,D(f(t,$))))}composePrism(t){return at(Y(t)(this))}}class ft{constructor(t,e){this.getOption=t,this.reverseGet=e,this._tag="Prism"}static fromPredicate(t){return ut(W(t))}static some(){return ht}modify(t){return e=>{const r=this.modifyOption(t)(e);return(0,p.$I)(r)?e:r.value}}modifyOption(t){return e=>p.uK.map(this.getOption(e),(r=>{const n=t(r);return n===r?e:this.reverseGet(n)}))}set(t){return this.modify((()=>t))}asOptional(){return at(Q(this))}asTraversal(){return it(tt(this))}asSetter(){return new Ft((t=>this.modify(t)))}asFold(){return new vt((t=>e=>r=>{const n=this.getOption(r);return(0,p.$I)(n)?t.empty:e(n.value)}))}compose(t){return ut(et(t)(this))}composePrism(t){return this.compose(t)}composeOptional(t){return at(f(this,Q,x(t)))}composeTraversal(t){return it(f(this,tt,ot(t)))}composeFold(t){return this.asFold().compose(t)}composeSetter(t){return this.asSetter().compose(t)}composeIso(t){return ut(f(this,et(f(t,C))))}composeLens(t){return at(rt(t)(this))}composeGetter(t){return this.asFold().compose(t.asFold())}}const ht=new ft(o.D_,p.zN);class mt{constructor(t,e){this.getOption=t,this.set=e,this._tag="Optional"}static fromPath(){const t=mt.fromNullableProp();return e=>{const r=t(e[0]);return e.slice(1).reduce(((e,r)=>e.compose(t(r))),r)}}static fromNullableProp(){return t=>new mt((e=>(0,p.k$)(e[t])),(e=>r=>null==r[t]?r:lt(r,t,e)))}static fromOptionProp(){const t=pt.fromProp();return e=>t(e).composePrism(ht)}modify(t){return Z(t)(this)}modifyOption(t){return B(t)(this)}asTraversal(){return it(V(this))}asFold(){return new vt((t=>e=>r=>{const n=this.getOption(r);return(0,p.$I)(n)?t.empty:e(n.value)}))}asSetter(){return new Ft((t=>this.modify(t)))}compose(t){return at(x(t)(this))}composeOptional(t){return this.compose(t)}composeTraversal(t){return it(f(this,V,ot(t)))}composeFold(t){return this.asFold().compose(t)}composeSetter(t){return this.asSetter().compose(t)}composeLens(t){return at(f(this,x(f(t,E))))}composePrism(t){return at(f(this,x(f(t,Q))))}composeIso(t){return at(f(this,x(f(t,K))))}composeGetter(t){return this.asFold().compose(t.asFold())}}class dt{constructor(t){this.modifyF=t,this._tag="Traversal"}modify(t){return st(t)(this)}set(t){return(t=>st((()=>t)))(t)(this)}filter(t){return it(function(t){return ot(w(T(t)))}(t)(this))}asFold(){return new vt((t=>e=>this.modifyF(c(t))((t=>s(e(t))))))}asSetter(){return new Ft((t=>this.modify(t)))}compose(t){return it(ot(t)(this))}composeTraversal(t){return this.compose(t)}composeFold(t){return this.asFold().compose(t)}composeSetter(t){return this.asSetter().compose(t)}composeOptional(t){return this.compose(t.asTraversal())}composeLens(t){return it(f(this,ot(f(t,X))))}composePrism(t){return it(f(this,ot(f(t,tt))))}composeIso(t){return it(f(this,ot(f(t,A))))}composeGetter(t){return this.asFold().compose(t.asFold())}}class gt{constructor(t){this.get=t,this._tag="Getter"}asFold(){return new vt((()=>t=>e=>t(this.get(e))))}compose(t){return new gt((e=>t.get(this.get(e))))}composeGetter(t){return this.compose(t)}composeFold(t){return this.asFold().compose(t)}composeLens(t){return this.compose(t.asGetter())}composeIso(t){return this.compose(t.asGetter())}composeTraversal(t){return this.asFold().compose(t.asFold())}composeOptional(t){return this.asFold().compose(t.asFold())}composePrism(t){return this.asFold().compose(t.asFold())}}class vt{constructor(t){this.foldMap=t,this._tag="Fold",this.getAll=t(n.JI())(n.of),this.exist=t(l.Hw),this.all=t(l.s2),this.foldMapFirst=t((0,p.te)())}compose(t){return new vt((e=>r=>this.foldMap(e)(t.foldMap(e)(r))))}composeFold(t){return this.compose(t)}composeGetter(t){return this.compose(t.asFold())}composeTraversal(t){return this.compose(t.asFold())}composeOptional(t){return this.compose(t.asFold())}composeLens(t){return this.compose(t.asFold())}composePrism(t){return this.compose(t.asFold())}composeIso(t){return this.compose(t.asFold())}find(t){return this.foldMapFirst((0,p.aN)(t))}headOption(t){return this.find((()=>!0))(t)}}class Ft{constructor(t){this.modify=t,this._tag="Setter"}set(t){return this.modify((0,o.dY)(t))}compose(t){return new Ft((e=>this.modify(t.modify(e))))}composeSetter(t){return this.compose(t)}composeTraversal(t){return this.compose(t.asSetter())}composeOptional(t){return this.compose(t.asSetter())}composeLens(t){return this.compose(t.asSetter())}composePrism(t){return this.compose(t.asSetter())}composeIso(t){return this.compose(t.asSetter())}}function yt(t){const e=nt(t);return()=>it(e())}}}]);
//# sourceMappingURL=1693.chunk.min.js.map