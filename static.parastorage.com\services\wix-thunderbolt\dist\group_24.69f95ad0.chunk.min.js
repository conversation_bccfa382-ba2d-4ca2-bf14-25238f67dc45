"use strict";(self.webpackJsonp__wix_thunderbolt_app=self.webpackJsonp__wix_thunderbolt_app||[]).push([[8253],{7185:(e,t,n)=>{n.r(t),n.d(t,{page:()=>O});var o=n(77748),r=n(20590),s=n(32166),c=n(87711),a=n(39218),d=n(10553),l=n(69578),i=n(43272),h=n(17709),p=n.n(h),g=n(62155),u=n(56232),m=n(91674);const v=e=>({compId:e.compId,dataId:e.dataId}),I=(e,t,n)=>{const{pageAnchorsObservers:o,activeAnchorObservers:r,anchors:s,pageId:c,siteOffset:a,headerCompId:d,reCheckAnchors:l}=e,i=(0,m.km)(d,t),h=i?.getBoundingClientRect().height;let I,b=h??a;const f=()=>{const e=t.document.getElementById(u.M4);e&&(b+=e.offsetHeight),I=s.reduce(((e,n)=>{const o=t.document.getElementById(n.compId);return o&&e.push({...n,element:o,top:o.getBoundingClientRect().top||0}),e}),[]),O()},A=e=>{const s=((e,t,n)=>{if(n.innerHeight+n.scrollY>=n.document.body.scrollHeight)return e[e.length-1];const o=e.findIndex((e=>Math.floor(e.top-t)>0));return-1===o&&e[e.length-1]?v(e[e.length-1]):0!==o&&e[o-1]?v(e[o-1]):u.aK})(I,b,t);s&&n.update(((e,t,n,o,r,s,c)=>{const a={};if(n.length&&n.forEach((e=>{a[e.id]={activeAnchor:o}})),t.length){const n=r?r.map((e=>({compId:e.compId,dataId:e.dataId,name:e.name}))):[];t.forEach((t=>{if(a[t.id]={activeAnchor:o},c){const{pageTopLabel:o,hiddenAnchorIds:r}=t.compData,c=r&&r.hasOwnProperty(s)?r[s]:[],d={compId:u.wq,dataId:u.jO,name:o||""},l=[d].concat(n).filter((t=>{const n=t.compId,o=e.document.getElementById(n),r=n===u.wq||Boolean(o&&"none"!==e.getComputedStyle(o).display),s=!c.includes(n);return r&&s}));a[t.id].anchors=l.length?l:[d]}}))}return a})(t,o,r,s,I,c,e))},O=()=>I.sort(((e,t)=>e.top-t.top)),C=e=>{l&&(!I||s.length>I.length)&&f(),I=I.map((e=>({...e,top:e.element.getBoundingClientRect().top||0}))),e&&O()},E=e=>{p().measure((()=>{C(e),A(e)}))},_=(0,g.debounce)(E.bind(null,l),u.cg),w=(0,g.debounce)(E.bind(null,!0),u.bS);return()=>(p().measure((()=>{f(),A(!0)})),t.addEventListener("scroll",_),t.addEventListener("resize",w),()=>{t.removeEventListener("scroll",_),t.removeEventListener("resize",w)})};var b=n(32740);const f=(0,o.Og)([(0,o.KT)(r.Gp,i.U),(0,o.KT)(r._K,i.U),s.RV,c.Ji,a.Is,d.n,(0,o.lq)(l.$$)],((e,t,n,o,r,s,c)=>{const a=c?.get(),d=a&&s["specs.thunderbolt.servicesInfra"]&&s["specs.thunderbolt.anchorService"],l=e.pageAnchorsObservers.concat(t.pageAnchorsObservers),i=e.activeAnchorObservers.concat(t.activeAnchorObservers),h=[...new Set([...e.anchors,...t.anchors]).values()],p=s["specs.thunderbolt.passHeaderCompIdToCalcActiveAnchorCorrectly"]?e.headerComponentId:"SITE_HEADER",g={pageAnchorsObservers:l,activeAnchorObservers:i,anchors:h,pageId:r,headerCompId:p,siteOffset:t.siteOffset,reCheckAnchors:!1},u="masterPage"!==r?I(g,n,o):()=>()=>{};let m;return{pageDidMount(){if(l.length||i.length)if("masterPage"===r)m=()=>{};else if(d){const e=a?.getService(b.AnchorsDefinition);m=e.observeAnchors(g)}else m=u()},pageWillUnmount(){m&&m()}}}));var A=n(16537);const O=e=>{e(A.$.PageDidMountHandler,A.$.PageWillUnmountHandler).to(f)}}}]);
//# sourceMappingURL=https://static.parastorage.com/services/wix-thunderbolt/dist/group_24.69f95ad0.chunk.min.js.map