"use strict";(self.webpackJsonp__wix_thunderbolt_app=self.webpackJsonp__wix_thunderbolt_app||[]).push([[569,1141,2646,3169,5294,5445],{8847:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.MappersLegacyDefinition=void 0;const r=i(24150);t.MappersLegacyDefinition=(0,r.defineService)("viewer-core/mappers-legacy")},41017:function(e,t,i){var r=this&&this.__createBinding||(Object.create?function(e,t,i,r){void 0===r&&(r=i);var n=Object.getOwnPropertyDescriptor(t,i);n&&!("get"in n?!t.__esModule:n.writable||n.configurable)||(n={enumerable:!0,get:function(){return t[i]}}),Object.defineProperty(e,r,n)}:function(e,t,i,r){void 0===r&&(r=i),e[r]=t[i]}),n=this&&this.__exportStar||function(e,t){for(var i in e)"default"===i||Object.prototype.hasOwnProperty.call(t,i)||r(t,e,i)};Object.defineProperty(t,"__esModule",{value:!0}),n(i(12960),t)},12960:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.implementStaticService=t.defineStaticService=void 0;const r=i(24150);t.defineStaticService=r.defineService,t.implementStaticService=r.implementService},38195:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.BusinessLoggerDefinition=void 0;const r=i(41017);t.BusinessLoggerDefinition=(0,r.defineStaticService)("viewer-core/business-logger")},63035:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ConsentPolicyDefinition=void 0;const r=i(41017);t.ConsentPolicyDefinition=(0,r.defineStaticService)("viewer-core/viewer-service-consent-policy")},73955:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.EnvironmentDefinition=void 0;const r=i(41017);t.EnvironmentDefinition=(0,r.defineStaticService)("viewer-core/viewer-service-environment")},26778:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.FedopsLoggerDefinition=void 0;const r=i(52575);t.FedopsLoggerDefinition=(0,r.defineStaticService)("viewer-core/fedops-logger")},52575:function(e,t,i){var r=this&&this.__createBinding||(Object.create?function(e,t,i,r){void 0===r&&(r=i);var n=Object.getOwnPropertyDescriptor(t,i);n&&!("get"in n?!t.__esModule:n.writable||n.configurable)||(n={enumerable:!0,get:function(){return t[i]}}),Object.defineProperty(e,r,n)}:function(e,t,i,r){void 0===r&&(r=i),e[r]=t[i]}),n=this&&this.__exportStar||function(e,t){for(var i in e)"default"===i||Object.prototype.hasOwnProperty.call(t,i)||r(t,e,i)};Object.defineProperty(t,"__esModule",{value:!0}),n(i(97786),t)},97786:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.implementStaticService=t.defineStaticService=void 0;const r=i(96406);t.defineStaticService=r.defineService,t.implementStaticService=r.implementService},37741:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.InteractionsDefinition=void 0;const r=i(44848);t.InteractionsDefinition=(0,r.defineStaticService)("viewer-core/interactions")},44848:function(e,t,i){var r=this&&this.__createBinding||(Object.create?function(e,t,i,r){void 0===r&&(r=i);var n=Object.getOwnPropertyDescriptor(t,i);n&&!("get"in n?!t.__esModule:n.writable||n.configurable)||(n={enumerable:!0,get:function(){return t[i]}}),Object.defineProperty(e,r,n)}:function(e,t,i,r){void 0===r&&(r=i),e[r]=t[i]}),n=this&&this.__exportStar||function(e,t){for(var i in e)"default"===i||Object.prototype.hasOwnProperty.call(t,i)||r(t,e,i)};Object.defineProperty(t,"__esModule",{value:!0}),n(i(54729),t)},54729:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.implementStaticService=t.defineStaticService=void 0;const r=i(96406);t.defineStaticService=r.defineService,t.implementStaticService=r.implementService},56844:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.LinkUtilsDefinition=void 0;const r=i(16665);t.LinkUtilsDefinition=(0,r.defineStaticService)("viewer-core/viewer-service-link-utils")},16665:function(e,t,i){var r=this&&this.__createBinding||(Object.create?function(e,t,i,r){void 0===r&&(r=i);var n=Object.getOwnPropertyDescriptor(t,i);n&&!("get"in n?!t.__esModule:n.writable||n.configurable)||(n={enumerable:!0,get:function(){return t[i]}}),Object.defineProperty(e,r,n)}:function(e,t,i,r){void 0===r&&(r=i),e[r]=t[i]}),n=this&&this.__exportStar||function(e,t){for(var i in e)"default"===i||Object.prototype.hasOwnProperty.call(t,i)||r(t,e,i)};Object.defineProperty(t,"__esModule",{value:!0}),n(i(82816),t)},82816:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.implementStaticService=t.defineStaticService=void 0;const r=i(96406);t.defineStaticService=r.defineService,t.implementStaticService=r.implementService},64981:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PageContextDefinition=void 0;const r=i(41017);t.PageContextDefinition=(0,r.defineStaticService)("viewer-core/viewer-service-page-context")},32168:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SessionManagerDefinition=void 0;const r=i(35437);t.SessionManagerDefinition=(0,r.defineStaticService)("viewer-core/viewer-service-session-manager")},35437:function(e,t,i){var r=this&&this.__createBinding||(Object.create?function(e,t,i,r){void 0===r&&(r=i);var n=Object.getOwnPropertyDescriptor(t,i);n&&!("get"in n?!t.__esModule:n.writable||n.configurable)||(n={enumerable:!0,get:function(){return t[i]}}),Object.defineProperty(e,r,n)}:function(e,t,i,r){void 0===r&&(r=i),e[r]=t[i]}),n=this&&this.__exportStar||function(e,t){for(var i in e)"default"===i||Object.prototype.hasOwnProperty.call(t,i)||r(t,e,i)};Object.defineProperty(t,"__esModule",{value:!0}),n(i(76052),t)},76052:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.implementStaticService=t.defineStaticService=void 0;const r=i(96406);t.defineStaticService=r.defineService,t.implementStaticService=r.implementService},61839:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SiteThemeDefinition=void 0;const r=i(24150);t.SiteThemeDefinition=(0,r.defineService)("viewer-core/viewer-service-site-theme")},57659:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.StyleUtilsDefinition=void 0;const r=i(10318);t.StyleUtilsDefinition=(0,r.defineStaticService)("viewer-core/viewer-service-style-utils")},10318:function(e,t,i){var r=this&&this.__createBinding||(Object.create?function(e,t,i,r){void 0===r&&(r=i);var n=Object.getOwnPropertyDescriptor(t,i);n&&!("get"in n?!t.__esModule:n.writable||n.configurable)||(n={enumerable:!0,get:function(){return t[i]}}),Object.defineProperty(e,r,n)}:function(e,t,i,r){void 0===r&&(r=i),e[r]=t[i]}),n=this&&this.__exportStar||function(e,t){for(var i in e)"default"===i||Object.prototype.hasOwnProperty.call(t,i)||r(t,e,i)};Object.defineProperty(t,"__esModule",{value:!0}),n(i(81923),t)},81923:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.implementStaticService=t.defineStaticService=void 0;const r=i(96406);t.defineStaticService=r.defineService,t.implementStaticService=r.implementService},35745:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TopologyDefinition=void 0;const r=i(41017);t.TopologyDefinition=(0,r.defineStaticService)("viewer-core/viewer-service-topology")},19076:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TranslationsDefinition=void 0;const r=i(24150);t.TranslationsDefinition=(0,r.defineService)("viewer-core/translations")},55363:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.UrlDefinition=void 0;const r=i(24150);t.UrlDefinition=(0,r.defineService)("viewer-core/viewer-service-url")},62360:(e,t,i)=>{t.h=void 0;const r=i(96406);t.h=(0,r.defineService)("viewer-core/viewer-service-wix-client")},33615:(e,t,i)=>{i.r(t),i.d(t,{ProtectedPagesApiSymbol:()=>y,page:()=>O,site:()=>w});var r,n=i(77748),o=i(20590),a=i(54563);!function(e){e.SM="SM",e.Pass="PASS",e.NONE="NONE"}(r||(r={}));const c=(0,n.Og)([(0,n.KT)(o._K,a.U),(0,n.KT)(o.YG,a.U),(0,n.KT)(o.wk,a.U)],((e,t,i)=>{const n=i=>{const n=e.pagesSecurity[i];return n&&n.requireLogin?r.SM:t.passwordProtected[i]||n&&n.passwordDigest?r.Pass:r.NONE};return{async handle(t){if(t.pageJsonFileName)return t;const o=t.pageId,{pagesMap:a,loginAndNavigate:c,authenticateUsingSitePassword:s,completedSitePasswordAuth:d}=i.get();if(a[o])return{...t,pageJsonFileName:a[o]};if(n("masterPage")===r.Pass&&!d)return s(t,r.Pass),null;const p=n(o);return p===r.NONE&&((e,t)=>{throw new Error(`we do not have authentication info for protected page ${e} page security: ${t}`)})(o,e.pagesSecurity[o]),c(t,p),null}}}));var s=i(71085),d=i(66397),p=i(19889),l=i(91994),u=i(12457),f=i(75396),v=i(32166),g=i(10553);const S=(0,n.Og)([(0,n.KT)(o.wk,a.U),(0,n.KT)(o.YG,a.U),(0,n.KT)(o._K,a.U),s.Ix,f.t7,(0,n.lq)(p.Np),(0,n.lq)(l.De),u.Tf,v.Ht,g.n],((e,{publicPageIds:t,pageUriSeoToRouterPrefix:i},{customNoPermissionsPageUriSeo:n,customNoPermissionsPageId:o},a,c,s,p,l,u,f)=>{const v=async(e,t)=>e===r.SM?(async()=>{let e;if(s)if(f["specs.thunderbolt.newAuthorizedPagesFlow"]){if(e=await s.requestAuthorizedPages(),e.success)return{authorizedPagesMap:e.pages,onProtectedPageNavigationComplete:async()=>{window.document.title=await l.getPageTitle()}}}else if(e=await s.requestAuthentication({}),e.success)return{authorizedPagesMap:await s.authorizeMemberPagesByToken(e.token)};return{authorizedPagesMap:{},authenticationResult:e}})():(async e=>p?p.promptPagePasswordDialog(e):{authorizedPagesMap:{}})(t),g={[r.Pass]:"password-protected",[r.SM]:"protected",[r.NONE]:void 0},S=async(e,t)=>a.navigate(e,{biData:{pageType:g[t]}}),P=async(e,t,r=!1)=>{if(n&&r){const r=`${i[n]?`./${i[n]}/`:"./"}${n}`,o=encodeURIComponent(JSON.stringify({restrictedPageId:e.pageId,restrictedPagePath:(e.relativeEncodedUrl??"").replace("./","/")}));return S(`${r}?appSectionParams=${o}`,t)}if(s&&"SM"===t){const e=()=>{c.isLandingOnProtectedPage()&&S("./",t)};return s.showNoPermissionsToPageDialog(e),!1}return!!c.isLandingOnProtectedPage()&&S("./",t)};async function m(t,i){if(!p)return;const{authorizedPagesMap:r,onComplete:n}=await p.promptSitePasswordDialog();e.update((e=>({...e,pagesMap:Object.assign(e.pagesMap,r),completedSitePasswordAuth:!0}))),await S(t.parsedUrl.href,i),n?.()}const _=async(i,r)=>{const n=i.pageId;try{const{authorizedPagesMap:a,authenticationResult:s,onProtectedPageNavigationComplete:p}=await v(r,n);if(e.update((e=>({...e,pagesMap:Object.assign(e.pagesMap,a)}))),s?.reason===d.qd.CANCELED)return!!c.isLandingOnProtectedPage()&&S("./",r);if(a[n]){const e=await S(i.parsedUrl.href,r);return p?.(),e}{const n=t.includes(o),a=Boolean(e.get().pagesMap[o]);return P(i,r,n||a)}}catch(e){return u.captureError(e,{tags:{feature:"protectedPage"}}),P(i,r)}};return e.update((()=>({loginAndNavigate:_,authenticateUsingSitePassword:m,completedSitePasswordAuth:!1,pagesMap:{}}))),{appWillMount:async()=>{}}}));var P=i(16537),m=i(60950);const _=(0,n.Og)([(0,n.KT)(o.wk,a.U),p.Np],((e,t)=>({getTpaHandlers:()=>({authorizeMemberPages:async()=>{const i=await t.authorizeMemberPagesByCookie();e.update((e=>({...e,pagesMap:{...e.pagesMap,...i}})))}})})));const b=(0,n.Og)([(0,n.KT)(o.wk,a.U)],(function(e){return{getPageJsonFileName:t=>e.get()?.pagesMap[t]??null,getProtectedPages:()=>({...e.get()?.pagesMap??{}})}})),y=Symbol("ProtectedPagesApiSymbol"),w=e=>{e(s.po.Protected).to(c),e(P.$.AppWillMountHandler).to(S),e(y).to(b)},O=e=>{e(m.dQ).to(_)}},11682:(e,t,i)=>{i.r(t),i.d(t,{platformWorkerPromise:()=>d});const{viewerModel:{siteAssets:{clientTopology:r},siteFeatures:n,siteFeaturesConfigs:{platform:o},site:{externalBaseUrl:a}},usedPlatformApis:c}=window,s=Worker&&n.includes("platform"),d=s?(async()=>{const e="platform_create-worker started";performance.mark(e);const{clientWorkerUrl:t,appsScripts:i,bootstrapData:n,sdksStaticPaths:s}=o,{appsSpecData:d,appDefIdToIsMigratedToGetPlatformApi:p,forceEmptySdks:l}=n,u=t.startsWith("http://localhost:")||t.startsWith("https://bo.wix.com/suricate/")||document.baseURI!==location.href?(e=>{const t=new Blob([`importScripts('${e}');`],{type:"application/javascript"});return URL.createObjectURL(t)})(t):t.replace(r.fileRepoUrl,`${a}/_partials`),f=new Worker(u),v=Object.keys(i.urls).filter((e=>!d[e]?.isModuleFederated)).reduce(((e,t)=>(e[t]=i.urls[t],e)),{});if(s&&s.mainSdks&&s.nonMainSdks){Object.values(p).every((e=>e))||l?f.postMessage({type:"preloadNamespaces",namespaces:c}):f.postMessage({type:"preloadAllNamespaces",sdksStaticPaths:s})}f.postMessage({type:"platformScriptsToPreload",appScriptsUrls:v});const g="platform_create-worker ended";return performance.mark(g),performance.measure("Create Platform Web Worker",e,g),f})():Promise.resolve()},69506:(e,t,i)=>{i.d(t,{B:()=>r});const r=(0,i(16473).P)("NamedSignals")},41039:(e,t,i)=>{i.d(t,{v:()=>r});const r=(0,i(16473).P)("experiment-service")},24150:(e,t)=>{function i(e,t){return t}Object.defineProperty(t,"__esModule",{value:!0}),t.defineService=function(e){return e},t.implementService=i,i.withConfig=function(){return function(e,t){return t}}},96406:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.implementService=t.defineService=void 0;var r=i(24150);Object.defineProperty(t,"defineService",{enumerable:!0,get:function(){return r.defineService}}),Object.defineProperty(t,"implementService",{enumerable:!0,get:function(){return r.implementService}})},16473:(e,t,i)=>{function r(e){return e}function n(e,t){return t}i.d(t,{P:()=>r,n:()=>n}),n.withConfig=function(){return function(e,t){return t}}}}]);
//# sourceMappingURL=https://static.parastorage.com/services/wix-thunderbolt/dist/platform.dbb95e9a.chunk.min.js.map