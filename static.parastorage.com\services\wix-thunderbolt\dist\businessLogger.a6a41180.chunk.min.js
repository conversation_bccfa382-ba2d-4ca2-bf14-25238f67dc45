"use strict";(self.webpackJsonp__wix_thunderbolt_app=self.webpackJsonp__wix_thunderbolt_app||[]).push([[4937],{17955:(e,t,i)=>{i.d(t,{t:()=>o});var n=i(46883);const o=e=>!(null!=e&&e.policy)||!1!==e.policy[n.$.Analytics]&&!1!==e.policy[n.$.Functional]},41108:(e,t,i)=>{i.d(t,{Af:()=>n,b6:()=>r,ho:()=>c,o:()=>s,v8:()=>o});const n=function(e){for(var t=arguments.length,i=new Array(t>1?t-1:0),n=1;n<t;n++)i[n-1]=arguments[n];return i.forEach((t=>{if(!e||"function"!=typeof e[t])throw new Error(`bsiManager: ${String(t)} must be a function`)}))},o=e=>{const t=RegExp("^\\s*"+e+"=\\s*(.*?)\\s*$");let i=[];try{i=window.document.cookie.split(";")}catch(e){}for(const e of i){const i=e.match(t);if(i)return i[1]}return null},s=(e,t,i,n,o)=>{const s=`${e}=${t}; expires=${new Date(i).toUTCString()}; path=${n}; domain=${o}; SameSite=None; Secure`;try{window.document.cookie=s}catch(e){}},r=(e,t,i)=>{s(e,null,0,t,i)},c=()=>new Date(Date.now()+864e5).setHours(0,0,0,0)},1642:(e,t,i)=>{i.d(t,{J:()=>a});var n=i(49432),o=i(81085),s=i(48541),r=i(41108),c=i(46883),l=i(97224);class a{constructor(e){this.options=e,(0,n.A)(this,"api",void 0),(0,n.A)(this,"cookie",void 0),(0,n.A)(this,"config",void 0),(0,n.A)(this,"consentPolicyChangedListener",void 0),(0,n.A)(this,"initialized",!1),(0,n.A)(this,"session",void 0),(0,n.A)(this,"ttlTimeout",void 0)}static create(e,t){return new a(t).init(e)}isCachedSessionValid(e,t){const i=this.config.getPolicy();return Date.now()<e.cacheExpires&&e.pageNumber===t&&[c.$.Analytics,c.$.Functional].every((t=>{var n,o;return(null==(n=e.policy)?void 0:n[t])===(null==i||null==(o=i.policy)?void 0:o[t])}))}createSessionObject(e,t,i){var n,o;void 0===t&&(t=null);const[s=this.api.generateGuid(),r="1"]=t?t.split("|"):[],{baseGlobalPageNumber:c=parseInt(r,10),baseSessionPageNumber:a=e}=i||{};return{bsi:s,pageNumber:e,baseGlobalPageNumber:c,baseSessionPageNumber:a,bsiString:`${s}|${c+e-a}`,policy:{...null==(n=this.config.getPolicy())?void 0:n.policy},cacheExpires:Date.now()+((null==(o=this.options)?void 0:o.cacheTtl)??l.gX)}}extend(e){const{bsiString:t,pageNumber:i}=e,{bsi:n,ttl:o}=this.cookie.set(t);return this.config.set(n),this.session={...e,bsiString:n},clearTimeout(this.ttlTimeout),o&&(this.ttlTimeout=setTimeout((()=>this.extend(this.createSessionObject(i))),o)),n}createOrExtendSession(e){void 0===e&&(e=1);const t=this.cookie.get()||this.config.get(),i=t?this.createSessionObject(e,t,this.session):this.createSessionObject(e);return this.extend(i)}init(e){var t;return(0,r.Af)(e,"generateGuid","getCommonConfig"),this.api=e,this.config=new s.w(this.api.getCommonConfig,this.api.getConsentPolicy),this.cookie=new o.W(this.config,this.api.getCookieReaderWriter,{cookieDomain:null==(t=this.options)?void 0:t.cookieDomain}),this.createOrExtendSession(),globalThis.consentPolicyManager&&"function"==typeof globalThis.addEventListener&&(this.consentPolicyChangedListener=()=>{this.createOrExtendSession()},globalThis.addEventListener("consentPolicyChanged",this.consentPolicyChangedListener)),this.initialized=!0,this}destroy(){clearTimeout(this.ttlTimeout),this.consentPolicyChangedListener&&globalThis.removeEventListener("consentPolicyChanged",this.consentPolicyChangedListener)}getBsi(e,t){var i;void 0===e&&(e=1);let{extend:n}=void 0===t?{extend:!0}:t;if(!this.initialized)throw new Error("bsiManager: please call init() first");return n?this.session&&this.isCachedSessionValid(this.session,e)?this.session.bsiString:this.createOrExtendSession(e):(null==(i=this.session)?void 0:i.bsiString)??null}}},48541:(e,t,i)=>{i.d(t,{w:()=>s});var n=i(49432),o=i(17955);class s{constructor(e,t){this.getCommonConfig=e,this.getConsentPolicy=t,(0,n.A)(this,"bsi",null)}checkPolicyAndDestroyIfNeeded(){return!!(0,o.t)(this.getPolicy())||(this.call("get","bsi")&&this.destroy(),!1)}call(e){const t=this.getCommonConfig();for(var i=arguments.length,n=new Array(i>1?i-1:0),o=1;o<i;o++)n[o-1]=arguments[o];return t&&"function"==typeof t[e]&&t[e](...n)}getPolicy(){const e=this.call("get","consentPolicy");return e?{policy:e}:"function"==typeof this.getConsentPolicy?this.getConsentPolicy():null}get(){return this.checkPolicyAndDestroyIfNeeded()&&this.call("get","bsi")||null}set(e){return this.checkPolicyAndDestroyIfNeeded()?(e!==this.call("get","bsi")&&(this.bsi=e,this.call("set","bsi",e)),e):null}subscribe(e){return this.call("subscribe",(()=>{const t=this.call("get","bsi");t!==this.bsi&&(this.bsi=t,e(t))})),this}destroy(){this.bsi=null,this.call("set","bsi",null)}}},81085:(e,t,i)=>{i.d(t,{W:()=>l});var n=i(97224),o=i(17955),s=i(41108);let r;const c=()=>{if(!r){const e=n.Ni.reduce(((e,t)=>e||-1===window.document.location.hostname.indexOf(t)?e:t),null);r=e||window.document.location.hostname}return r};class l{constructor(e,t,i){void 0===t&&(t=()=>({read:s.v8,write:s.o,destroy:s.b6})),void 0===i&&(i={}),this.configStorage=e,this.getCookieReaderWriter=t,this.options=i}checkPolicyAndDestroyIfNeeded(){return!!(0,o.t)(this.configStorage.getPolicy())||(this.destroy(),!1)}get(){return this.checkPolicyAndDestroyIfNeeded()?this.getCookieReaderWriter().read(n.z4):null}set(e){if(!this.checkPolicyAndDestroyIfNeeded())return{bsi:null,ttl:null};const t=Date.now(),i=Math.min(t+n.M5,(0,s.ho)()),o=this.options.cookieDomain??c();return this.getCookieReaderWriter().write(n.z4,`${e}`,i,"/",o),{bsi:e,ttl:i-t}}destroy(){this.getCookieReaderWriter().destroy(n.z4,"/",this.options.cookieDomain??c());try{window.localStorage.removeItem("beatSessionTs"),window.localStorage.removeItem("beatSessionId")}catch(e){}}}},46883:(e,t,i)=>{i.d(t,{$:()=>n});let n=function(e){return e.Functional="functional",e.Analytics="analytics",e}({})},41017:function(e,t,i){var n=this&&this.__createBinding||(Object.create?function(e,t,i,n){void 0===n&&(n=i);var o=Object.getOwnPropertyDescriptor(t,i);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[i]}}),Object.defineProperty(e,n,o)}:function(e,t,i,n){void 0===n&&(n=i),e[n]=t[i]}),o=this&&this.__exportStar||function(e,t){for(var i in e)"default"===i||Object.prototype.hasOwnProperty.call(t,i)||n(t,e,i)};Object.defineProperty(t,"__esModule",{value:!0}),o(i(12960),t)},12960:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.implementStaticService=t.defineStaticService=void 0;const n=i(24150);t.defineStaticService=n.defineService,t.implementStaticService=n.implementService},38195:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.BusinessLoggerDefinition=void 0;const n=i(41017);t.BusinessLoggerDefinition=(0,n.defineStaticService)("viewer-core/business-logger")},24150:(e,t)=>{function i(e,t){return t}Object.defineProperty(t,"__esModule",{value:!0}),t.defineService=function(e){return e},t.implementService=i,i.withConfig=function(){return function(e,t){return t}}}}]);
//# sourceMappingURL=https://static.parastorage.com/services/wix-thunderbolt/dist/businessLogger.a6a41180.chunk.min.js.map